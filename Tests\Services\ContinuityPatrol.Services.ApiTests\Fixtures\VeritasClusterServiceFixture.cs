﻿using AutoFixture;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using Moq;

public class VeritasClusterServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public VeritasClusterService Service { get; }

    public CreateVeritasClusterCommand CreateCommand { get; }
    public UpdateVeritasClusterCommand UpdateCommand { get; }
    public GetVeritasClusterPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public VeritasClusterDetailVm DetailVm { get; }
    public List<VeritasClusterListVm> ClusterList { get; }
    public PaginatedResult<VeritasClusterListVm> PaginatedResult { get; }

    private readonly Fixture _fixture;

    public VeritasClusterServiceFixture()
    {
        _fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new VeritasClusterService(ClientMock.Object);

        CreateCommand = _fixture.Create<CreateVeritasClusterCommand>();
        UpdateCommand = _fixture.Create<UpdateVeritasClusterCommand>();
        PaginatedQuery = _fixture.Create<GetVeritasClusterPaginatedListQuery>();

        BaseResponse = _fixture.Create<BaseResponse>();
        DetailVm = _fixture.Create<VeritasClusterDetailVm>();
        ClusterList = _fixture.Create<List<VeritasClusterListVm>>();
        PaginatedResult = _fixture.Create<PaginatedResult<VeritasClusterListVm>>();
    }
}