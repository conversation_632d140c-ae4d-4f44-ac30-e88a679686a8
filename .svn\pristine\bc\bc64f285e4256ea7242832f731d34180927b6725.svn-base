﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class VeritasClusterServiceTests : IClassFixture<VeritasClusterServiceFixture>
{
    private readonly VeritasClusterServiceFixture _fixture;

    public VeritasClusterServiceTests(VeritasClusterServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        string id = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        string id = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Get<VeritasClusterDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsVeritasClusterNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsVeritasClusterNameExist("TestName", "id-123");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedVeritasClusters_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<VeritasClusterListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedVeritasClusters(_fixture.PaginatedQuery);

        Assert.NotNull(result);
        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetVeritasClusterList_Should_Return_ClusterList()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<VeritasClusterListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.ClusterList);

        var result = await _fixture.Service.GetVeritasClusterList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.ClusterList, result);
    }
}
