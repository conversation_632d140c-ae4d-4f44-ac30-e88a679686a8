using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowProfileInfoViewRepositoryTests : IClassFixture<WorkflowProfileInfoViewFixture>
    {
        private readonly WorkflowProfileInfoViewFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowProfileInfoViewRepository _repoParent;
        private readonly WorkflowProfileInfoViewRepository _repoNotParent;
        private readonly WorkFlowRepository _workFlowRepository;

        public WorkflowProfileInfoViewRepositoryTests(WorkflowProfileInfoViewFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _workFlowRepository= new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoParent = new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockUserService(), _workFlowRepository);
            _repoNotParent = new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _workFlowRepository);
           
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            var ids = _fixture.WorkflowProfileInfoViewList.Select(x => x.ProfileId).ToList();
            var result = await _repoParent.GetWorkflowProfileInfoByProfileIds(ids);

            Assert.All(result, x => Assert.Contains(x.ProfileId, ids));
        }

        [Fact]
        public async Task IsRunningWorkflow_ReturnsTuple_WhenIsParent()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            entity.IsRunning = true;
            entity.ProgressBar = "50%";
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsRunningWorkflow(entity.WorkflowId);

            Assert.True(result.IsRunning);
            Assert.Equal("50%", result.ProgressBar);
        }

        [Fact]
        public async Task IsRunningWorkflow_ReturnsFalse_WhenNotFound()
        {
            var result = await _repoParent.IsRunningWorkflow("non-existent");

            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task GetRunningProfileByProfileIds_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            entity.IsRunning = true;
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.ProfileId };
            var result = await _repoParent.GetRunningProfileByProfileIds(ids);

            Assert.All(result, x => Assert.True(x.IsRunning));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.WorkflowId };
            var result = await _repoParent.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(entity.ProfileId, ids);

            Assert.All(result, x => Assert.Equal(entity.ProfileId, x.ProfileId));
            Assert.All(result, x => Assert.Contains(x.WorkflowId, ids));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ReturnsNames()
        {
            //_mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoNames();

            Assert.All(result, x => Assert.NotNull(x.ProfileName));
        }

        #region Parent/Child Company Logic Tests

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ShouldReturnAllProfiles_WhenIsParent()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList;
            profiles[0].CompanyId = "PARENT_COMPANY";
            profiles[1].CompanyId = "ChHILD_COMPANY_123";

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var profileIds = profiles.Select(x => x.ProfileId).ToList();

            // Act
            var result = await _repoParent.GetWorkflowProfileInfoByProfileIds(profileIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count); // Parent can see all profiles
            Assert.Contains(result, x => x.CompanyId == "PARENT_COMPANY");
            Assert.Contains(result, x => x.CompanyId == "ChHILD_COMPANY_123");
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ShouldReturnFilteredProfiles_WhenIsNotParent()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList;
            profiles[0].CompanyId = "PARENT_COMPANY";
            profiles[1].CompanyId = "ChHILD_COMPANY_123";
            profiles[2].CompanyId = "ChHILD_COMPANY_123";

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var profileIds = profiles.Select(x => x.ProfileId).ToList();

            // Act
            var result = await _repoNotParent.GetWorkflowProfileInfoByProfileIds(profileIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Child can only see their company profiles
            Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
            Assert.DoesNotContain(result, x => x.CompanyId == "PARENT_COMPANY");
        }

        [Fact]
        public async Task IsRunningWorkflow_ShouldReturnRunningWorkflow_WhenIsParent()
        {
            // Arrange
            var profile = _fixture.WorkflowProfileInfoViewDto;
            profile.IsRunning = true;
            profile.ProgressBar = "75%";
            profile.CompanyId = "ANY_COMPANY";

            await _dbContext.WorkflowProfileInfoViews.AddAsync(profile);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.IsRunningWorkflow(profile.WorkflowId);

            // Assert
            Assert.True(result.IsRunning);
            Assert.Equal("75%", result.ProgressBar);
        }

        [Fact]
        public async Task IsRunningWorkflow_ShouldReturnRunningWorkflow_WhenIsNotParentAndSameCompany()
        {
            // Arrange
            var profile = _fixture.WorkflowProfileInfoViewDto;
            profile.IsRunning = true;
            profile.ProgressBar = "60%";
            profile.CompanyId = "ChHILD_COMPANY_123";

            await _dbContext.WorkflowProfileInfoViews.AddAsync(profile);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoNotParent.IsRunningWorkflow(profile.WorkflowId);

            // Assert
            Assert.True(result.IsRunning);
            Assert.Equal("60%", result.ProgressBar);
        }

        [Fact]
        public async Task IsRunningWorkflow_ShouldReturnFalse_WhenIsNotParentAndDifferentCompany()
        {
            // Arrange
            var profile = _fixture.WorkflowProfileInfoViewDto;
            profile.IsRunning = true;
            profile.ProgressBar = "80%";
            profile.CompanyId = "OTHER_COMPANY_456";

            await _dbContext.WorkflowProfileInfoViews.AddAsync(profile);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoNotParent.IsRunningWorkflow(profile.WorkflowId);

            // Assert
            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task GetRunningProfileByProfileIds_ShouldFilterByCompany_WhenIsNotParent()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList;
            profiles[0].IsRunning = true;
            profiles[0].CompanyId = "ChHILD_COMPANY_123"; // Should be included
            profiles[1].IsRunning = true;
            profiles[1].CompanyId = "OTHER_COMPANY_456"; // Should be excluded
            profiles[2].IsRunning = true;
            profiles[2].CompanyId = "ChHILD_COMPANY_123"; // Should be included

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var profileIds = profiles.Select(x => x.ProfileId).ToList();

            // Act
            var result = await _repoNotParent.GetRunningProfileByProfileIds(profileIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, x => Assert.True(x.IsRunning));
            Assert.All(result, x => Assert.NotNull(x.ProfileId));
            Assert.All(result, x => Assert.NotNull(x.ProgressBar));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ShouldFilterByCompany_WhenIsNotParent()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList;
            var targetProfileId = profiles[0].ProfileId;

            profiles[0].ProfileId = targetProfileId;
            profiles[0].CompanyId = "ChHILD_COMPANY_123"; // Should be included
            profiles[1].ProfileId = targetProfileId;
            profiles[1].CompanyId = "OTHER_COMPANY_456"; // Should be excluded
           

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var workflowIds = profiles.Select(x => x.WorkflowId).ToList();

            // Act
            var result = await _repoNotParent.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(targetProfileId, workflowIds);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(targetProfileId, result.First().ProfileId);
        }

        #endregion

        #region Edge Cases and Error Handling Tests

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ShouldHandleEmptyProfileIds()
        {
            // Arrange
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.GetWorkflowProfileInfoByProfileIds(new List<string>());

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }



        [Fact]
        public async Task IsRunningWorkflow_ShouldHandleNullWorkflowId()
        {
            // Arrange
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.IsRunningWorkflow(null);

            // Assert
            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task IsRunningWorkflow_ShouldHandleEmptyWorkflowId()
        {
            // Arrange
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.IsRunningWorkflow("");

            // Assert
            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task GetRunningProfileByProfileIds_ShouldReturnEmpty_WhenNoRunningProfiles()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList;
            profiles.ForEach(x => x.IsRunning = false); // No running profiles

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var profileIds = profiles.Select(x => x.ProfileId).ToList();

            // Act
            var result = await _repoParent.GetRunningProfileByProfileIds(profileIds);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ShouldHandleNullProfileId()
        {
            // Arrange
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            var workflowIds = _fixture.WorkflowProfileInfoViewList.Select(x => x.WorkflowId).ToList();

            // Act
            var result = await _repoParent.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(null, workflowIds);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ShouldHandleEmptyWorkflowIds()
        {
            // Arrange
            var profile = _fixture.WorkflowProfileInfoViewDto;
            await _dbContext.WorkflowProfileInfoViews.AddAsync(profile);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(profile.ProfileId, new List<string>());

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task IsRunningWorkflow_ShouldReturnFalse_WhenWorkflowNotRunning()
        {
            // Arrange
            var profile = _fixture.WorkflowProfileInfoViewDto;
            profile.IsRunning = false; // Not running
            profile.ProgressBar = "100%";

            await _dbContext.WorkflowProfileInfoViews.AddAsync(profile);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _repoParent.IsRunningWorkflow(profile.WorkflowId);

            // Assert
            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldReturnDistinctProfiles()
        {
            // Arrange
            var profiles = _fixture.WorkflowProfileInfoViewList.Take(3).ToList();

            // Add duplicate profile with same ReferenceId
            var duplicateProfile = new Domain.Views.WorkflowProfileInfoView
            {
                ReferenceId = profiles[0].ReferenceId, // Same reference ID
                ProfileName = "Duplicate Profile",
                ProfileId = "DUPLICATE_PROFILE_ID",
                BusinessServiceId = "DUPLICATE_SERVICE_ID",
                CompanyId = "ChHILD_COMPANY_123"
            };
            profiles.Add(duplicateProfile);

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(profiles);
            await _dbContext.SaveChangesAsync();

            var _mockWorkflowRepo = new Mock<IWorkflowRepository>();
            _mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string> { "DUPLICATE_PROFILE_ID" });

            var repository=new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockUserService(), _mockWorkflowRepo.Object);
            // Act
            var result = await repository.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.All(result, x => Assert.NotNull(x.ProfileName));
            Assert.All(result, x => Assert.NotNull(x.ReferenceId));

            // Should have distinct ReferenceIds
            var distinctReferenceIds = result.Select(x => x.ReferenceId).Distinct().Count();
            Assert.Equal(distinctReferenceIds, result.Count);
        }
        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldIncludePermissionProfiles_WhenUserIsAllInfra()
        {
            // Arrange
           

            var allProfiles = _fixture.WorkflowProfileInfoViewList;
            var permissionOnlyProfile = _fixture.WorkflowProfileInfoViewDto;

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(allProfiles);
            await _dbContext.WorkflowProfileInfoViews.AddAsync(permissionOnlyProfile);
            await _dbContext.SaveChangesAsync();

            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string> { permissionOnlyProfile .ReferenceId});

            var repository = new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockUserService(), mockWorkflowRepo.Object);

            // Act
            var result = await repository.GetWorkflowProfileInfoNames();

            // Assert
            Assert.Contains(result, x => x.ReferenceId == permissionOnlyProfile.ReferenceId);
            Assert.All(result, x => Assert.False(string.IsNullOrWhiteSpace(x.ProfileName)));
        }
       

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldUseAssignedInfra_WhenUserIsNotAllInfra()
        {
            // Arrange
            var mockUserService = new Mock<ILoggedInUserService>();
            mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false); 
            mockUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(DbContextFactory.GetAssignedEntityForIsAllinfraFalse()));

            var allProfiles = _fixture.WorkflowProfileInfoViewList;
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(allProfiles);
            await _dbContext.SaveChangesAsync();

            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());

            var repo = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repo.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotEmpty(result);
            Assert.All(result, x => Assert.NotNull(x.ProfileName));
        }
       

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldReturnEmptyList_WhenNoDataAndNoPermissions()
        {
            // Arrange
            var mockUserService = new Mock<ILoggedInUserService>();
            mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockUserService.Setup(x => x.IsAllInfra).Returns(true);

            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());

            var repo = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repo.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region Uncovered Methods Tests - 100% Coverage

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldHandleAssignedInfraObjects_WhenIsAllInfraFalse()
        {
            // Arrange
            await ClearDatabase();
            var infraObject = CreateInfraObject();
            infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
            await _dbContext.InfraObjects.AddAsync(infraObject);
            await _dbContext.SaveChangesAsync();

            var workflowProfileInfoViews = new List<WorkflowProfileInfoView>
            {
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = infraObject.ReferenceId, // Assigned infra object
                    ProfileName = "Assigned Profile",
                    ProfileId = "PROF_ASSIGNED",
                    BusinessServiceId = "BS_ASSIGNED",
                    CompanyId = "ChHILD_COMPANY_123"
                },
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = "UNASSIGNED_INFRA_ID", // Non-assigned infra object
                    ProfileName = "Unassigned Profile",
                    ProfileId = "PROF_UNASSIGNED",
                    BusinessServiceId = "BS_UNASSIGNED",
                    CompanyId = "ChHILD_COMPANY_123"
                }
            };

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(workflowProfileInfoViews);
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string>());

            // Create repository with IsAllInfra = false
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryNotAllInfra = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repositoryNotAllInfra.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Only assigned infra object should be returned
            Assert.Equal("PROF_ASSIGNED", result.First().ProfileId);
        }

        [Fact]
        public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedBusinessServices()
        {
            // Arrange
            await ClearDatabase();
            var workflowProfileInfoView = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_ID_1",
                ProfileName = "Test Profile",
                CompanyId = "ChHILD_COMPANY_123"
            };

            await _dbContext.WorkflowProfileInfoViews.AddAsync(workflowProfileInfoView);
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string>());

            // Create repository with empty assigned business services
            var mockUserService = new Mock<ILoggedInUserService>();
            var emptyAssignedEntity = new AssignedEntity
            {
                AssignedBusinessServices = new List<AssignedBusinessServices>()
            };
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(emptyAssignedEntity);
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryEmptyAssigned = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repositoryEmptyAssigned.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should return empty when no assigned business services
        }

        [Fact]
        public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedBusinessFunctions()
        {
            // Arrange
            await ClearDatabase();
            var workflowProfileInfoView = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_ID_1",
                ProfileName = "Test Profile",
                CompanyId = "ChHILD_COMPANY_123"
            };

            await _dbContext.WorkflowProfileInfoViews.AddAsync(workflowProfileInfoView);
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string>());

            // Create repository with assigned business services but no business functions
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedEntityWithEmptyFunctions = new AssignedEntity
            {
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>() // Empty functions
                    }
                }
            };
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(assignedEntityWithEmptyFunctions);
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryEmptyFunctions = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repositoryEmptyFunctions.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should return empty when no assigned business functions
        }

        [Fact]
        public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedInfraObjects()
        {
            // Arrange
            await ClearDatabase();
            var workflowProfileInfoView = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_ID_1",
                ProfileName = "Test Profile",
                CompanyId = "ChHILD_COMPANY_123"
            };

            await _dbContext.WorkflowProfileInfoViews.AddAsync(workflowProfileInfoView);
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string>());

            // Create repository with assigned business functions but no infra objects
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedEntityWithEmptyInfraObjects = new AssignedEntity
            {
                AssignedBusinessServices = new List<AssignedBusinessServices>
                {
                    new AssignedBusinessServices
                    {
                        Id = "BS_1",
                        AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                        {
                            new AssignedBusinessFunctions
                            {
                                Id = "BF_1",
                                AssignedInfraObjects = new List<AssignedInfraObjects>() // Empty infra objects
                            }
                        }
                    }
                }
            };
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(assignedEntityWithEmptyInfraObjects);
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryEmptyInfraObjects = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repositoryEmptyInfraObjects.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result); // Should return empty when no assigned infra objects
        }

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ShouldCombineBaseQueryAndPermissions_WhenBothExist()
        {
            // Arrange
            await ClearDatabase();
            var baseProfileInfo = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_ID_BASE",
                ProfileName = "Base Profile",
                ProfileId = "PROF_BASE",
                BusinessServiceId = "BS_BASE",
                CompanyId = "COMPANY_123"
            };

            var permissionProfileInfo = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_ID_PERM",
                ProfileName = "Permission Profile",
                ProfileId = "PROF_PERM",
                BusinessServiceId = "BS_PERM",
                CompanyId = "OTHER_COMPANY" // Different company, only accessible via permissions
            };

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(new[] { baseProfileInfo, permissionProfileInfo });
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository with permissions
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string> { permissionProfileInfo.ReferenceId });

            // Create repository with IsAllInfra = true
            var mockUserService = new Mock<ILoggedInUserService>();
            mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(true);
            mockUserService.Setup(x => x.IsAllInfra).Returns(true);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);

            var repository = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repository.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains(result, x => x.ReferenceId == baseProfileInfo.ReferenceId);
            Assert.Contains(result, x => x.ReferenceId == permissionProfileInfo.ReferenceId);
        }

        [Fact]
        public async Task AssignedInfraObjects_ShouldHandleMultipleAssignedInfraObjects_WhenMatching()
        {
            // Arrange
            await ClearDatabase();
            var infraObject1 = CreateInfraObject();
            infraObject1.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
            var infraObject2 = CreateInfraObject();
            infraObject2.ReferenceId = "ANOTHER_ASSIGNED_INFRA_ID";
            await _dbContext.InfraObjects.AddRangeAsync(new[] { infraObject1, infraObject2 });
            await _dbContext.SaveChangesAsync();

            var workflowProfileInfoViews = new List<WorkflowProfileInfoView>
            {
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = infraObject1.ReferenceId,
                    ProfileName = "Profile 1",
                    ProfileId = "PROF_1",
                    BusinessServiceId = "BS_1",
                    CompanyId = "ChHILD_COMPANY_123"
                },
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = infraObject1.ReferenceId, // Same infra object, different profile
                    ProfileName = "Profile 2",
                    ProfileId = "PROF_2",
                    BusinessServiceId = "BS_2",
                    CompanyId = "ChHILD_COMPANY_123"
                }
            };

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(workflowProfileInfoViews);
            await _dbContext.SaveChangesAsync();

            // Create mock workflow repository
            var mockWorkflowRepo = new Mock<IWorkflowRepository>();
            mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile"))
                .ReturnsAsync(new List<string>());

            // Create repository with IsAllInfra = false
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryNotAllInfra = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, mockWorkflowRepo.Object);

            // Act
            var result = await repositoryNotAllInfra.GetWorkflowProfileInfoNames();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Both profiles should be returned as they use the same assigned infra object
          
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ShouldUseAssignedInfraObjects_WhenIsAllInfraFalse()
        {
            // Arrange
            await ClearDatabase();
            var infraObject = CreateInfraObject();
            infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
            await _dbContext.InfraObjects.AddAsync(infraObject);
            await _dbContext.SaveChangesAsync();

            var workflowProfileInfoViews = new List<WorkflowProfileInfoView>
            {
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = infraObject.ReferenceId, // Assigned infra object
                    ProfileName = "Assigned Profile",
                    ProfileId = "PROF_ASSIGNED",
                    BusinessServiceId = "BS_ASSIGNED",
                    CompanyId = "ChHILD_COMPANY_123"
                },
                new WorkflowProfileInfoView
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectId = "UNASSIGNED_INFRA_ID", // Non-assigned infra object
                    ProfileName = "Unassigned Profile",
                    ProfileId = "PROF_UNASSIGNED",
                    BusinessServiceId = "BS_UNASSIGNED",
                    CompanyId = "ChHILD_COMPANY_123"
                }
            };

            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(workflowProfileInfoViews);
            await _dbContext.SaveChangesAsync();

            // Create repository with IsAllInfra = false
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryNotAllInfra = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, _workFlowRepository);

            var profileIds = new List<string> { "PROF_ASSIGNED", "PROF_UNASSIGNED" };

            // Act
            var result = await repositoryNotAllInfra.GetWorkflowProfileInfoByProfileIds(profileIds);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Only assigned infra object should be returned
            Assert.Equal("PROF_ASSIGNED", result.First().ProfileId);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ShouldUseAssignedInfraObjects_WhenIsAllInfraFalse()
        {
            // Arrange
            await ClearDatabase();
            var infraObject = CreateInfraObject();
            infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
            await _dbContext.InfraObjects.AddAsync(infraObject);
            await _dbContext.SaveChangesAsync();

            var workflowProfileInfoView = new WorkflowProfileInfoView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObject.ReferenceId, // Assigned infra object
                ProfileName = "Assigned Profile",
                ProfileId = "PROF_ASSIGNED",
                WorkflowId = "WORKFLOW_ASSIGNED",
                BusinessServiceId = "BS_ASSIGNED",
                CompanyId = "ChHILD_COMPANY_123"
            };

            await _dbContext.WorkflowProfileInfoViews.AddAsync(workflowProfileInfoView);
            await _dbContext.SaveChangesAsync();

            // Create repository with IsAllInfra = false
            var mockUserService = new Mock<ILoggedInUserService>();
            var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
            mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
            mockUserService.Setup(x => x.UserId).Returns("USER_456");
            mockUserService.Setup(x => x.IsParent).Returns(false);
            mockUserService.Setup(x => x.IsAllInfra).Returns(false);
            mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

            var repositoryNotAllInfra = new WorkflowProfileInfoViewRepository(_dbContext, mockUserService.Object, _workFlowRepository);

            var workflowIds = new List<string> { "WORKFLOW_ASSIGNED" };

            // Act
            var result = await repositoryNotAllInfra.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds("PROF_ASSIGNED", workflowIds);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result); // Should return the assigned infra object
            Assert.Equal("PROF_ASSIGNED", result.First().ProfileId);
        }

        #endregion

        private async Task ClearDatabase()
        {
            _dbContext.WorkflowProfileInfoViews.RemoveRange(_dbContext.WorkflowProfileInfoViews);
            _dbContext.InfraObjects.RemoveRange(_dbContext.InfraObjects);
            await _dbContext.SaveChangesAsync();
        }

        private Domain.Entities.InfraObject CreateInfraObject()
        {
            return new Domain.Entities.InfraObject
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Test Infra Object",
                CompanyId = "ChHILD_COMPANY_123",
                IsActive = true
            };
        }
    }
}