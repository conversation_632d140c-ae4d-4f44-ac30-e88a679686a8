using Azure;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestBusinessServiceHealthLogSpecification : Specification<BusinessServiceHealthLog>
{
    public TestBusinessServiceHealthLogSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.ProblemState.Contains(searchTerm);
        }
    }
}

public class BusinessServiceHealthLogRepositoryTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly BusinessServiceHealthLogFixture _businessServiceHealthLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceHealthLogRepository _repository;
    private readonly BusinessServiceHealthLogRepository _repositoryNotParent;

    public BusinessServiceHealthLogRepositoryTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _businessServiceHealthLogFixture = businessServiceHealthLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceHealthLogRepository(_dbContext);
        _repositoryNotParent = new BusinessServiceHealthLogRepository(_dbContext);
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddBusinessServiceHealthLog_Successfully()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties();

        // Act
        var result = await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceHealthLog.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceHealthLog.ConfiguredCount, result.ConfiguredCount);
        Assert.Equal(businessServiceHealthLog.DRReadyCount, result.DRReadyCount);
        Assert.Equal(businessServiceHealthLog.DRNotReadyCount, result.DRNotReadyCount);
        Assert.Equal(businessServiceHealthLog.ProblemState, result.ProblemState);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnBusinessServiceHealthLog_WhenExists()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties();
        await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessServiceHealthLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceHealthLog.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceHealthLog.ConfiguredCount, result.ConfiguredCount);
        Assert.Equal(businessServiceHealthLog.DRReadyCount, result.DRReadyCount);
        Assert.Equal(businessServiceHealthLog.DRNotReadyCount, result.DRNotReadyCount);
        Assert.Equal(businessServiceHealthLog.ProblemState, result.ProblemState);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString() ;

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllActiveBusinessServiceHealthLogs()
    {
        // Arrange
        var businessServiceHealthLogs = _businessServiceHealthLogFixture.BusinessServiceHealthLogList;
        foreach (var log in businessServiceHealthLogs)
        {
            await _repository.AddAsync(log);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= businessServiceHealthLogs.Count);
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateBusinessServiceHealthLog_Successfully()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties();
        await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Modify properties
        businessServiceHealthLog.ConfiguredCount = 150;
        businessServiceHealthLog.DRReadyCount = 140;
        businessServiceHealthLog.DRNotReadyCount = 10;
        businessServiceHealthLog.ProblemState = "Updated State";

        // Act
        await _repository.UpdateAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Assert
        var updatedLog = await _repository.GetByReferenceIdAsync(businessServiceHealthLog.ReferenceId);
        Assert.NotNull(updatedLog);
        Assert.Equal(150, updatedLog.ConfiguredCount);
        Assert.Equal(140, updatedLog.DRReadyCount);
        Assert.Equal(10, updatedLog.DRNotReadyCount);
        Assert.Equal("Updated State", updatedLog.ProblemState);
    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkBusinessServiceHealthLogAsInactive_Successfully()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties();
        await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        businessServiceHealthLog.IsActive = false;
        // Act
       await  _repository.UpdateAsync(businessServiceHealthLog);
         _dbContext.SaveChanges();

        // Assert
        var deletedLog = await _repository.GetByReferenceIdAsync(businessServiceHealthLog.ReferenceId);


        Assert.NotNull(deletedLog);
        Assert.False(deletedLog.IsActive);
    }

    #endregion

    #region Specification Tests

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnMatchingBusinessServiceHealthLogs()
    {
        // Arrange
        var operationalLogs = _businessServiceHealthLogFixture.CreateMultipleBusinessServiceHealthLogsWithSameProblemState("Operational", 3);
        var issuesLogs = _businessServiceHealthLogFixture.CreateMultipleBusinessServiceHealthLogsWithSameProblemState("Issues Detected", 2);

        foreach (var log in operationalLogs.Concat(issuesLogs))
        {
            await _repository.AddAsync(log);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new BusinessServiceHealthLogFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1,10,specification, "ProblemState","Asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count >= 3);

    }

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var logs = _businessServiceHealthLogFixture.CreateMultipleBusinessServiceHealthLogsWithSameProblemState("Operational", 2);
        foreach (var log in logs)
        {
            await _repository.AddAsync(log);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var specification = new BusinessServiceHealthLogFilterSpecification("Operational");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ProblemState", "Asc");

        // Assert
        Assert.NotNull(result);

    }

    #endregion

    #region Business Logic Tests

    [Fact]
    public async Task AddAsync_ShouldSetCorrectCounts_WhenCreatingBusinessServiceHealthLog()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithSpecificCounts(100, 85, 15);

        // Act
        var result = await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.ConfiguredCount);
        Assert.Equal(85, result.DRReadyCount);
        Assert.Equal(15, result.DRNotReadyCount);
        Assert.True(result.DRReadyCount + result.DRNotReadyCount <= result.ConfiguredCount);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnLogsOrderedByCreatedDate()
    {
        // Arrange
        var logs = new List<BusinessServiceHealthLog>();
        for (int i = 0; i < 3; i++)
        {
            var log = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties();
            log.CreatedDate = DateTime.UtcNow.AddDays(-i);
            logs.Add(log);
            await _repository.AddAsync(log);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 3);
        
        var orderedLogs = result.Where(x => logs.Any(l => l.ReferenceId == x.ReferenceId))
                               .OrderByDescending(x => x.CreatedDate)
                               .ToList();
        
        for (int i = 0; i < orderedLogs.Count - 1; i++)
        {
            Assert.True(orderedLogs[i].CreatedDate >= orderedLogs[i + 1].CreatedDate);
        }
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task AddAsync_ShouldHandleNullProblemState()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithProperties(problemState: null);
        businessServiceHealthLog.ProblemState=null;
        // Act
        var result = await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.ProblemState);
    }

    [Fact]
    public async Task AddAsync_ShouldHandleZeroCounts()
    {
        // Arrange
        var businessServiceHealthLog = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogWithSpecificCounts(0, 0, 0);

        // Act
        var result = await _repository.AddAsync(businessServiceHealthLog);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.ConfiguredCount);
        Assert.Equal(0, result.DRReadyCount);
        Assert.Equal(0, result.DRNotReadyCount);
    }

    #endregion
}
