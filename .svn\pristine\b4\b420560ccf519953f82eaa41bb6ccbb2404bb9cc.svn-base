using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing

public class TestComponentSaveAlFilterSpecification : Specification<ComponentSaveAll>
{
    public TestComponentSaveAlFilterSpecification(string searchString)

    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.EntityName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("EntityName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityName.Contains(stringItem.Replace("EntityName=", "",
                            StringComparison.OrdinalIgnoreCase)));

            }
            else
            {
                Criteria = p =>
                    p.EntityName.Contains(searchString);
            }
        }
    }
}
public class ComponentSaveAllRepositoryTests : IClassFixture<ComponentSaveAllFixture>
{
    private readonly ComponentSaveAllFixture _componentSaveAllFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ComponentSaveAllRepository _repository;
    private readonly ComponentSaveAllRepository _repositoryNotParent;

    public ComponentSaveAllRepositoryTests(ComponentSaveAllFixture componentSaveAllFixture)
    {
        _componentSaveAllFixture = componentSaveAllFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ComponentSaveAllRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new ComponentSaveAllRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddComponentSaveAll_Successfully()
    {
        // Arrange
        var componentSaveAll = _componentSaveAllFixture.ComponentSaveAllDto;


        // Act
        await _repository.AddAsync(componentSaveAll);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(componentSaveAll.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal(componentSaveAll.EntityName, result.EntityName);

    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnComponentSaveAll_WhenExists()
    {
        // Arrange
        var componentSaveAll = _componentSaveAllFixture.ComponentSaveAllDto;
        await _repository.AddAsync(componentSaveAll);

        // Act
        var result = await _repository.GetByReferenceIdAsync(componentSaveAll.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentSaveAll.ReferenceId, result.ReferenceId);
        Assert.Equal(componentSaveAll.EntityName, result.EntityName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("9767b50f-4f89-4149-9ffa-14107eeb4b8d");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateComponentSaveAll_Successfully()
    {
        // Arrange
        var componentSaveAll = _componentSaveAllFixture.ComponentSaveAllDto;
        await _repository.AddAsync(componentSaveAll);

        // Act
        componentSaveAll.EntityName = "Updated Component";
        componentSaveAll.Properties = "{\"updated\": true}";
        await _repository.UpdateAsync(componentSaveAll);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(componentSaveAll.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("Updated Component", result.EntityName);
        Assert.Equal("{\"updated\": true}", result.Properties);
    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        // Arrange
        var componentSaveAll = _componentSaveAllFixture.ComponentSaveAllDto;
        await _repository.AddAsync(componentSaveAll);

        // Act

        componentSaveAll.IsActive = false;
        _dbContext.ComponentSaveAlls.Update(componentSaveAll);
        _dbContext.SaveChanges();

        // Assert
        var result = await _repository.GetByReferenceIdAsync(componentSaveAll.ReferenceId);
        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveComponents_WhenIsParent()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllList;
        await _repository.AddRangeAsync(componentSaveAlls);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentSaveAlls.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredComponents_WhenIsNotParent()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllList.Take(3).ToList();

        await _repositoryNotParent.AddRangeAsync(componentSaveAlls);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // Only child company components
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllPaginationList;
        await _repository.AddRangeAsync(componentSaveAlls);

        var specification = new TestComponentSaveAlFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "EntityName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllPaginationList;

        componentSaveAlls[0].EntityName = "FilteredComponent1";
        componentSaveAlls[1].EntityName = "AnotherFilteredComponent";
        componentSaveAlls[1].IsActive = true;
        componentSaveAlls[0].IsActive = true;

        await _repository.AddRangeAsync(componentSaveAlls);

        var specification = new TestComponentSaveAlFilterSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "EntityName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.EntityName));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllList;
        _repository.AddRangeAsync(componentSaveAlls).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion



    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var componentSaveAlls = _componentSaveAllFixture.ComponentSaveAllList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(componentSaveAlls);
        var initialCount = componentSaveAlls.Count;

        var toUpdate = componentSaveAlls.Take(2).ToList();
        toUpdate.ForEach(x => x.EntityName = "UpdatedComponentName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = componentSaveAlls.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.EntityName == "UpdatedComponentName").ToList();
        Assert.Equal(2, updated.Count);
    }

   
    [Fact]
    public async Task Repository_ShouldHandleEmptyCollections_Gracefully()
    {
        // Act
        var emptyList = await _repository.ListAllAsync();
        var emptyPaginated = await _repository.PaginatedListAllAsync(1, 10, new TestComponentSaveAlFilterSpecification(""), "EntityName", "asc");

        // Assert
        Assert.NotNull(emptyList);
        Assert.Empty(emptyList);
        Assert.NotNull(emptyPaginated);
        Assert.True(emptyPaginated.Succeeded);
        Assert.Empty(emptyPaginated.Data);
    }

}
    #endregion
