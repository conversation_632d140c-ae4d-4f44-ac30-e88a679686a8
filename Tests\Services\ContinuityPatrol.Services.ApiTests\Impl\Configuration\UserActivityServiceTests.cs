﻿using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Shared.Core.Responses;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserActivityServiceTests : IClassFixture<UserActivityServiceFixture>
{
    private readonly UserActivityServiceFixture _fixture;

    public UserActivityServiceTests(UserActivityServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetUserActivityList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<UserActivityListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserActivityList);

        var result = await _fixture.Service.GetUserActivityList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserActivityList.Count, result.Count);
    }

    [Fact]
    public async Task GetUserActivityLoginName_Should_Return_List()
    {
        var loginName = "testuser";

        _fixture.ClientMock
            .Setup(x => x.Get<List<UserActivityLoginNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserActivityLoginNames);

        var result = await _fixture.Service.GetUserActivityLoginName(loginName);

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserActivityLoginNames.Count, result.Count);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_Should_Return_List()
    {
        var loginName = "testuser";
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";

        _fixture.ClientMock
            .Setup(x => x.Get<List<UserActivityListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserActivityList);

        var result = await _fixture.Service.GetStartTimeEndTimeByUser(loginName, startDate, endDate);

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserActivityList.Count, result.Count);
    }
}
