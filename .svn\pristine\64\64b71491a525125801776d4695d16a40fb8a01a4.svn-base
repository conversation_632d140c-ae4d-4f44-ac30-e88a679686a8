﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateDrOperation;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateStateToUnlock;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationCategoryType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessFunctionId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByDrReady;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class InfraObjectServiceTests : IClassFixture<InfraObjectServiceFixture>
{
    private readonly InfraObjectServiceFixture _fixture;

    public InfraObjectServiceTests(InfraObjectServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ReturnsExpectedResponse()
    {
        var command = _fixture.Fixture.Create<CreateInfraObjectCommand>();
        var response = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(response);

        var result = await _fixture.Service.CreateAsync(command);

        Assert.Equal(response, result);
    }

    [Fact]
    public async Task GetInfraObjectList_ReturnsList()
    {
        var list = _fixture.Fixture.CreateMany<InfraObjectListVm>(3).ToList();

        _fixture.ClientMock.Setup(c => c.Get<List<InfraObjectListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(list);

        var result = await _fixture.Service.GetInfraObjectList();

        Assert.Equal(list, result);
    }

    [Fact]
    public async Task UpdateAsync_ReturnsExpectedResponse()
    {
        var command = _fixture.Fixture.Create<UpdateInfraObjectCommand>();
        var response = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(response);

        var result = await _fixture.Service.UpdateAsync(command);

        Assert.Equal(response, result);
    }

    [Fact]
    public async Task UpdateDrOperationStatus_ReturnsExpectedResponse()
    {
        var command = _fixture.Fixture.Create<UpdateInfraObjectDrOperationCommand>();
        var response = _fixture.Fixture.Create<UpdateInfraObjectDrOperationResponse>();

        _fixture.ClientMock.Setup(c => c.Put<UpdateInfraObjectDrOperationResponse>(It.IsAny<RestRequest>())).ReturnsAsync(response);

        var result = await _fixture.Service.UpdateDrOperationStatus(command);

        Assert.Equal(response, result);
    }

    [Fact]
    public async Task GetPaginatedInfraObjects_ReturnsPaginatedResult()
    {
        var query = _fixture.Fixture.Create<GetInfraObjectPaginatedListQuery>();
        var response = _fixture.Fixture.Create<PaginatedResult<InfraObjectListVm>>();

        _fixture.ClientMock.Setup(c => c.Get<PaginatedResult<InfraObjectListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(response);

        var result = await _fixture.Service.GetPaginatedInfraObjects(query);

        Assert.Equal(response, result);
    }

    [Fact]
    public async Task DeleteAsync_ReturnsExpectedResponse()
    {
        var response = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(response);

        var result = await _fixture.Service.DeleteAsync("some-id");

        Assert.Equal(response, result);
    }

    [Fact]
    public async Task IsInfraObjectNameExist_ReturnsTrue()
    {
        _fixture.ClientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>())).ReturnsAsync(true);

        var result = await _fixture.Service.IsInfraObjectNameExist("test", "id");

        Assert.True(result);
    }
   
}
public class InfraObjectServiceTests1
{
    private readonly IFixture _fixture;
    private readonly Mock<IBaseClient> _mockClient;
    private readonly InfraObjectService _service;

    public InfraObjectServiceTests1()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _mockClient = _fixture.Freeze<Mock<IBaseClient>>();
        _service = new InfraObjectService(_mockClient.Object);
    }

    [Fact]
    public async Task GetInfraObjectByBusinessFunctionId_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<List<GetInfraObjectByBusinessFunctionIdVm>>();
        _mockClient.Setup(x => x.Get<List<GetInfraObjectByBusinessFunctionIdVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectByBusinessFunctionId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectByBusinessServiceId_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<List<GetInfraObjectByBusinessServiceIdVm>>();
        _mockClient.Setup(x => x.Get<List<GetInfraObjectByBusinessServiceIdVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectByBusinessServiceId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectById_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<InfraObjectDetailVm>();
        _mockClient.Setup(x => x.Get<InfraObjectDetailVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectById(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectDetailsById_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<GetInfraObjectDetailByIdVm>();
        _mockClient.Setup(x => x.Get<GetInfraObjectDetailByIdVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectDetailsById(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectDrReady_ShouldReturnExpected()
    {
        var expected = _fixture.Create<DrReadyCount>();
        _mockClient.Setup(x => x.Get<DrReadyCount>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectDrReady();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectListByReplicationCategoryType_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<List<InfraObjectListByReplicationCategoryTypeVm>>();
        _mockClient.Setup(x => x.Get<List<InfraObjectListByReplicationCategoryTypeVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectListByReplicationCategoryType(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectListByReplicationTypeId_ShouldReturnExpected()
    {
        var id = _fixture.Create<string>();
        var expected = _fixture.Create<List<InfraObjectListByReplicationTypeVm>>();
        _mockClient.Setup(x => x.Get<List<InfraObjectListByReplicationTypeVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectListByReplicationTypeId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetInfraObjectNames_ShouldReturnExpected()
    {
        var expected = _fixture.Create<List<GetInfraObjectNameVm>>();
        _mockClient.Setup(x => x.Get<List<GetInfraObjectNameVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.GetInfraObjectNames();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateInfraObjectState_ShouldReturnExpected()
    {
        var command = _fixture.Create<UpdateInfraObjectStateCommand>();
        var expected = _fixture.Create<BaseResponse>();
        _mockClient.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.UpdateInfraObjectState(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateInfraObjectStateUnlock_ShouldReturnExpected()
    {
        var command = _fixture.Create<UpdateStateToUnlockCommand>();
        var expected = _fixture.Create<BaseResponse>();
        _mockClient.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _service.UpdateInfraObjectStateUnlock(command);

        Assert.Equal(expected, result);
    }
}