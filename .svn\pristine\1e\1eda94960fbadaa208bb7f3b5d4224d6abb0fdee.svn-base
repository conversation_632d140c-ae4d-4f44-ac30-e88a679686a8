﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class LoadBalancerServiceTests : IClassFixture<LoadBalancerServiceFixture>
{
    private readonly LoadBalancerServiceFixture _fixture;

    public LoadBalancerServiceTests(LoadBalancerServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task TestConnection_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.TestConnection(_fixture.TestCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync("123");

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task GetLoadBalancerList_ShouldReturn_List()
    {
        var expected = _fixture.FixtureGen.CreateMany<LoadBalancerListVm>().ToList();

        _fixture.ClientMock.Setup(c => c.Get<List<LoadBalancerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetLoadBalancerList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsLoadBalancerNameExist_ShouldReturn_Bool()
    {
        _fixture.ClientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsLoadBalancerNameExist("test", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetLoadBalancerById_ShouldReturn_Detail()
    {
        var expected = _fixture.FixtureGen.Create<LoadBalancerDetailVm>();

        _fixture.ClientMock.Setup(c => c.Get<LoadBalancerDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetLoadBalancerById("id");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetPaginatedNodeConfigurations_ShouldReturn_Result()
    {
        var expected = _fixture.FixtureGen.Create<PaginatedResult<LoadBalancerListVm>>();

        _fixture.ClientMock.Setup(c => c.Get<PaginatedResult<LoadBalancerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetPaginatedNodeConfigurations(_fixture.PaginatedQuery);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetStateMonitorStatusList_ShouldReturn_List()
    {
        var expected = _fixture.FixtureGen.CreateMany<StateMonitorStatusListVm>().ToList();

        _fixture.ClientMock.Setup(c => c.Get<List<StateMonitorStatusListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetStateMonitorStatusList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsIpAddressAndPortExist_ShouldReturn_True()
    {
        _fixture.ClientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsIpAddressAndPortExist("127.0.0.1", 8080, "guid");

        Assert.True(result);
    }

    [Fact]
    public async Task UpdateNodeStatus_ShouldReturn_Response()
    {
        _fixture.ClientMock.Setup(c => c.Put<UpdateNodeStatusResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.NodeStatusResponse);

        var result = await _fixture.Service.UpdateNodeStatus(_fixture.NodeStatusCommand);

        Assert.Equal(_fixture.NodeStatusResponse, result);
    }

    [Fact]
    public async Task UpdateDefault_ShouldReturn_Response()
    {
        _fixture.ClientMock.Setup(c => c.Put<UpdateLoadBalancerDefaultResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DefaultResponse);

        var result = await _fixture.Service.UpdateDefault(_fixture.DefaultCommand);

        Assert.Equal(_fixture.DefaultResponse, result);
    }

    [Fact]
    public async Task GetLoadBalancerNamesByType_ShouldReturn_List()
    {
        var expected = _fixture.FixtureGen.CreateMany<LoadBalancerNameVm>().ToList();

        _fixture.ClientMock.Setup(c => c.Get<List<LoadBalancerNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetLoadBalancerNamesByType();

        Assert.Equal(expected, result);
    }
}
