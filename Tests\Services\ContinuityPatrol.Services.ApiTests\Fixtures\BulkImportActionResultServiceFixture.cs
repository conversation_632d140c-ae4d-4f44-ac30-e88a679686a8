﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BulkImportActionResultServiceFixture
{
    public CreateBulkImportActionResultCommand CreateCommand { get; }
    public UpdateBulkImportActionResultCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public BulkImportActionResultDetailVm DetailVm { get; }
    public List<BulkImportActionResultListVm> ListVm { get; }

    public BulkImportActionResultServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBulkImportActionResultCommand>();
        UpdateCommand = fixture.Create<UpdateBulkImportActionResultCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<BulkImportActionResultDetailVm>();
        ListVm = fixture.CreateMany<BulkImportActionResultListVm>(3).ToList();
    }
}