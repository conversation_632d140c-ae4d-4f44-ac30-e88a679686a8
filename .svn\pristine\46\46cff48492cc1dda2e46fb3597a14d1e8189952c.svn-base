﻿using AutoFixture;
using AutoFixture.AutoMoq;
using Moq;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;

public class TeamMasterServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public TeamMasterService Service { get; }

    public CreateTeamMasterCommand CreateCommand { get; }
    public UpdateTeamMasterCommand UpdateCommand { get; }
    public BaseResponse Response { get; }
    public string TeamId { get; }

    public TeamMasterServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new TeamMasterService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateTeamMasterCommand>();
        UpdateCommand = fixture.Create<UpdateTeamMasterCommand>();
        Response = fixture.Create<BaseResponse>();
        TeamId = fixture.Create<string>();
    }
}