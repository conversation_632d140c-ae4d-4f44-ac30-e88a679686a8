﻿using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class AccessManagerServiceTests : IClassFixture<AccessManagerServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly AccessManagerService _service;
    private readonly AccessManagerServiceFixture _fixture;

    public AccessManagerServiceTests(AccessManagerServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new AccessManagerService(_clientMock.Object);
    }

    [Fact]
    public async Task GetAccessManagerList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.Get<List<AccessManagerListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetAccessMangerList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.ListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetAccessManagerPaginatedList_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(x => x.Get<PaginatedResult<AccessManagerListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedListVm);

        var result = await _service.GetAccessManagerPaginatedList(_fixture.PaginatedQuery);

        Assert.NotNull(result);
        Assert.Equal(_fixture.PaginatedListVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("test-id");

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByUserRole_ShouldReturnVm()
    {
        _clientMock.Setup(x => x.Get<GetByRoleIdVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.RoleIdVm);

        var result = await _service.GetByUserRole("role-123");

        Assert.NotNull(result);
        Assert.Equal(_fixture.RoleIdVm, result);
    }
}
