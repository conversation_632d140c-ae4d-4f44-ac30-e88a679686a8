﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

public class UserInfoServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserInfoService Service { get; }

    public CreateUserInfoCommand CreateCommand { get; }
    public UpdateUserInfoCommand UpdateCommand { get; }
    public UserInfoDetailVm UserDetail { get; }
    public BaseResponse BaseResponse { get; }

    public UserInfoServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserInfoService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateUserInfoCommand>();
        UpdateCommand = fixture.Create<UpdateUserInfoCommand>();
        UserDetail = fixture.Create<UserInfoDetailVm>();
        BaseResponse = fixture.Create<BaseResponse>();
    }
}