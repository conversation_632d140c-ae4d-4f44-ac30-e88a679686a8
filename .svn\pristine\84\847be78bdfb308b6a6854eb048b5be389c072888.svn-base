﻿using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

public class ServerServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public ServerService Service { get; }
    public Fixture Fixture { get; }
    public BaseResponse Response { get; }

    public ServerServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Response = Fixture.Create<BaseResponse>();
        Service = new ServerService(ClientMock.Object);
    }
}


//using ContinuityPatrol.Services.Api.Impl.Configuration;
//using ContinuityPatrol.Shared.Core.Responses;
//using ContinuityPatrol.Shared.Services.Base;

//namespace ContinuityPatrol.Services.ApiTests.Fixtures;

//public class ServerServiceFixture
//{
//    public Mock<IBaseClient> ClientMock { get; }
//    public ServerService Service { get; }

//    public Fixture Fixture { get; }

//    public BaseResponse Response { get; }

//    public ServerServiceFixture()
//    {
//        Fixture = new Fixture();

//        ClientMock = new Mock<IBaseClient>();
//        Service = new ServerService(ClientMock.Object);

//        Response = Fixture.Create<BaseResponse>();
//    }
//}