﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BulkImportOperationGroupServiceFixture
{
    public CreateBulkImportOperationGroupCommand CreateCommand { get; }
    public UpdateBulkImportOperationGroupCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public BulkImportOperationGroupDetailVm DetailVm { get; }
    public List<BulkImportOperationGroupListVm> ListVm { get; }

    public BulkImportOperationGroupServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBulkImportOperationGroupCommand>();
        UpdateCommand = fixture.Create<UpdateBulkImportOperationGroupCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<BulkImportOperationGroupDetailVm>();
        ListVm = fixture.CreateMany<BulkImportOperationGroupListVm>(3).ToList();
    }
}