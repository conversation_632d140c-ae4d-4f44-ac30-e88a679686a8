﻿using ContinuityPatrol.Application.Features.UserRole.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserRoleServiceTests : IClassFixture<UserRoleServiceFixture>
{
    private readonly UserRoleServiceFixture _fixture;

    public UserRoleServiceTests(UserRoleServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.UserRoleId);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_UserRoleDetail()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<UserRoleDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId(_fixture.UserRoleId);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetUserRolePaginatedList_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<UserRoleListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetUserRolePaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsUserRoleNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsUserRoleNameExist("Admin", null);

        Assert.True(result);
    }

    [Fact]
    public async Task GetUserRoles_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<UserRoleListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.RoleList);

        var result = await _fixture.Service.GetUserRoles();

        Assert.Equal(_fixture.RoleList, result);
    }

    [Fact]
    public async Task GetUserRoleNames_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<UserRoleNamesVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.RoleNames);

        var result = await _fixture.Service.GetUserRoleNames();

        Assert.Equal(_fixture.RoleNames, result);
    }
}
