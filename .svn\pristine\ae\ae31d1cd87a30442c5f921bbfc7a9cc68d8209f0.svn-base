﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

public class CompanyServiceFixture
{
    public CreateCompanyCommand CreateCompanyCommand { get; }
    public UpdateCompanyCommand UpdateCompanyCommand { get; }
    public CreateDefaultCompanyCommand CreateDefaultCompanyCommand { get; }
    public GetCompanyPaginatedListQuery PaginatedListQuery { get; }
    public BaseResponse BaseResponse { get; }
    public CompanyDetailVm CompanyDetail { get; }
    public List<CompanyListVm> CompanyList { get; }
    public List<CompanyNameVm> CompanyNames { get; }
    public PaginatedResult<CompanyListVm> PaginatedCompanies { get; }
    public string CompanyName { get; }
    public string CompanyId { get; }


    public CompanyServiceFixture()
    {
        var fixture = new Fixture();

        CreateCompanyCommand = fixture.Create<CreateCompanyCommand>();
        UpdateCompanyCommand = fixture.Create<UpdateCompanyCommand>();
        CreateDefaultCompanyCommand = fixture.Create<CreateDefaultCompanyCommand>();
        PaginatedListQuery = fixture.Create<GetCompanyPaginatedListQuery>();
        BaseResponse = fixture.Create<BaseResponse>();
        CompanyDetail = fixture.Create<CompanyDetailVm>();
        CompanyList = fixture.Create<List<CompanyListVm>>();
        CompanyNames = fixture.Create<List<CompanyNameVm>>();
        PaginatedCompanies = fixture.Create<PaginatedResult<CompanyListVm>>();
        CompanyName = fixture.Create<string>();
        CompanyId = fixture.Create<string>();
    }
}