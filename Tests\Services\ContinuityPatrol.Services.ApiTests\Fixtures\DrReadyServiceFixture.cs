﻿using AutoFixture;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.InfraObjectSchedulerLogPagination;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class DrReadyServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public DrReadyService Service { get; }

    public CreateInfraObjectSchedulerCommand CreateCommand { get; }
    public UpdateInfraObjectSchedulerCommand UpdateCommand { get; }
    public UpdateInfraObjectSchedulerStatusCommand StatusCommand { get; }
    public UpdateInfraObjectSchedulerStateCommand StateCommand { get; }
    public GetInfraObjectSchedulerPaginatedListQuery PaginatedQuery { get; }
    public GetInfraObjectSchedulerLogsPaginationQuery LogsQuery { get; }

    public BaseResponse BaseResponse { get; }
    public PaginatedResult<InfraObjectSchedulerListVm> PaginatedResult { get; }
    public PaginatedResult<InfraObjectSchedulerLogsListVm> LogsPaginatedResult { get; }

    public DrReadyServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new DrReadyService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateInfraObjectSchedulerCommand>();
        UpdateCommand = Fixture.Create<UpdateInfraObjectSchedulerCommand>();
        StatusCommand = Fixture.Create<UpdateInfraObjectSchedulerStatusCommand>();
        StateCommand = Fixture.Create<UpdateInfraObjectSchedulerStateCommand>();
        PaginatedQuery = Fixture.Create<GetInfraObjectSchedulerPaginatedListQuery>();
        LogsQuery = Fixture.Create<GetInfraObjectSchedulerLogsPaginationQuery>();

        BaseResponse = Fixture.Create<BaseResponse>();
        PaginatedResult = Fixture.Create<PaginatedResult<InfraObjectSchedulerListVm>>();
        LogsPaginatedResult = Fixture.Create<PaginatedResult<InfraObjectSchedulerLogsListVm>>();
    }
}
