﻿using AutoFixture;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class GlobalSettingServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public GlobalSettingService Service { get; }

    public CreateGlobalSettingCommand CreateCommand { get; }
    public UpdateGlobalSettingCommand UpdateCommand { get; }
    public AuthenticationCommand AuthCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<GlobalSettingListVm> ListVm { get; }
    public GlobalSettingDetailVm DetailVm { get; }
    public GetGlobalSettingPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<GlobalSettingListVm> PaginatedResult { get; }

    public GlobalSettingServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new GlobalSettingService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateGlobalSettingCommand>();
        UpdateCommand = Fixture.Create<UpdateGlobalSettingCommand>();
        AuthCommand = Fixture.Create<AuthenticationCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        ListVm = Fixture.CreateMany<GlobalSettingListVm>(3).ToList();
        DetailVm = Fixture.Create<GlobalSettingDetailVm>();
        PaginatedQuery = Fixture.Create<GetGlobalSettingPaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<GlobalSettingListVm>>();
    }
}
