﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserLogin.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.UserLoginModel;
using ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.CreateSession;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class UserLoginServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserLoginService Service { get; }

    public string UserId { get; }
    public string SessionId { get; }
    public ClearSessionUserLoginResponse ClearSessionResponse { get; }
    public CreateSessionUserLoginResponse CreateSessionResponse { get; }
    public UserLoginDetailVm UserLoginDetail { get; }
    public BaseResponse BaseResponse { get; }
    public UpdateUserLoginCommand UpdateCommand { get; }

    public UserLoginServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserLoginService(ClientMock.Object);

        UserId = fixture.Create<string>();
        SessionId = fixture.Create<string>();
        ClearSessionResponse = fixture.Create<ClearSessionUserLoginResponse>();
        CreateSessionResponse = fixture.Create<CreateSessionUserLoginResponse>();
        UserLoginDetail = fixture.Create<UserLoginDetailVm>();
        BaseResponse = fixture.Create<BaseResponse>();
        UpdateCommand = fixture.Create<UpdateUserLoginCommand>();
    }
}