using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DrCalenderRepositoryTests : IClassFixture<DrCalenderFixture>
{
    private readonly DrCalenderFixture _drCalenderFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DrCalenderRepository _repository;
    private readonly DrCalenderRepository _repositoryNotParent;

    public DrCalenderRepositoryTests(DrCalenderFixture drCalenderFixture)
    {
        _drCalenderFixture = drCalenderFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DrCalenderRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DrCalenderRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var drCalenderActivity = _drCalenderFixture.DrCalenderActivityDto;
        drCalenderActivity.ReferenceId=Guid.NewGuid().ToString(); // Ensure ReferenceId is set
        // Act
        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivity);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(drCalenderActivity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drCalenderActivity.ActivityName, result.ActivityName);
        Assert.Equal(drCalenderActivity.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DrCalenderActivity);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var drCalenderActivity = _drCalenderFixture.DrCalenderActivityDto;
        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivity);
        await _dbContext.SaveChangesAsync();

        drCalenderActivity.ActivityName = "UpdatedActivityName";

        // Act
        _dbContext.DrCalenderActivity.Update(drCalenderActivity);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(drCalenderActivity.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedActivityName", result.ActivityName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var drCalenderActivity = _drCalenderFixture.DrCalenderActivityDto;
        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivity);
        await _dbContext.SaveChangesAsync();

        // Act
        drCalenderActivity.IsActive = false;

        _dbContext.DrCalenderActivity.Update(drCalenderActivity);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drCalenderActivities.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        // Set ReferenceId to match the company ID for filtering
        drCalenderActivities.ForEach(x => x.ReferenceId = DrCalenderFixture.CompanyId);
        await _repositoryNotParent.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DrCalenderFixture.CompanyId, x.ReferenceId));
    }

    #endregion

    #region IsDrCalenderNameUnique Tests

    [Fact]
    public async Task IsDrCalenderNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var drCalenderActivity = _drCalenderFixture.DrCalenderActivityDto;
        drCalenderActivity.ActivityName = "ExistingActivityName";
        await _repository.AddAsync(drCalenderActivity);

        // Act
        var result = await _repository.IsDrCalenderNameUnique("ExistingActivityName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDrCalenderNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.IsDrCalenderNameUnique("NonExistentActivityName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region DrCalendarDrillEventList Tests

    [Fact]
    public async Task DrCalendarDrillEventList_ShouldReturnFutureEvents_WhenIsParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        // Set some activities with future dates
        drCalenderActivities.Take(2).ToList().ForEach(x => x.ScheduledStartDate = DateTime.Today.AddDays(1));
        // Set some activities with past dates
        drCalenderActivities.Skip(2).ToList().ForEach(x => x.ScheduledStartDate = DateTime.Today.AddDays(-1));
        
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.DrCalendarDrillEventList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.ScheduledStartDate >= DateTime.Today));
    }

    [Fact]
    public async Task DrCalendarDrillEventList_ShouldReturnFilteredFutureEvents_WhenIsNotParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
      
        drCalenderActivities.Take(2).ToList().ForEach(x => 
        {
            x.ScheduledStartDate = DateTime.Today.AddDays(1);
            x.CompanyId = "ChHILD_COMPANY_123";
        });
        // Set some activities with past dates
        drCalenderActivities.Skip(2).ToList().ForEach(x => x.ScheduledStartDate = DateTime.Today.AddDays(-1));
        
        _dbContext.DrCalenderActivity.AddRange(drCalenderActivities);
        _dbContext.SaveChanges();

        // Act
        var result = await _repositoryNotParent.DrCalendarDrillEventList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.ScheduledStartDate >= DateTime.Today));
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetByWorkflowProfileId Tests

    [Fact]
    public async Task GetByWorkflowProfileId_ShouldReturnPastEvents_WhenIsParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        // Set some activities with past dates and matching workflow profile
        drCalenderActivities.Take(2).ToList().ForEach(x => 
        {
            x.ScheduledStartDate = DateTime.Now.AddDays(-1);
            x.WorkflowProfiles = "20fb61e8-6dba-455b-859f-1bc18de32c35";
        });
        // Set some activities with future dates
        drCalenderActivities.Skip(2).ToList().ForEach(x => x.ScheduledStartDate = DateTime.Now.AddDays(1));
        
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.GetByWorkflowProfileId("20fb61e8-6dba-455b-859f-1bc18de32c35");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.ScheduledStartDate < DateTime.Now));
        Assert.All(result, x => Assert.Contains("20fb61e8-6dba-455b-859f-1bc18de32c35", x.WorkflowProfiles));
    }

    [Fact]
    public async Task GetByWorkflowProfileId_ShouldReturnFilteredPastEvents_WhenIsNotParent()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        // Set some activities with past dates, matching workflow profile and company
        drCalenderActivities.Take(2).ToList().ForEach(x => 
        {
            x.ScheduledStartDate = DateTime.Now.AddDays(-1);
            x.WorkflowProfiles = DrCalenderFixture.WorkflowProfileId;
            x.CompanyId = "ChHILD_COMPANY_123";
        });
        // Set some activities with future dates
        drCalenderActivities.Skip(2).ToList().ForEach(x => x.ScheduledStartDate = DateTime.Now.AddDays(1));

        _dbContext.DrCalenderActivity.AddRange(drCalenderActivities);
        _dbContext.SaveChanges();

        // Act
        var result = await _repositoryNotParent.GetByWorkflowProfileId(DrCalenderFixture.WorkflowProfileId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.ScheduledStartDate < DateTime.Now));
        Assert.All(result, x => Assert.Contains(DrCalenderFixture.WorkflowProfileId, x.WorkflowProfiles));
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetByWorkflowProfileId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.GetByWorkflowProfileId("non-existent-workflow-profile-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;

        // Act
        var result = await _repository.AddRangeAsync(drCalenderActivities);

        // Assert
        Assert.Equal(drCalenderActivities.Count, result.Count());
        Assert.Equal(drCalenderActivities.Count, _dbContext.DrCalenderActivity.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList;
        await _repository.AddRangeAsync(drCalenderActivities);

        // Act
        var result = await _repository.RemoveRangeAsync(drCalenderActivities);

        // Assert
        Assert.Equal(drCalenderActivities.Count, result.Count());
        Assert.Empty(_dbContext.DrCalenderActivity);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(drCalenderActivities);
        var initialCount = drCalenderActivities.Count;
        
        var toUpdate = drCalenderActivities.Take(2).ToList();
        toUpdate.ForEach(x => x.ActivityName = "UpdatedActivityName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = drCalenderActivities.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.ActivityName == "UpdatedActivityName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region IsActivityNameExist Tests


    [Fact]
    public async Task IsActivityNameExist_ShouldReturnTrue_WhenNameAndDateExist_AndIdIsInvalid()
    {
        // Arrange
        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityDto;
        drCalenderActivities.ActivityName = "DRActivity";
        drCalenderActivities.ScheduledStartDate = new DateTime(2025, 7, 16);
        var invalidId = "NotAGuid";

        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivities);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.IsActivityNameExist(drCalenderActivities.ActivityName, invalidId, drCalenderActivities.ScheduledStartDate);

        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task IsActivityNameExist_ShouldReturnFalse_WhenNameAndDateDoNotExist_AndIdIsInvalid()
    {
        // Arrange
        var name = "NonExisting";
        var date = DateTime.Today;
        var invalidId = "invalid-id";

        // Act
        var result = await _repository.IsActivityNameExist(name, invalidId, date);

        // Assert
        Assert.False(result);
    }
    [Fact]
    public async Task IsActivityNameExist_ShouldReturnTrue_WhenAnotherActivityWithSameNameDateExists()
    {
        // Arrange
        var name = "SameActivity";
        var date = new DateTime(2025, 7, 16);
        var existingId = Guid.NewGuid().ToString();
        var otherId = Guid.NewGuid().ToString();

        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityDto;
        drCalenderActivities.ActivityName = name;
        drCalenderActivities.ScheduledStartDate = date;
        drCalenderActivities.ReferenceId = "NotAGuid";

        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivities);
        _dbContext.SaveChanges();


        // Act
        var result = await _repository.IsActivityNameExist(name, existingId, date);

        // Assert
        Assert.True(result); // name+date exists with different id
    }
    [Fact]
    public async Task IsActivityNameExist_ShouldReturnFalse_WhenSameActivityExistsWithSameId()
    {
        // Arrange
        var name = "UniqueActivity";
        var date = new DateTime(2025, 7, 16);
        var sameId = Guid.NewGuid().ToString();

        var drCalenderActivities = _drCalenderFixture.DrCalenderActivityDto;
        drCalenderActivities.ActivityName = name;
        drCalenderActivities.ScheduledStartDate = date;
        drCalenderActivities.ReferenceId = sameId;

        await _dbContext.DrCalenderActivity.AddAsync(drCalenderActivities);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.IsActivityNameExist(name, sameId, date);

        // Assert
        Assert.False(result); // it's not a duplicate if it's the same ID
    }
    [Fact]
    public async Task IsActivityNameExist_ShouldReturnFalse_WhenNoDataInDb()
    {
        // Arrange
        var name = "NoData";
        var date = DateTime.Today;
        var id = Guid.NewGuid().ToString();

        // No data

        // Act
        var result = await _repository.IsActivityNameExist(name, id, date);

        // Assert
        Assert.False(result);
    }


    #endregion

    #region UpcommingDrill
    [Fact]
    public async Task UpComingDrillList_ShouldReturnAllDrillsWithin10Days_WhenUserIsParent()
    {
        // Arrange
        var today = DateTime.Now.Date;
        var company1 = "COMPANY_123";
        var company2 = "COMPANY_2";

        var activities = new List<DrCalenderActivity>
    {
        _drCalenderFixture.CreateActivity("Activity 1", today.AddDays(3), company1),
        _drCalenderFixture.CreateActivity("Activity 2", today.AddDays(9), company2),
        _drCalenderFixture.CreateActivity("Old Activity", today.AddDays(-1), company1),
        _drCalenderFixture.CreateActivity("Future Activity", today.AddDays(15), company1)
    };

        await _dbContext.DrCalenderActivity.AddRangeAsync(activities);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.UpComingDrillList();

        // Assert
        Assert.NotEmpty(result);
        Assert.Equal(2, result.Count); // Only 2 within next 10 days
        Assert.All(result, x => Assert.InRange(x.ScheduledStartDate.Date, today, today.AddDays(10)));
    }
    [Fact]
    public async Task UpComingDrillList_ShouldReturnOnlyCompanyDrills_WhenUserIsNotParent()
    {
        // Arrange
        var today = DateTime.Now.Date;
        var userCompanyId = "ChHILD_COMPANY_123";

        var activities = new List<DrCalenderActivity>
    {
        _drCalenderFixture.CreateActivity("Activity 1", today.AddDays(3), userCompanyId),
        _drCalenderFixture.CreateActivity("Activity 2", today.AddDays(5), "OTHER_CO"),
        _drCalenderFixture.CreateActivity("Too Far", today.AddDays(15), userCompanyId)
    };

        await _dbContext.DrCalenderActivity.AddRangeAsync(activities);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.UpComingDrillList();

        // Assert
        Assert.Single(result); // Only one from user's company
        Assert.Equal(userCompanyId, result.First().CompanyId);
    }
    [Fact]
    public async Task UpComingDrillList_ShouldReturnEmpty_WhenNoDrillsExist()
    {
        // Act
        var result = await _repository.UpComingDrillList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
    [Fact]
    public async Task UpComingDrillList_ShouldReturnEmpty_WhenDrillsAreOutside10DayWindow()
    {
        // Arrange
        var today = DateTime.Now.Date;
        var companyId = "COMPANY_123";

        var activities = new List<DrCalenderActivity>
    {
        _drCalenderFixture. CreateActivity("Past Drill", today.AddDays(-5), companyId),
        _drCalenderFixture. CreateActivity("Far Future Drill", today.AddDays(20), companyId)
    };

        await _dbContext.DrCalenderActivity.AddRangeAsync(activities);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.UpComingDrillList();

        // Assert
        Assert.Empty(result);
    }



    #endregion
}
