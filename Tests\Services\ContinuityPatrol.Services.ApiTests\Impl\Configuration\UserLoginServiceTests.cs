﻿using ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.CreateSession;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserLoginServiceTests : IClassFixture<UserLoginServiceFixture>
{
    private readonly UserLoginServiceFixture _fixture;

    public UserLoginServiceTests(UserLoginServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task ClearDatabaseSession_Should_Return_Expected_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<ClearSessionUserLoginResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ClearSessionResponse);

        var result = await _fixture.Service.ClearDatabaseSession(_fixture.UserId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.ClearSessionResponse, result);
    }

    [Fact]
    public async Task CreateDatabaseSession_Should_Return_Expected_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<CreateSessionUserLoginResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.CreateSessionResponse);

        var result = await _fixture.Service.CreateDatabaseSession(_fixture.UserId, _fixture.SessionId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.CreateSessionResponse, result);
    }

    [Fact]
    public async Task GetUserInfoByUserId_Should_Return_Expected_User()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<UserLoginDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserLoginDetail);

        var result = await _fixture.Service.GetUserInfoByUserId(_fixture.UserId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserLoginDetail, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }
}
