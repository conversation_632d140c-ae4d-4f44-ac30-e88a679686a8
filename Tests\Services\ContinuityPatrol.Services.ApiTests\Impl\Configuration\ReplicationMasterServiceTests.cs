﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class ReplicationMasterServiceTests : IClassFixture<ReplicationMasterServiceFixture>
{
    private readonly ReplicationMasterServiceFixture _fixture;

    public ReplicationMasterServiceTests(ReplicationMasterServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetReplicationMasterList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<ReplicationMasterListVm>>(It.IsAny<RestRequest>(), "GetReplicationMasterList"))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetReplicationMasterList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetReplicationMasterNames_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<ReplicationMasterNameVm>>(It.IsAny<RestRequest>(), "GetReplicationMasterNames"))
            .ReturnsAsync(_fixture.NameList);

        var result = await _fixture.Service.GetReplicationMasterNames();

        Assert.Equal(_fixture.NameList, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<ReplicationMasterDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("sample-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsReplicationMasterNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsReplicationMasterNameExist("TestReplicationMaster", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetReplicationMasterPaginatedList_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<PaginatedResult<ReplicationMasterListVm>>(It.IsAny<RestRequest>(), "GetReplicationMasterPaginatedList"))
            .ReturnsAsync(_fixture.PaginatedList);

        var result = await _fixture.Service.GetReplicationMasterPaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedList, result);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<GetByInfraMasterNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ByInfraMasterList);

        var result = await _fixture.Service.GetReplicationMasterByInfraMasterName("infra-name");

        Assert.Equal(_fixture.ByInfraMasterList, result);
    }
}
