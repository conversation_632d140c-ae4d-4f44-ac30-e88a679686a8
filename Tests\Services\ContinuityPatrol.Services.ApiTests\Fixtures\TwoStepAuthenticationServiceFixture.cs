﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.TwoStepAuthentication.Commands.SendOtp;
using ContinuityPatrol.Application.Features.TwoStepAuthentication.Commands.VerifyOtp;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class TwoStepAuthenticationServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public TwoStepAuthenticationService Service { get; }

    public SendOtpCommand SendOtpCommand { get; }
    public SendOtpResponse SendOtpResponse { get; }

    public VerifyOtpCommand VerifyOtpCommand { get; }
    public VerifyOtpResponse VerifyOtpResponse { get; }

    public TwoStepAuthenticationServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new TwoStepAuthenticationService(ClientMock.Object);

        SendOtpCommand = fixture.Create<SendOtpCommand>();
        SendOtpResponse = fixture.Create<SendOtpResponse>();

        VerifyOtpCommand = fixture.Create<VerifyOtpCommand>();
        VerifyOtpResponse = fixture.Create<VerifyOtpResponse>();
    }
}