﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BusinessServiceServiceFixture
{
    public CreateBusinessServiceCommand CreateCommand { get; }
    public UpdateBusinessServiceCommand UpdateCommand { get; }
    public GetBusinessServicePaginatedListQuery PaginatedQuery { get; }
    public List<BusinessServiceListVm> ListVm { get; }
    public List<BusinessServiceNameVm> NameVm { get; }
    public BusinessServiceDetailVm DetailVm { get; }
    public GetBusinessServiceDiagramDetailVm DiagramVm { get; }
    public PaginatedResult<BusinessServiceListVm> PaginatedResult { get; }
    public BaseResponse BaseResponse { get; }

    public BusinessServiceServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBusinessServiceCommand>();
        UpdateCommand = fixture.Create<UpdateBusinessServiceCommand>();
        PaginatedQuery = fixture.Create<GetBusinessServicePaginatedListQuery>();
        ListVm = fixture.CreateMany<BusinessServiceListVm>().ToList();
        NameVm = fixture.CreateMany<BusinessServiceNameVm>().ToList();
        DetailVm = fixture.Create<BusinessServiceDetailVm>();
        DiagramVm = fixture.Create<GetBusinessServiceDiagramDetailVm>();
        PaginatedResult = fixture.Create<PaginatedResult<BusinessServiceListVm>>();
        BaseResponse = fixture.Create<BaseResponse>();
    }
}