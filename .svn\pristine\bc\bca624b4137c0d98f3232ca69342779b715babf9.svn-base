﻿using AutoFixture;
using AutoFixture.AutoMoq;
using Moq;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Create;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Shared.Core.Wrapper;

public class TableAccessServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public TableAccessService Service { get; }

    public CreateTableAccessCommand CreateCommand { get; }
    public UpdateTableAccessCommand UpdateCommand { get; }
    public GetTableAccessPaginatedListQuery Query { get; }
    public BaseResponse Response { get; }
    public PaginatedResult<TableAccessListVm> PaginatedResult { get; }
    public List<TableAccessListVm> TableAccessList { get; }
    public List<SchemaNameListVm> SchemaNames { get; }
    public List<GetTableNameListBySchemaVm> TableNames { get; }
    public string TableAccessName { get; }
    public string SchemaName { get; }
    public string Id { get; }

    public TableAccessServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new TableAccessService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateTableAccessCommand>();
        UpdateCommand = fixture.Create<UpdateTableAccessCommand>();
        Query = fixture.Create<GetTableAccessPaginatedListQuery>();
        Response = fixture.Create<BaseResponse>();
        PaginatedResult = fixture.Create<PaginatedResult<TableAccessListVm>>();
        TableAccessList = fixture.Create<List<TableAccessListVm>>();
        SchemaNames = fixture.Create<List<SchemaNameListVm>>();
        TableNames = fixture.Create<List<GetTableNameListBySchemaVm>>();
        TableAccessName = fixture.Create<string>();
        SchemaName = fixture.Create<string>();
        Id = fixture.Create<string>();
    }
}
