﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestStateMonitorStatusSpecification : Specification<StateMonitorStatus>
{
    public TestStateMonitorStatusSpecification()
    {
        Criteria = x => x.IsActive;
    }
}

public class StateMonitorStatusRepositoryTests : IClassFixture<StateMonitorStatusFixture>
{
    private readonly StateMonitorStatusFixture _stateMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly StateMonitorStatusRepository _repository;
    private readonly StateMonitorStatusRepository _repositoryNotParent;

    public StateMonitorStatusRepositoryTests(StateMonitorStatusFixture stateMonitorStatusFixture)
    {
        _stateMonitorStatusFixture = stateMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new StateMonitorStatusRepository(_dbContext);
        _repositoryNotParent = new StateMonitorStatusRepository(_dbContext);
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddStateMonitorStatus_Successfully()
    {
        // Arrange
        var stateMonitorStatus = _stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties();

        // Act
        var result = await _repository.AddAsync(stateMonitorStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(stateMonitorStatus.ReferenceId, result.ReferenceId);

        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnStateMonitorStatus_WhenExists()
    {
        // Arrange
        var stateMonitorStatus = _stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties();
        await _repository.AddAsync(stateMonitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(stateMonitorStatus.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(stateMonitorStatus.ReferenceId, result.ReferenceId);

    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByIdAsync(12);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllActiveStateMonitorStatuses()
    {
        // Arrange
        var stateMonitorStatuses = _stateMonitorStatusFixture.StateMonitorStatusList;
        foreach (var status in stateMonitorStatuses)
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= stateMonitorStatuses.Count);
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateStateMonitorStatus_Successfully()
    {
        // Arrange
        var stateMonitorStatus = _stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties();
        await _repository.AddAsync(stateMonitorStatus);
        await _dbContext.SaveChangesAsync();

        // Modify properties
        stateMonitorStatus.LastModifiedDate = DateTime.UtcNow;

        // Act
         _dbContext.StateMonitorStatus.Update(stateMonitorStatus);
        _dbContext.SaveChanges();

        // Assert
        var updatedStatus = await _repository.GetByIdAsync(stateMonitorStatus.Id);
        Assert.NotNull(updatedStatus);
        Assert.Equal(stateMonitorStatus.ReferenceId, updatedStatus.ReferenceId);
    }

    #endregion

   

    #region Specification Tests

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnMatchingStateMonitorStatuses()
    {
        // Arrange
        var activeStatuses = new List<StateMonitorStatus>();
        var inactiveStatuses = new List<StateMonitorStatus>();

        for (int i = 0; i < 3; i++)
        {
            activeStatuses.Add(_stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties(isActive: true));
            inactiveStatuses.Add(_stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties(isActive: false));
        }

        foreach (var status in activeStatuses.Concat(inactiveStatuses))
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new TestStateMonitorStatusSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1,10,specification, "Properties", "Asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count >= 3);
        Assert.All(result.Data, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnEmptyList_WhenNoActiveMatches()
    {
        // Arrange
        var inactiveStatuses = new List<StateMonitorStatus>();
        for (int i = 0; i < 2; i++)
        {
            inactiveStatuses.Add(_stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties(isActive: false));
        }

        foreach (var status in inactiveStatuses)
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new TestStateMonitorStatusSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Properties", "Asc");

        // Assert
        Assert.NotNull(result);
        // Should not return inactive statuses
        Assert.DoesNotContain(result.Data, status => !status.IsActive);
    }

    #endregion

    #region Business Logic Tests

    [Fact]
    public async Task GetAllAsync_ShouldReturnStatusesOrderedByCreatedDate()
    {
        // Arrange
        var statuses = new List<StateMonitorStatus>();
        for (int i = 0; i < 3; i++)
        {
            var status = _stateMonitorStatusFixture.CreateStateMonitorStatusWithProperties();
            status.CreatedDate = DateTime.UtcNow.AddDays(-i);
            statuses.Add(status);
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 3);

        var orderedStatuses = result.Where(x => statuses.Any(s => s.ReferenceId == x.ReferenceId))
                                   .OrderByDescending(x => x.CreatedDate)
                                   .ToList();

        for (int i = 0; i < orderedStatuses.Count - 1; i++)
        {
            Assert.True(orderedStatuses[i].CreatedDate >= orderedStatuses[i + 1].CreatedDate);
        }
    }

    #endregion

    #region Edge Cases

   
    [Fact]
    public async Task GetByIdAsync_ShouldHandleInvalidGuidFormat()
    {
        // Arrange
        var invalidId = "invalid-guid-format";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(async () => await _repository.GetByReferenceIdAsync(invalidId));
    }

    #endregion
}
