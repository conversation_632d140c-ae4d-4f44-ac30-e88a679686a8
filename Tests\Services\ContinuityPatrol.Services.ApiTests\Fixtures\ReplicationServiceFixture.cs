﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class ReplicationServiceFixture
{
    public ReplicationService Service { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public Fixture Fixture { get; }

    public CreateReplicationCommand CreateCommand { get; }
    public CreateReplicationResponse CreateResponse { get; }

    public UpdateReplicationCommand UpdateCommand { get; }
    public UpdateReplicationResponse UpdateResponse { get; }

    public SaveAllReplicationCommand SaveAllCommand { get; }
    public SaveAsReplicationCommand SaveAsCommand { get; }

    public List<ReplicationListVm> ReplicationList { get; }
    public List<ReplicationNameVm> NameList { get; }
    public List<ReplicationTypeVm> TypeList { get; }
    public List<GetReplicationByLicenseKeyListVm> ByLicenseList { get; }

    public ReplicationDetailVm DetailVm { get; }

    public BaseResponse BaseResponse { get; }

    public GetReplicationPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<ReplicationListVm> PaginatedResult { get; }

    public ReplicationServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new ReplicationService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateReplicationCommand>();
        CreateResponse = Fixture.Create<CreateReplicationResponse>();

        UpdateCommand = Fixture.Create<UpdateReplicationCommand>();
        UpdateResponse = Fixture.Create<UpdateReplicationResponse>();

        SaveAllCommand = Fixture.Create<SaveAllReplicationCommand>();
        SaveAsCommand = Fixture.Create<SaveAsReplicationCommand>();

        ReplicationList = Fixture.Create<List<ReplicationListVm>>();
        NameList = Fixture.Create<List<ReplicationNameVm>>();
        TypeList = Fixture.Create<List<ReplicationTypeVm>>();
        ByLicenseList = Fixture.Create<List<GetReplicationByLicenseKeyListVm>>();

        DetailVm = Fixture.Create<ReplicationDetailVm>();
        BaseResponse = Fixture.Create<BaseResponse>();

        PaginatedQuery = Fixture.Create<GetReplicationPaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<ReplicationListVm>>();
    }
}
