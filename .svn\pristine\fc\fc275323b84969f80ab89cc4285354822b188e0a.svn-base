﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetailByDatabaseId;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration
{
    public class InfraReplicationMappingServiceFixture
    {
        public IFixture Fixture { get; }
        public Mock<IBaseClient> MockClient { get; }
        public InfraReplicationMappingService Service { get; }

        public InfraReplicationMappingServiceFixture()
        {
            Fixture = new Fixture().Customize(new AutoMoqCustomization());
            MockClient = Fixture.Freeze<Mock<IBaseClient>>();
            Service = new InfraReplicationMappingService(MockClient.Object);
        }
    }

    public class InfraReplicationMappingServiceTests : IClassFixture<InfraReplicationMappingServiceFixture>
    {
        private readonly InfraReplicationMappingServiceFixture _fixture;

        public InfraReplicationMappingServiceTests(InfraReplicationMappingServiceFixture fixture)
        {
            _fixture = fixture;
        }

        [Fact]
        public async Task GetInfraReplicationMappingList_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<List<InfraReplicationMappingListVm>>();
            _fixture.MockClient.Setup(c => c.GetFromCache<List<InfraReplicationMappingListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
                .ReturnsAsync(expected);

            var result = await _fixture.Service.GetInfraReplicationMappingList();

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetByReferenceId_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<InfraReplicationMappingDetailVm>();
            _fixture.MockClient.Setup(c => c.Get<InfraReplicationMappingDetailVm>(It.IsAny<RestRequest>()))
                .ReturnsAsync(expected);

            var result = await _fixture.Service.GetByReferenceId("id");

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetPaginatedInfraReplicationMapping_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<PaginatedResult<InfraReplicationMappingListVm>>();
            _fixture.MockClient.Setup(c => c.GetFromCache<PaginatedResult<InfraReplicationMappingListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
                .ReturnsAsync(expected);

            var query = _fixture.Fixture.Create<GetInfraReplicationMappingPaginatedListQuery>();
            var result = await _fixture.Service.GetPaginatedInfraReplicationMapping(query);

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetInfraReplicationMappingByDatabaseId_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<List<InfraReplicationMappingByDatabaseIdVm>>();
            _fixture.MockClient.Setup(c => c.Get<List<InfraReplicationMappingByDatabaseIdVm>>(It.IsAny<RestRequest>()))
                .ReturnsAsync(expected);

            var result = await _fixture.Service.GetInfraReplicationMappingByDatabaseId("dbId", "rmId");

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task CreateAsync_CallsClientWithCorrectRequest()
        {
            var response = _fixture.Fixture.Create<BaseResponse>();
            _fixture.MockClient.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(response);

            var command = _fixture.Fixture.Create<CreateInfraReplicationMappingCommand>();
            var result = await _fixture.Service.CreateAsync(command);

            Assert.Equal(response, result);
        }

        [Fact]
        public async Task UpdateAsync_CallsClientWithCorrectRequest()
        {
            var response = _fixture.Fixture.Create<BaseResponse>();
            _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(response);

            var command = _fixture.Fixture.Create<UpdateInfraReplicationMappingCommand>();
            var result = await _fixture.Service.UpdateAsync(command);

            Assert.Equal(response, result);
        }

        [Fact]
        public async Task DeleteAsync_CallsClientWithCorrectRequest()
        {
            var response = _fixture.Fixture.Create<BaseResponse>();
            _fixture.MockClient.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(response);

            var result = await _fixture.Service.DeleteAsync("id");

            Assert.Equal(response, result);
        }

        [Fact]
        public async Task GetInfraReplicationMappingByType_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<List<InfraReplicationMappingListVm>>();
            _fixture.MockClient.Setup(c => c.Get<List<InfraReplicationMappingListVm>>(It.IsAny<RestRequest>()))
                .ReturnsAsync(expected);

            var result = await _fixture.Service.GetInfraReplicationMappingByType("type");

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsExpectedResult()
        {
            var expected = _fixture.Fixture.Create<List<InfraReplicationMappingListVm>>();
            _fixture.MockClient.Setup(c => c.Get<List<InfraReplicationMappingListVm>>(It.IsAny<RestRequest>()))
                .ReturnsAsync(expected);

            var result = await _fixture.Service.GetTypeByDatabaseIdAndReplicationMasterId("dbid", "rmid", "type");

            Assert.Equal(expected, result);
        }
    }
}
