{"Version": 1, "WorkspaceRootPath": "D:\\Testcase\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\analyticscontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\analyticscontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\itresiliencyviewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\itresiliencyviewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\drift\\controllers\\driftparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\drift\\controllers\\driftparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\drift\\controllers\\driftprofilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\drift\\controllers\\driftprofilecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\drift\\controllers\\driftmanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\drift\\controllers\\driftmanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\drift\\controllers\\driftdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\drift\\controllers\\driftdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\serviceavailabilitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\serviceavailabilitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\resiliencymappingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\resiliencymappingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\itresiliencyviewcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\itresiliencyviewcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\customdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\customdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\customdashboardcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\customdashboardcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\dcmappingcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\dcmappingcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|d:\\testcase\\tests\\shared\\continuitypatrol.shared.tests\\mocks\\loggedinuserservicerepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|solutionrelative:tests\\shared\\continuitypatrol.shared.tests\\mocks\\loggedinuserservicerepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|d:\\testcase\\shared\\continuitypatrol.shared.core\\contracts\\identity\\iloggedinuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|solutionrelative:shared\\continuitypatrol.shared.core\\contracts\\identity\\iloggedinuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\cyberresiliency\\controllers\\airgapcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\cyberresiliency\\controllers\\airgapcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\businessviewnewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\businessviewnewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\analyticscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\analyticscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 15, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ItResiliencyViewControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\ItResiliencyViewControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\ItResiliencyViewControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\ItResiliencyViewControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\ItResiliencyViewControllerShould.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwCMAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:33:11.978Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DriftProfileController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftProfileController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftProfileController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftProfileController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftProfileController.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwB8AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:23:47.006Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DriftParameterController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftParameterController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftParameterController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftParameterController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftParameterController.cs", "ViewState": "AgIAAIUAAAAAAAAAAIAwwHIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:23:11.153Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DriftManagementController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftManagementController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftManagementController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftManagementController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftManagementController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:23:00.883Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DriftDashboardController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftDashboardController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftDashboardController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftDashboardController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Drift\\Controllers\\DriftDashboardController.cs", "ViewState": "AgIAAPMAAAAAAAAAAIA5wPsAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:22:36.98Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ServiceAvailabilityController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ServiceAvailabilityController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ServiceAvailabilityController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ServiceAvailabilityController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ServiceAvailabilityController.cs", "ViewState": "AgIAADsCAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:21:34.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ResiliencyMappingController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ResiliencyMappingController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ResiliencyMappingController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ResiliencyMappingController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ResiliencyMappingController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:21:24.793Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "DcMappingControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\DcMappingControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\DcMappingControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\DcMappingControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\DcMappingControllerShould.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:20:42.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "LoggedInUserServiceRepositoryMocks.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LoggedInUserServiceRepositoryMocks.cs", "RelativeDocumentMoniker": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LoggedInUserServiceRepositoryMocks.cs", "ToolTip": "D:\\Testcase\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LoggedInUserServiceRepositoryMocks.cs", "RelativeToolTip": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LoggedInUserServiceRepositoryMocks.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:18:47.92Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ILoggedInUserService.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Identity\\ILoggedInUserService.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Identity\\ILoggedInUserService.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Identity\\ILoggedInUserService.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Identity\\ILoggedInUserService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:13:07.8Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "AirGapControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\CyberResiliency\\Controllers\\AirGapControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\CyberResiliency\\Controllers\\AirGapControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\CyberResiliency\\Controllers\\AirGapControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\CyberResiliency\\Controllers\\AirGapControllerShould.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:05:08.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "BusinessViewNewControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:29:18.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "AnalyticsController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "ViewState": "AgIAAG4AAAAAAAAAAAAuwHsAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:26:37.553Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ItResiliencyViewController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:24:13.635Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "AnalyticsControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "ViewState": "AgIAADUAAAAAAAAAAAAiwEQAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:23:52.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "CustomDashboardControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAvwFwAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:19:34.505Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "CustomDashboardController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "ViewState": "AgIAACUAAAAAAAAAAAAawBMAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:18:33.272Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}]}]}