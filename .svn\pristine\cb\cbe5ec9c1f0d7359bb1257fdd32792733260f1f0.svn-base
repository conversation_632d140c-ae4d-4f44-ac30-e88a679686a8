﻿using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class ComponentTypeServiceTests : IClassFixture<ComponentTypeServiceFixture>
{
    private readonly ComponentTypeServiceFixture _fixture;
    private readonly Mock<IBaseClient> _clientMock;
    private readonly ComponentTypeService _service;

    public ComponentTypeServiceTests(ComponentTypeServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new ComponentTypeService(_clientMock.Object);
    }

    [Fact]
    public async Task GetComponentTypeById_ShouldReturnDetail()
    {
        // Arrange
        _clientMock.Setup(c => c.Get<ComponentTypeDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ComponentTypeDetailVm);

        // Act
        var result = await _service.GetComponentTypeById(_fixture.Id);

        // Assert
        Assert.Equal(_fixture.ComponentTypeDetailVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetComponentTypeListByName_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<ComponentTypeModel>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ComponentTypeModels);

        var result = await _service.GetComponentTypeListByName(_fixture.Name);

        Assert.Equal(_fixture.ComponentTypeModels, result);
    }

    [Fact]
    public async Task IsComponentTypeExist_ShouldReturnTrue()
    {
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsComponentTypeExist(_fixture.Name, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedComponentTypes_ShouldReturnPaginatedList()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<ComponentTypeListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedComponentTypes);

        var result = await _service.GetPaginatedComponentTypes(_fixture.Query);

        Assert.Equal(_fixture.PaginatedComponentTypes, result);
    }

    [Fact]
    public async Task GetComponentTypeList_ShouldReturnList()
    {
        // Arrange
        _clientMock.Setup(c => c.Get<List<ComponentTypeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ComponentTypeList);

        // Act
        var result = await _service.GetComponentTypeList();

        // Assert
        Assert.Equal(_fixture.ComponentTypeList, result);
        Assert.IsType<List<ComponentTypeListVm>>(result);
    }

}
