﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BusinessFunctionServiceFixture
{
    public CreateBusinessFunctionCommand CreateCommand { get; }
    public UpdateBusinessFunctionCommand UpdateCommand { get; }
    public GetBusinessFunctionPaginatedListQuery PaginatedQuery { get; }
    public List<BusinessFunctionListVm> ListVm { get; }
    public List<BusinessFunctionNameVm> NameVm { get; }
    public List<GetBusinessFunctionNameByBusinessServiceIdVm> ByServiceIdVm { get; }
    public PaginatedResult<BusinessFunctionListVm> PaginatedResult { get; }
    public BaseResponse BaseResponse { get; }

    public BusinessFunctionServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBusinessFunctionCommand>();
        UpdateCommand = fixture.Create<UpdateBusinessFunctionCommand>();
        PaginatedQuery = fixture.Create<GetBusinessFunctionPaginatedListQuery>();
        ListVm = fixture.CreateMany<BusinessFunctionListVm>().ToList();
        NameVm = fixture.CreateMany<BusinessFunctionNameVm>().ToList();
        ByServiceIdVm = fixture.CreateMany<GetBusinessFunctionNameByBusinessServiceIdVm>().ToList();
        PaginatedResult = fixture.Create<PaginatedResult<BusinessFunctionListVm>>();
        BaseResponse = fixture.Create<BaseResponse>();
    }
}