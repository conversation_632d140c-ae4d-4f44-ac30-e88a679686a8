using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing


public class CyberComponentMappingRepositoryTests : IClassFixture<CyberComponentMappingFixture>
{
    private readonly CyberComponentMappingFixture _cyberComponentMappingFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberComponentMappingRepository _repository;
    private readonly CyberComponentMappingRepository _repositoryNotParent;

    public CyberComponentMappingRepositoryTests(CyberComponentMappingFixture cyberComponentMappingFixture)
    {
        _cyberComponentMappingFixture = cyberComponentMappingFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberComponentMappingRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new CyberComponentMappingRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddCyberComponentMapping_Successfully()
    {
        // Arrange

        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        // Act
        await _repository.AddAsync(cyberComponentMapping);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(cyberComponentMapping.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal(cyberComponentMapping.Name, result.Name);

    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnCyberComponentMapping_WhenExists()
    {
        // Arrange
        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        await _repository.AddAsync(cyberComponentMapping);

        // Act
        var result = await _repository.GetByReferenceIdAsync(cyberComponentMapping.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberComponentMapping.ReferenceId, result.ReferenceId);
        Assert.Equal(cyberComponentMapping.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("eb01b17e-2ed8-4d79-8ea0-3a9ac38c899d");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateCyberComponentMapping_Successfully()
    {
        // Arrange
        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        cyberComponentMapping.Name = "Updated Mapping";

        await _repository.AddAsync(cyberComponentMapping);
        var result = await _repository.GetByReferenceIdAsync(cyberComponentMapping.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("Updated Mapping", result.Name);

    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        // Arrange
        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        await _repository.AddAsync(cyberComponentMapping);

        // Act
        cyberComponentMapping.IsActive = false;
         _dbContext.CyberComponentMappings.Update(cyberComponentMapping);
        _dbContext.SaveChanges();
        // Assert
        var result = await _repository.GetByReferenceIdAsync(cyberComponentMapping.ReferenceId);
        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        await _repository.AddAsync(cyberComponentMapping);

        // Act
        var result = await _repository.IsNameExist(cyberComponentMapping.Name, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingList;
        var name = cyberComponentMappings[0].Name;

        await _repository.AddRangeAsync(cyberComponentMappings);

        // Act
        var result = await _repository.IsNameExist(name, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberComponentMapping = _cyberComponentMappingFixture.CyberComponentMappingDto;

        await _repository.AddAsync(cyberComponentMapping);

        // Act
        var result = await _repository.IsNameExist(cyberComponentMapping.Name, cyberComponentMapping.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingList;
        

        await _repository.AddRangeAsync(cyberComponentMappings);

        // Act
        var result = await _repository.IsNameExist(cyberComponentMappings[0].Name, cyberComponentMappings[1].ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist(null, "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("", "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsWhitespace()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("   ", "valid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveMappings_WhenIsParent()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingList.Take(3).ToList();
        cyberComponentMappings[2].IsActive = false;
        _dbContext.CyberComponentMappings.AddRange(cyberComponentMappings);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active mappings
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredMappings_WhenIsNotParent()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingList.Take(3).ToList();
        cyberComponentMappings.ForEach(x=> x.IsActive = true);  
        await _repositoryNotParent.AddRangeAsync(cyberComponentMappings);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // Only child company mappings

        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingPaginationList;
        

        await _repository.AddRangeAsync(cyberComponentMappings);

        var specification = new CyberComponentMappingFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
        Assert.True(result.TotalCount >= 15);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingPaginationList;
        cyberComponentMappings[0].Name = "Filtered1";
        cyberComponentMappings[1].Name = "Filtered2";
        await _repository.AddRangeAsync(cyberComponentMappings);

        var specification = new CyberComponentMappingFilterSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.Name));
    }

    #endregion

   
    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var cyberComponentMappings = _cyberComponentMappingFixture.CyberComponentMappingPaginationList;
       

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(cyberComponentMappings);
        var initialCount = cyberComponentMappings.Count;

        var toUpdate = cyberComponentMappings.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedMappingName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = cyberComponentMappings.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedMappingName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
