﻿using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetByServerTypeId;
using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class ServerSubTypeServiceTests : IClassFixture<ServerSubTypeServiceFixture>
{
    private readonly ServerSubTypeServiceFixture _fixture;

    public ServerSubTypeServiceTests(ServerSubTypeServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        var id = _fixture.Fixture.Create<string>();

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetServerSubTypeById_Should_Return_DetailVm()
    {
        var id = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<ServerSubTypeDetailVm>();

        _fixture.ClientMock
            .Setup(x => x.Get<ServerSubTypeDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerSubTypeById(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetPaginatedServerSubTypes_Should_Return_PaginatedResult()
    {
        var query = _fixture.Fixture.Create<GetServerSubTypePaginatedListQuery>();
        var expected = _fixture.Fixture.Create<PaginatedResult<ServerSubTypeListVm>>();

        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<ServerSubTypeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetPaginatedServerSubTypes(query);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerSubTypeList_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerSubTypeListVm>>();

        _fixture.ClientMock
            .Setup(x => x.Get<List<ServerSubTypeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerSubTypeList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerSubTypeByTypeId_Should_Return_List()
    {
        var id = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<GetServerSubTypeByServerTypeIdVm>>();

        _fixture.ClientMock
            .Setup(x => x.Get<List<GetServerSubTypeByServerTypeIdVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerSubTypeByTypeId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsServerSubTypeExist_Should_Return_True()
    {
        var name = _fixture.Fixture.Create<string>();
        var id = _fixture.Fixture.Create<string>();

        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsServerSubTypeExist(name, id);

        Assert.True(result);
    }
}
