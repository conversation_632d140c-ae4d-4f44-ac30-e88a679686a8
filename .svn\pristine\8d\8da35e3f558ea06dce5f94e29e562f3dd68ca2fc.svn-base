﻿using ContinuityPatrol.Application.Features.User.Events.Update;
using ContinuityPatrol.Application.Features.User.Events.UpdateSendEmail;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.User.Commands.Update;

public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, UpdateUserResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserRepository _userRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IUserRoleRepository _userRoleRepository;   
    public UpdateUserCommandHandler(IMapper mapper, IUserRepository userRepository,
        IUserInfoRepository userInfoRepository, IPublisher publisher,
        IUserInfraObjectRepository userInfraObjectRepository,ILoggedInUserService loggedInUserService,IUserRoleRepository userRoleRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _userInfoRepository = userInfoRepository;
        _publisher = publisher;
        _userInfraObjectRepository = userInfraObjectRepository;
        _loggedInUserService = loggedInUserService;
        _userRoleRepository = userRoleRepository;
    }

    public async Task<UpdateUserResponse> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdateUser = await _userRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdateUser == null)
            throw new NotFoundException(nameof(Domain.Entities.User), request.Id);

        var previousRole = eventToUpdateUser.RoleName;

        request.UserInfoCommand.UserId = request.Id;

        var eventToUpdateUserInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.Id);

        if (eventToUpdateUserInfo == null)
            throw new NotFoundException(nameof(Domain.Entities.User), request.Id);

        var eventToUpdateUserInfraObject = await _userInfraObjectRepository.GetUserInfraObjectByUserIdAsync(request.Id);

        _mapper.Map(request, eventToUpdateUser, typeof(UpdateUserCommand), typeof(Domain.Entities.User));

        _mapper.Map(request.UserInfoCommand, eventToUpdateUserInfo, typeof(UpdateUserCommand),
            typeof(Domain.Entities.UserInfo));
        await _userRepository.UpdateAsync(eventToUpdateUser);

        await _userInfoRepository.UpdateAsync(eventToUpdateUserInfo);

        if (request.UserInfraObjectCommand != null)
        {
            _mapper.Map(request.UserInfraObjectCommand, eventToUpdateUserInfraObject);

            await _userInfraObjectRepository.UpdateAsync(eventToUpdateUserInfraObject);
        }

        var response = new UpdateUserResponse
        {
            Message = Message.Update(nameof(Domain.Entities.User), eventToUpdateUser!.LoginName),

            UserId = eventToUpdateUser.ReferenceId
        };

        await _publisher.Publish(new UserUpdatedEvent { UserName = eventToUpdateUser.LoginName }, cancellationToken);


        if (eventToUpdateUser.RoleName.ToLower() != previousRole.ToLower())
        {
            var userRole = await _userRoleRepository.GetByReferenceIdAsync(_loggedInUserService?.Role);

            await _publisher.Publish(
                new UpdateSendEmailEvent
                {
                    UserId = eventToUpdateUser.ReferenceId,
                    IsPreferredMode = request.UserInfoCommand.IsPreferredMode,
                    Email = request.UserInfoCommand.Email,
                    LoginName = request.LoginName,
                    UserName = request.UserInfoCommand.UserName,
                    CompanyName = request.CompanyName,
                    LoginType = request.LoginType,
                    PreviouseRole= previousRole,
                    CurrentRole = request.RoleName,
                    ChangedByUserRole = userRole.Role,
                    ChangedByUserName= _loggedInUserService!.LoginName
                }, cancellationToken);
        }
        
        return response;
    }
}