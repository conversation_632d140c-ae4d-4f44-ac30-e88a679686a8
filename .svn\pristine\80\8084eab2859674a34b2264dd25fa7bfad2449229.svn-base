﻿using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class DRCalendarServiceTests : IClassFixture<DRCalendarServiceFixture>
{
    private readonly DRCalendarServiceFixture _fixture;

    public DRCalendarServiceTests(DRCalendarServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        const string id = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetDrCalendarById_Should_Return_Detail()
    {
        const string id = "calendar-id";

        _fixture.ClientMock
            .Setup(x => x.Get<DrCalendarDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetDrCalendarById(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetDrCalenderList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<DrCalendarActivityListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ActivityList);

        var result = await _fixture.Service.GetDrCalenderList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.ActivityList, result);
    }

    [Fact]
    public async Task GetPaginatedDrCalendar_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<DrCalendarActivityListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedDrCalendar(_fixture.PaginatedQuery);

        Assert.NotNull(result);
        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetDrCalendarDrillEvents_Should_Return_Count()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<GetUpcomingDrillCountVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DrillCountVm);

        var result = await _fixture.Service.GetDrCalendarDrillEvents();

        Assert.NotNull(result);
        Assert.Equal(_fixture.DrillCountVm, result);
    }

    [Fact]
    public async Task IsDrCalendarNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsDrCalendarNameExist("TestActivity", "abc-123", DateTime.UtcNow);

        Assert.True(result);
    }
}
