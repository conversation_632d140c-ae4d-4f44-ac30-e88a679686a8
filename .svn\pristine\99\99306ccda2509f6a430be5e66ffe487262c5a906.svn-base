﻿using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class CompanyServiceTests : IClassFixture<CompanyServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly CompanyService _service;
    private readonly CompanyServiceFixture _fixture;

    public CompanyServiceTests(CompanyServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new CompanyService(_clientMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCompanyCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCompanyCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("company-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetCompanies_ShouldReturnCompanyList()
    {
        _clientMock.Setup(c => c.Get<List<CompanyListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.CompanyList);

        var result = await _service.GetCompanies();

        Assert.Equal(_fixture.CompanyList, result);
    }

    [Fact]
    public async Task GetCompanyById_ShouldReturnDetail()
    {
        _clientMock.Setup(c => c.Get<CompanyDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.CompanyDetail);

        var result = await _service.GetCompanyById("company-id");

        Assert.Equal(_fixture.CompanyDetail, result);
    }

    [Fact]
    public async Task GetCompanyNamesOnLogin_ShouldReturnNames()
    {
        _clientMock.Setup(c => c.GetFromCache<List<CompanyNameVm>>(It.IsAny<RestRequest>(), "GetCompanyNamesOnLogin"))
                   .ReturnsAsync(_fixture.CompanyNames);

        var result = await _service.GetCompanyNamesOnLogin();

        Assert.Equal(_fixture.CompanyNames, result);
    }

    [Fact]
    public async Task GetPaginatedCompanies_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<CompanyListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedCompanies);

        var result = await _service.GetPaginatedCompanies(_fixture.PaginatedListQuery);

        Assert.Equal(_fixture.PaginatedCompanies, result);
    }

    [Fact]
    public async Task CreateDefaultCompany_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateDefaultCompany(_fixture.CreateDefaultCompanyCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task IsCompanyNameExist_ShouldReturnTrue()
    {
        // Arrange
        string name = "TestCompany";
        string id = "123";
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.IsCompanyNameExist(name, id);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCompanyDisplayNameExist_ShouldReturnFalse()
    {
        // Arrange
        string displayName = "TestDisplay";
        string id = "456";
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.IsCompanyDisplayNameExist(displayName, id);

        // Assert
        Assert.False(result);
    }

}
