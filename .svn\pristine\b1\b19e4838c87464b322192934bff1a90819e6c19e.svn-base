﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByLicenseKey;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByUserName;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class DatabaseServiceTests : IClassFixture<DatabaseServiceFixture>
{
    private readonly DatabaseServiceFixture _fixture;

    public DatabaseServiceTests(DatabaseServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task SaveAllDatabase_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<SaveAllDatabaseCommand>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.SaveAllDatabase(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnList()
    {
        var expected = _fixture.Fixture.Create<List<DatabaseNameVm>>();
        _fixture.ClientMock.Setup(c => c.GetFromCache<List<DatabaseNameVm>>(It.IsAny<RestRequest>(), "GetDatabaseNames")).ReturnsAsync(expected);

        var result = await _fixture.Service.GetDatabaseNames();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsDatabaseNameExist_ShouldReturnBool()
    {
        var expected = _fixture.Fixture.Create<bool>();
        var name = _fixture.Fixture.Create<string>();
        var id = _fixture.Fixture.Create<string>();

        _fixture.ClientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.IsDatabaseNameExist(name, id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDatabasePaginatedList_ShouldReturnPaginatedResult()
    {
        var query = _fixture.Fixture.Create<GetDatabasePaginatedListQuery>();
        var expected = _fixture.Fixture.Create<PaginatedResult<DatabaseListVm>>();

        _fixture.ClientMock.Setup(c => c.Get<PaginatedResult<DatabaseListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.GetDatabasePaginatedList(query);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<CreateDatabaseCommand>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.CreateAsync(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<UpdateDatabaseCommand>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.UpdateAsync(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        var id = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        var id = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<DatabaseDetailVm>();

        _fixture.ClientMock.Setup(c => c.Get<DatabaseDetailVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.GetByReferenceId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByLicenseKey_ShouldReturnList()
    {
        var licenseId = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<GetDatabaseByLicenseKeyListVm>>();

        _fixture.ClientMock.Setup(c => c.Get<List<GetDatabaseByLicenseKeyListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _fixture.Service.GetByLicenseKey(licenseId);

        Assert.Equal(expected, result);
    }
    [Fact]
    public async Task DatabaseTestConnection_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<DatabaseTestConnectionCommand>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.DatabaseTestConnection(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByDatabaseType_ShouldReturnList()
    {
        var databaseTypeId = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<DatabaseTypeVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<DatabaseTypeVm>>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByDatabaseType(databaseTypeId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByServerId_ShouldReturnList()
    {
        var serverId = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<GetDatabaseByServerIdVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<GetDatabaseByServerIdVm>>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByServerId(serverId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByType_ShouldReturnList()
    {
        var type = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<GetDatabaseByTypeVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<GetDatabaseByTypeVm>>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByType(type);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDatabaseByUserName_ShouldReturnList()
    {
        var userName = _fixture.Fixture.Create<string>();
        var databaseTypeId = _fixture.Fixture.Create<string>();
        var expected = _fixture.Fixture.Create<List<GetDatabaseByUserNameVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<GetDatabaseByUserNameVm>>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.GetDatabaseByUserName(userName, databaseTypeId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDatabaseList_ShouldReturnList()
    {
        var expected = _fixture.Fixture.Create<List<DatabaseListVm>>();

        _fixture.ClientMock.Setup(x => x.GetFromCache<List<DatabaseListVm>>(It.IsAny<RestRequest>(), "DatabaseList"))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.GetDatabaseList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task SaveAsDatabase_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<SaveAsDatabaseCommand>();
        var expected = _fixture.Fixture.Create<BaseResponse>();

        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.SaveAsDatabase(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateDatabaseFormVersion_ShouldReturnBaseResponse()
    {
        var command = _fixture.Fixture.Create<UpdateDatabaseVersionCommand>();
        var expected = _fixture.Fixture.Create<UpdateDatabasePasswordResponse>();

        _fixture.ClientMock.Setup(x => x.Put<UpdateDatabasePasswordResponse>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.UpdateDatabaseFormVersion(command);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task UpdateDatabasePassword_ShouldReturnResponse()
    {
        var command = _fixture.Fixture.Create<UpdateDatabasePasswordCommand>();
        var expected = _fixture.Fixture.Create<UpdateDatabasePasswordResponse>();

        _fixture.ClientMock.Setup(x => x.Put<UpdateDatabasePasswordResponse>(It.IsAny<RestRequest>()))
                           .ReturnsAsync(expected);

        var result = await _fixture.Service.UpdateDatabasePassword(command);

        Assert.Equal(expected, result);
    }
}
