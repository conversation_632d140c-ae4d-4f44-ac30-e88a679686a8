﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class IncidentManagementSummaryServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public IncidentManagementSummaryService Service { get; }

    public CreateIncidentManagementSummaryCommand CreateCommand { get; }
    public UpdateIncidentManagementSummaryCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public IncidentManagementSummaryDetailVm DetailVm { get; }
    public List<IncidentManagementSummaryListVm> ListVm { get; }

    public IncidentManagementSummaryServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new IncidentManagementSummaryService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateIncidentManagementSummaryCommand>();
        UpdateCommand = Fixture.Create<UpdateIncidentManagementSummaryCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<IncidentManagementSummaryDetailVm>();
        ListVm = Fixture.CreateMany<IncidentManagementSummaryListVm>(3).ToList();
    }
}