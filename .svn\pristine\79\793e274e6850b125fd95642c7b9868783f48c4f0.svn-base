using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetFailurePrediction;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowPredictionFixture : IDisposable
{
    // Main entity
    public List<WorkflowPrediction> WorkflowPredictionPaginationList { get; set; }
    public List<WorkflowPrediction> WorkflowPredictionList { get; set; }
    public WorkflowPrediction WorkflowPredictionDto { get; set; }

    // Related entities for failure prediction
    public List<WorkflowHistoricalFailureRateView> WorkflowHistoricalFailureRateViewList { get; set; }
    public List<WorkflowRTOBreachRiskView> WorkflowRTOBreachRiskViewList { get; set; }
    public List<WorkflowLastFailureRecencyView> WorkflowLastFailureRecencyViewList { get; set; }
    public List<WorkflowInfrastructureHealthView> WorkflowInfrastructureHealthViewList { get; set; }
    public List<WorkflowExecutionTimeDeviationView> WorkflowExecutionTimeDeviationViewList { get; set; }
    public List<WorkflowRecentConfigurationChangesView> WorkflowRecentConfigurationChangesViewList { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowPredictionFixture()
    {
        var fixture = new Fixture();
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // Create main entity data
        WorkflowPredictionList = new List<WorkflowPrediction>();
        for (int i = 1; i <= 5; i++)
        {
            WorkflowPredictionList.Add(new WorkflowPrediction
            {Id= i,
                ReferenceId = $"WF_PRED_{i}",
                ActionName= $"Action {i}",
                ActionId = $"ACTION_{i}",
                NextPossibleId = $"NEXT_{i}",
                Count = i * 10,
                NextPossibleActionName= $"Next Action {i}",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-i)
            });
        }

        WorkflowPredictionPaginationList = new List<WorkflowPrediction>();
        for (int i = 1; i <= 20; i++)
        {
            WorkflowPredictionPaginationList.Add(new WorkflowPrediction
            {

                ReferenceId = $"WF_PRED_{i}",
                ActionName = $"Action {i}",
                ActionId = $"ACTION_{i}",
                NextPossibleId = $"NEXT_{i}",
                Count = i * 10,
                NextPossibleActionName = $"Next Action {i}",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-i)
            });
        }

        WorkflowPredictionDto = new WorkflowPrediction
        {

            ReferenceId =Guid.NewGuid().ToString(),
            ActionName = $"Action",
            ActionId = $"ACTION",
            NextPossibleId = $"NEXT",
            Count =  10,
            NextPossibleActionName = $"Next Action",
            IsActive = true,
            CreatedDate = DateTime.Now
            
        };

        // Create related entity data for failure prediction
        WorkflowHistoricalFailureRateViewList = new List<WorkflowHistoricalFailureRateView>();
        WorkflowRTOBreachRiskViewList = new List<WorkflowRTOBreachRiskView>();
        WorkflowLastFailureRecencyViewList = new List<WorkflowLastFailureRecencyView>();
        WorkflowInfrastructureHealthViewList = new List<WorkflowInfrastructureHealthView>();
        WorkflowExecutionTimeDeviationViewList = new List<WorkflowExecutionTimeDeviationView>();
        WorkflowRecentConfigurationChangesViewList = new List<WorkflowRecentConfigurationChangesView>();

        // Create consistent data across all related entities
        for (int i = 1; i <= 5; i++)
        {
            string referenceId = $"WORKFLOW_{i}";
            string workflowName = $"Workflow {i}";

            WorkflowHistoricalFailureRateViewList.Add(new WorkflowHistoricalFailureRateView
            {
                ReferenceId = referenceId,
                WorkflowName = workflowName,
                WeightagePoints = 10 + i
            });

            WorkflowRTOBreachRiskViewList.Add(new WorkflowRTOBreachRiskView
            {
                ReferenceId = referenceId,
                WorkflowName = workflowName,
                WeightagePoint = 5 + i
            });

            WorkflowLastFailureRecencyViewList.Add(new WorkflowLastFailureRecencyView
            {
                ReferenceId = referenceId,
                WorkflowName = workflowName,
                WeightagePoints = 8 + i
            });

            WorkflowInfrastructureHealthViewList.Add(new WorkflowInfrastructureHealthView
            {
                ReferenceId = referenceId,
                WorkflowName = workflowName,
                Weightage = 7 + i
            });

            WorkflowExecutionTimeDeviationViewList.Add(new WorkflowExecutionTimeDeviationView
            {
                ReferenceId = referenceId,
                WorkflowName = workflowName,
                WeightagePoints = 6 + i
            });

            WorkflowRecentConfigurationChangesViewList.Add(new WorkflowRecentConfigurationChangesView
            {
                WorkflowId = referenceId,
                WorkflowName = workflowName,
                TotalRiskScore = 4 + i
            });
        }

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
