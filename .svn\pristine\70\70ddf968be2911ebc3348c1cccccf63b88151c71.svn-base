﻿using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class InfraObjectServiceFixture
{
    public IFixture Fixture { get; } = new Fixture();
    public Mock<IBaseClient> ClientMock { get; } = new Mock<IBaseClient>();
    public InfraObjectService Service { get; }

    public InfraObjectServiceFixture()
    {
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        Service = new InfraObjectService(ClientMock.Object);
    }
}