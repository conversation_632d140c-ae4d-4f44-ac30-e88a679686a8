using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class OracleRACMonitorLogsRepositoryTests : IClassFixture<OracleRACMonitorLogsFixture>
{
    private readonly OracleRACMonitorLogsFixture _oracleRACMonitorLogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly OracleRACMonitorLogsRepository _repository;
    private readonly OracleRACMonitorLogsRepository _repositoryNotParent;
    private readonly Mock<IConfiguration> _configMock;

    public OracleRACMonitorLogsRepositoryTests(OracleRACMonitorLogsFixture oracleRACMonitorLogsFixture)
    {
        _oracleRACMonitorLogsFixture = oracleRACMonitorLogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _configMock = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new OracleRACMonitorLogsRepository(_dbContext, _configMock.Object);
        _repositoryNotParent = new OracleRACMonitorLogsRepository(_dbContext, _configMock.Object);
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddOracleRACMonitorLog_Successfully()
    {
        // Arrange
        var oracleRACMonitorLog = new OracleRACMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };
        // Act
        await _repository.AddAsync(oracleRACMonitorLog);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);
        Assert.NotNull(result);
       
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnOracleRACMonitorLog_WhenExists()
    {
        // Arrange
        var oracleRACMonitorLog = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsDto;
        await _repository.AddAsync(oracleRACMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(oracleRACMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(oracleRACMonitorLog.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("eb01b17e-2ed8-4d79-8ea0-3a9ac38c899d");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateOracleRACMonitorLog_Successfully()
    {
        // Arrange
        var oracleRACMonitorLog = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsDto;

        await _repository.AddAsync(oracleRACMonitorLog);

        // Act
        oracleRACMonitorLog.InfraObjectName = "Updated message";
        oracleRACMonitorLog.ConfiguredRPO = "15";
        await _repository.UpdateAsync(oracleRACMonitorLog);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("Updated message", result.InfraObjectName);

    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        // Arrange
        var oracleRACMonitorLog = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsDto;

        await _repository.AddAsync(oracleRACMonitorLog);


        // Act
        oracleRACMonitorLog.IsActive = false;

        await  _repository.UpdateAsync(oracleRACMonitorLog);


        // Assert
        var result = await _repository.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);
        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLogs_WhenIsParent()
    {
        // Arrange

        var oracleRACMonitorLogs = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsList;

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count); // Only active logs
        Assert.All(result, x => Assert.True(x.IsActive));
    }


    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var oracleRACMonitorLogs = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsPaginationList;
      

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        var specification = new OracleRacMonitorLogsFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "InfraObjectName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
        Assert.True(result.TotalCount >= 15);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var oracleRACMonitorLogs = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsList;
        oracleRACMonitorLogs[0].InfraObjectName = "oracleRACFilteredMonitorLogs";
        oracleRACMonitorLogs[1].InfraObjectName = "oracleFilteredMonitorLogs";

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        var specification = new OracleRacMonitorLogsFilterSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "InfraObjectName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.InfraObjectName));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery()
    {
        // Arrange
 
        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>
        {
            new OracleRACMonitorLogs
            {
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new OracleRACMonitorLogs
            {
                
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        _repository.AddRangeAsync(oracleRACMonitorLogs).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion

    #region Parent/Child Company Logic Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnLog_WhenIsParent()
    {
        // Arrange
        var oracleRACMonitorLog = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsDto;

        await _repository.AddAsync(oracleRACMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(oracleRACMonitorLog.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnLog_WhenIsNotParentAndSameCompany()
    {
        // Arrange
        var oracleRACMonitorLog = _oracleRACMonitorLogsFixture.OracleRACMonitorLogsDto;

        await _repositoryNotParent.AddAsync(oracleRACMonitorLog);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(oracleRACMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(oracleRACMonitorLog.ReferenceId, result.ReferenceId);

    }

   
    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>
        {
            new OracleRACMonitorLogs
            {
                InfraObjectId = infraObjectId,
             
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new OracleRACMonitorLogs
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(2),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new OracleRACMonitorLogs
            {
                InfraObjectId = infraObjectId,
               
                CreatedDate = baseDate.AddDays(10), // Outside range
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new OracleRACMonitorLogs
            {
                InfraObjectId = "OTHER_INFRA",
   
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _dbContext.OracleRacMonitorLogs.AddRangeAsync(oracleRACMonitorLogs);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.InfraObjectId == oracleRACMonitorLogs[0].InfraObjectId);
        Assert.Contains(result, x => x.InfraObjectId == oracleRACMonitorLogs[1].InfraObjectId);
        Assert.DoesNotContain(result, x => x.InfraObjectId =="dsfdsf" );
        Assert.DoesNotContain(result, x => x.InfraObjectId == "OTHER_INFRA");
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoLogsInRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var futureStartDate = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddYears(2).ToString("yyyy-MM-dd");

        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>
        {
            new OracleRACMonitorLogs
            {
                InfraObjectId = infraObjectId,
          
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, futureStartDate, futureEndDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogsOfSpecificType()
    {
        // Arrange
        var logType = "ERROR";
        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>
        {
            new OracleRACMonitorLogs
            {
                Type = logType,
               
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new OracleRACMonitorLogs
            {
                Type = logType,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new OracleRACMonitorLogs
            {
                Type = "INFO",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        // Act
        var result = await _repository.GetDetailByType(logType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(logType, x.Type));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeNotExists()
    {
        // Arrange
        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>
        {
            new OracleRACMonitorLogs
            {
                Type = "INFO",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _repository.AddRangeAsync(oracleRACMonitorLogs);

        // Act
        var result = await _repository.GetDetailByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleNullType()
    {
        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var oracleRACMonitorLogs = new List<OracleRACMonitorLogs>();
        for (int i = 1; i <= 5; i++)
        {
            oracleRACMonitorLogs.Add(new OracleRACMonitorLogs
            {
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            });
        }

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(oracleRACMonitorLogs);
        var initialCount = oracleRACMonitorLogs.Count;

        var toUpdate = oracleRACMonitorLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.WorkflowName = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = oracleRACMonitorLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.WorkflowName == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
