﻿using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class IncidentDailyServiceTests : IClassFixture<IncidentDailyServiceFixture>
{
    private readonly IncidentDailyServiceFixture _fixture;

    public IncidentDailyServiceTests(IncidentDailyServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateIncidentDaily_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateIncidentDaily(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateIncidentLogs_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateIncidentLogs(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteIncidentDaily_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteIncidentDaily("test-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetIncidentDailyById_ShouldReturnDetailVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<IncidentDailyDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetIncidentDailyById("incident-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetList_ShouldReturnListVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<IncidentDailyListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetPaginatedIncidentDaily_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<IncidentDailyListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedIncidentDaily(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
