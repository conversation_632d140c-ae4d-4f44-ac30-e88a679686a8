﻿using AutoFixture;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class ReplicationMasterServiceFixture
{
    public readonly Mock<IBaseClient> ClientMock;
    public readonly ReplicationMasterService Service;
    public readonly Fixture FixtureGen;

    public readonly CreateReplicationMasterCommand CreateCommand;
    public readonly UpdateReplicationMasterCommand UpdateCommand;
    public readonly BaseResponse BaseResponse;
    public readonly ReplicationMasterDetailVm DetailVm;
    public readonly List<ReplicationMasterListVm> ListVm;
    public readonly List<ReplicationMasterNameVm> NameList;
    public readonly List<GetByInfraMasterNameVm> ByInfraMasterList;
    public readonly PaginatedResult<ReplicationMasterListVm> PaginatedList;
    public readonly GetReplicationMasterPaginatedListQuery PaginatedQuery;

    public ReplicationMasterServiceFixture()
    {
        FixtureGen = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new ReplicationMasterService(ClientMock.Object);

        CreateCommand = FixtureGen.Create<CreateReplicationMasterCommand>();
        UpdateCommand = FixtureGen.Create<UpdateReplicationMasterCommand>();
        BaseResponse = FixtureGen.Create<BaseResponse>();
        DetailVm = FixtureGen.Create<ReplicationMasterDetailVm>();
        ListVm = FixtureGen.CreateMany<ReplicationMasterListVm>(3).ToList();
        NameList = FixtureGen.CreateMany<ReplicationMasterNameVm>(3).ToList();
        ByInfraMasterList = FixtureGen.CreateMany<GetByInfraMasterNameVm>(2).ToList();
        PaginatedList = FixtureGen.Create<PaginatedResult<ReplicationMasterListVm>>();
        PaginatedQuery = FixtureGen.Create<GetReplicationMasterPaginatedListQuery>();
    }
}
