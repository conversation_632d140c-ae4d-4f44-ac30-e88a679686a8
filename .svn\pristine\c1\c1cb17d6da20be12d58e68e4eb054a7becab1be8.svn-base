﻿using AutoFixture;
using AutoFixture.AutoMoq;
using Moq;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Update;
using ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SmsConfigurationModel;
using ContinuityPatrol.Shared.Core.Responses;

public class SmsConfigurationServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SmsConfigurationService Service { get; }

    public CreateSmsConfigurationCommand CreateCommand { get; }
    public UpdateSmsConfigurationCommand UpdateCommand { get; }

    public BaseResponse BaseResponse { get; }
    public List<SmsConfigurationListVm> SmsConfigList { get; }
    public SmsConfigurationDetailVm SmsConfigDetail { get; }
    public string Id { get; }

    public SmsConfigurationServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SmsConfigurationService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSmsConfigurationCommand>();
        UpdateCommand = fixture.Create<UpdateSmsConfigurationCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        SmsConfigList = fixture.Create<List<SmsConfigurationListVm>>();
        SmsConfigDetail = fixture.Create<SmsConfigurationDetailVm>();
        Id = fixture.Create<string>();
    }
}