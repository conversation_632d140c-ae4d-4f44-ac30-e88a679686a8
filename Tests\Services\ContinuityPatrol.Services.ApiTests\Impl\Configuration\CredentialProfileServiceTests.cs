﻿using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Impl.Configuration.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class CredentialProfileServiceTests : IClassFixture<CredentialProfileServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly CredentialProfileService _service;
    private readonly CredentialProfileServiceFixture _fixture;

    public CredentialProfileServiceTests(CredentialProfileServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new CredentialProfileService(_clientMock.Object);
    }

    [Fact]
    public async Task GetCredentialProfileList_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<CredentialProfileListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.CredentialProfileList);

        var result = await _service.GetCredentialProfileList();

        Assert.Equal(_fixture.CredentialProfileList, result);
    }

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<CredentialProfileNameVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.CredentialProfileNameList);

        var result = await _service.GetCredentialProfileNames();

        Assert.Equal(_fixture.CredentialProfileNameList, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        var id = "test-id";
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync(id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        var id = "test-id";
        _clientMock.Setup(c => c.Get<CredentialProfileDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId(id);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetPaginatedCredentialProfiles_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<CredentialProfileListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetPaginatedCredentialProfiles(_fixture.PaginatedListQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsServerNameExist_ShouldReturnBoolean()
    {
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsServerNameExist("ServerName", null);

        Assert.True(result);
    }
}