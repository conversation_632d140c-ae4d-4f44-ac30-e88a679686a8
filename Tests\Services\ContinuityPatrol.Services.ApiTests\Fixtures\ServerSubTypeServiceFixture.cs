﻿using ContinuityPatrol.Application.Features.ServerSubType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerSubType.Commands.Update;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

public class ServerSubTypeServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public ServerSubTypeService Service { get; }

    public BaseResponse BaseResponse { get; }
    public CreateServerSubTypeCommand CreateCommand { get; }
    public UpdateServerSubTypeCommand UpdateCommand { get; }

    public ServerSubTypeServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new ServerSubTypeService(ClientMock.Object);

        BaseResponse = Fixture.Create<BaseResponse>();
        CreateCommand = Fixture.Create<CreateServerSubTypeCommand>();
        UpdateCommand = Fixture.Create<UpdateServerSubTypeCommand>();
    }
}