﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BulkImportServiceFixture
{
    public CreateBulkImportCommand CreateCommand { get; }
    public NextBulkImportCommand NextCommand { get; }
    public RollBackBulkImportCommand RollbackCommand { get; }
    public BaseResponse BaseResponse { get; }

    public BulkImportServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBulkImportCommand>();
        NextCommand = fixture.Create<NextBulkImportCommand>();
        RollbackCommand = fixture.Create<RollBackBulkImportCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
    }
}