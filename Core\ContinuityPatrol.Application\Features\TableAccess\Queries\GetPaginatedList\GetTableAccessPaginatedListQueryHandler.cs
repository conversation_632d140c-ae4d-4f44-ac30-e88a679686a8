﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;

public class GetTableAccessPaginatedListQueryHandler : IRequestHandler<GetTableAccessPaginatedListQuery,
    PaginatedResult<TableAccessListVm>>
{
    private readonly IMapper _mapper;

    private readonly ITableAccessRepository _tableAccessRepository;

    public GetTableAccessPaginatedListQueryHandler(IMapper mapper, ITableAccessRepository tableAccessRepository)
    {
        _mapper = mapper;

        _tableAccessRepository = tableAccessRepository;
    }

    public async Task<PaginatedResult<TableAccessListVm>> Handle(GetTableAccessPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new TableAccessFilterSpecification(request.SearchString);

        var queryable = await _tableAccessRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var tableAccessesList =  _mapper.Map<PaginatedResult<TableAccessListVm>>(queryable);

        return tableAccessesList;
    }
}