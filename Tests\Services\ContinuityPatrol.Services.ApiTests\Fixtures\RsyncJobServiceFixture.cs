﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class RsyncJobServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public RsyncJobService Service { get; }

    public CreateRsyncJobCommand CreateCommand { get; }
    public UpdateRsyncJobCommand UpdateCommand { get; }
    public GetRsyncJobPaginatedQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public RsyncJobDetailVm DetailVm { get; }
    public List<RsyncJobListVm> ListVm { get; }
    public PaginatedResult<RsyncJobListVm> PaginatedResult { get; }

    public RsyncJobServiceFixture()
    {
        var fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new RsyncJobService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateRsyncJobCommand>();
        UpdateCommand = fixture.Create<UpdateRsyncJobCommand>();
        PaginatedQuery = fixture.Create<GetRsyncJobPaginatedQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<RsyncJobDetailVm>();
        ListVm = fixture.CreateMany<RsyncJobListVm>(3).ToList();
        PaginatedResult = fixture.Create<PaginatedResult<RsyncJobListVm>>();
    }
}