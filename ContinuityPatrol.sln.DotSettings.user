﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=51f923ec_002Dbb93_002D4cea_002Da7a5_002Df5c4bc844275/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="All tests from AirGapControllerShould.cs" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;Or&gt;&#xD;
    &lt;ProjectFile&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:Dashboard/d:Controllers/f:ServiceAvailabilityControllerShould.cs&lt;/ProjectFile&gt;&#xD;
    &lt;ProjectFile&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:Dashboard/d:Controllers/f:ResiliencyMappingControllerShould.cs&lt;/ProjectFile&gt;&#xD;
    &lt;ProjectFile&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:Dashboard/d:Controllers/f:ItResiliencyViewControllerShould.cs&lt;/ProjectFile&gt;&#xD;
    &lt;ProjectFile&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:Dashboard/d:Controllers/f:CustomDashboardControllerShould.cs&lt;/ProjectFile&gt;&#xD;
    &lt;ProjectFile&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:Dashboard/d:Controllers/f:AnalyticsControllerShould.cs&lt;/ProjectFile&gt;&#xD;
    &lt;ProjectFolder&gt;B473E785-F4D0-4E26-AA6A-B9BB2CF722DA/d:Areas/d:CyberResiliency/d:Controllers&lt;/ProjectFolder&gt;&#xD;
  &lt;/Or&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>