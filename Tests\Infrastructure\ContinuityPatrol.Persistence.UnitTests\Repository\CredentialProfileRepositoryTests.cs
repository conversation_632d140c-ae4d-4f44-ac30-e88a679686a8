using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing

public class CredentialProfileRepositoryTests : IClassFixture<CredentialProfileFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CredentialProfileRepository _repository;
    private readonly CredentialProfileRepository _repositoryNotParent;

    public CredentialProfileRepositoryTests(CredentialProfileFixture credentialProfileFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CredentialProfileRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new CredentialProfileRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;

        // Act
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Name, result.Name);
        Assert.Equal(credentialProfile.CredentialType, result.CredentialType);
        Assert.Single(_dbContext.CredentialProfiles);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        credentialProfile.Name = "UpdatedName";
        credentialProfile.CredentialType = "UpdatedType";

        // Act
        _dbContext.CredentialProfiles.Update(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedType", result.CredentialType);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        // Act
        credentialProfile.IsActive = false;

        _dbContext.CredentialProfiles.Update(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();
        // Act
        var result = await _repository.GetByIdAsync(credentialProfile.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Id, result.Id);
        Assert.Equal(credentialProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
   
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.ReferenceId, result.ReferenceId);
        Assert.Equal(credentialProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfiles.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ShouldReturnCredentialProfilesOfSpecificType()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile 
            { 
                Name = "Profile1",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile2",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile3",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetType(credentialType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(credentialType, x.CredentialType));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCredentialProfileNames Tests

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnNamesAndReferenceIds()
    {
        // Arrange
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile 
            { 
                Name = "Profile1",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile2",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnEmpty_WhenNoCredentialProfiles()
    {
        // Act
        var result = await _repository.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsCredentialProfileNameExist Tests

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "ExistingName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsCredentialProfileNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.IsCredentialProfileNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "SameName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Act
        var result = await _repository.IsCredentialProfileNameExist("SameName", credentialProfile.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsCredentialProfileNameUnique Tests

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "UniqueName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsCredentialProfileNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.IsCredentialProfileNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetCredentialProfileByType Tests

    [Fact]
    public async Task GetCredentialProfileByType_ShouldReturnProfilesOfSpecificType()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "Profile1",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile2",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile3",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = _repository.GetCredentialProfileByType(credentialType);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);
        Assert.All(resultList, x => Assert.Equal(credentialType, x.CredentialType));
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetCredentialProfileByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = _repository.GetCredentialProfileByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var credentoialProfile = _credentialProfileFixture.CredentialProfileList;
        var credentialProfile1 = credentoialProfile[0];
        var credentialProfile2 = credentoialProfile[1];

        credentialProfile2.Name = "DifferentName";

        // Act
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile1);
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();


        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.CredentialProfiles.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(credentialProfiles);
        var initialCount = credentialProfiles.Count;

        var toUpdate = credentialProfiles.Take(2).ToList();
        toUpdate.ForEach(x => x.CredentialType = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = credentialProfiles.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.CredentialType == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleCredentialTypeFiltering()
    {
        // Arrange
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "Profile1",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile2",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile3",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var sshProfiles = await _repository.GetType("SSH");
        var ftpProfiles = await _repository.GetType("FTP");

        // Assert
        Assert.Equal(2, sshProfiles.Count);
        Assert.Single(ftpProfiles);
        Assert.All(sshProfiles, x => Assert.Equal("SSH", x.CredentialType));
        Assert.All(ftpProfiles, x => Assert.Equal("FTP", x.CredentialType));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfilePaginationList;
        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnFilteredResults_WhenIsNotParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfilePaginationList;

        // Set some profiles to child company
        credentialProfiles.Take(5).ToList().ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");

        await _repositoryNotParent.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repositoryNotParent.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfilePaginationList;
        credentialProfiles[0].Name = "FilteredProfile";
        credentialProfiles[1].Name = "AnotherFilteredProfile";
        credentialProfiles[2].Name = "DifferentProfile";

        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.Name));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery_WhenIsParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        _repository.AddRangeAsync(credentialProfiles).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredQuery_WhenIsNotParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;

        // Set some profiles to child company
        credentialProfiles.Take(3).ToList().ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");

        _repositoryNotParent.AddRangeAsync(credentialProfiles).Wait();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion

    #region Parent/Child Company Logic Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllProfiles_WhenIsParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfiles.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredProfiles_WhenIsNotParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;

        // Set different company IDs
        credentialProfiles[0].CompanyId = "ChHILD_COMPANY_123"; // Should be included
        credentialProfiles[1].CompanyId = "OTHER_COMPANY_456"; // Should be excluded
        credentialProfiles[2].CompanyId = "ChHILD_COMPANY_123"; // Should be included

        await _repositoryNotParent.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnProfile_WhenIsParent()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _repository.AddAsync(credentialProfile);

        // Act
        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnProfile_WhenIsNotParentAndSameCompany()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(credentialProfile);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.ReferenceId, result.ReferenceId);
        Assert.Equal("ChHILD_COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIsNotParentAndDifferentCompany()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.CompanyId = "OTHER_COMPANY_456";
        await _repositoryNotParent.AddAsync(credentialProfile);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnAllNames_WhenIsParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfiles.Count, result.Count);
    }

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnFilteredNames_WhenIsNotParent()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;

        // Set different company IDs
        credentialProfiles[0].CompanyId = "ChHILD_COMPANY_123"; // Should be included
        credentialProfiles[1].CompanyId = "OTHER_COMPANY_456"; // Should be excluded
        credentialProfiles[2].CompanyId = "ChHILD_COMPANY_123"; // Should be included

        await _repositoryNotParent.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repositoryNotParent.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    #endregion

    #region Uncovered Methods Tests - 100% Coverage

    [Fact]
    public async Task GetByIdAsync_ShouldReturnProfile_WhenIsParentAndExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _repository.AddAsync(credentialProfile);

        // Act
        var result = await _repository.GetByIdAsync(credentialProfile.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Id, result.Id);
        Assert.Equal(credentialProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnProfile_WhenIsNotParentAndSameCompany()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(credentialProfile);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(credentialProfile.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Id, result.Id);
        Assert.Equal("ChHILD_COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenIsNotParentAndDifferentCompany()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.CompanyId = "OTHER_COMPANY_456";
        await _repositoryNotParent.AddAsync(credentialProfile);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(credentialProfile.Id);

        // Assert
        Assert.Null(result);
    }


    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldReturnPaginatedResults()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "SSH Profile 1",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "SSH Profile 2",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "FTP Profile",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Equal(credentialType, x.CredentialType));
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, specification, "NON_EXISTENT_TYPE", "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldApplySpecificationFilter()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "FilteredSSHProfile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "AnotherSSHProfile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "DifferentSSHProfile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("Filtered");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
        Assert.Contains("Filtered", result.Data.First().Name);
        Assert.Equal(credentialType, result.Data.First().CredentialType);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandlePagination()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>();

        for (int i = 1; i <= 15; i++)
        {
            credentialProfiles.Add(new CredentialProfile
            {
                Name = $"SSH Profile {i:D2}",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            });
        }

        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act - Get first page
        var firstPage = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", "asc");
        var secondPage = await _repository.GetCredentialProfileByType(2, 10, specification, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(firstPage);
        Assert.True(firstPage.Succeeded);
        Assert.Equal(10, firstPage.Data.Count);
        Assert.Equal(1, firstPage.CurrentPage);
        Assert.Equal(15, firstPage.TotalCount);

        Assert.NotNull(secondPage);
        Assert.True(secondPage.Succeeded);
        Assert.Equal(5, secondPage.Data.Count);
        Assert.Equal(2, secondPage.CurrentPage);
        Assert.Equal(15, secondPage.TotalCount);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleSorting()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "C Profile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "A Profile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "B Profile",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        var specification = new CredentialProfileFilterSpecification("");

        // Act - Sort ascending
        var ascResult = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", "asc");
        var descResult = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", "desc");

        // Assert
        Assert.NotNull(ascResult);
        Assert.True(ascResult.Succeeded);
        Assert.Equal(3, ascResult.Data.Count);
        Assert.Equal("A Profile", ascResult.Data.First().Name);

        Assert.NotNull(descResult);
        Assert.True(descResult.Succeeded);
        Assert.Equal(3, descResult.Data.Count);
        Assert.Equal("C Profile", descResult.Data.First().Name);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldThrowArgumentException_WhenTypeIsNull()
    {
        // Arrange
        var specification = new CredentialProfileFilterSpecification("");

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _repository.GetCredentialProfileByType(1, 10, specification, null, "Name", "asc"));
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldThrowArgumentException_WhenTypeIsEmpty()
    {
        // Arrange
        var specification = new CredentialProfileFilterSpecification("");

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _repository.GetCredentialProfileByType(1, 10, specification, "", "Name", "asc"));
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleNullSpecification()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfile = new CredentialProfile
        {
            Name = "SSH Profile",
            CredentialType = credentialType,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = CredentialProfileFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(credentialProfile);

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, null, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
        Assert.Equal(credentialType, result.Data.First().CredentialType);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleZeroPageSize()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfile = new CredentialProfile
        {
            Name = "SSH Profile",
            CredentialType = credentialType,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = CredentialProfileFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(credentialProfile);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 0, specification, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.NotEmpty(result.Data); // Should return empty when page size is 0
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleInvalidPageNumber()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfile = new CredentialProfile
        {
            Name = "SSH Profile",
            CredentialType = credentialType,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = CredentialProfileFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(credentialProfile);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(0, 10, specification, credentialType, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        // The behavior depends on the ToSortedPaginatedListAsync implementation
        // It should handle invalid page numbers gracefully
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleNullSortColumn()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfile = new CredentialProfile
        {
            Name = "SSH Profile",
            CredentialType = credentialType,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = CredentialProfileFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(credentialProfile);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, null, "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
    }

    [Fact]
    public async Task GetCredentialProfileByType_Paginated_ShouldHandleNullSortOrder()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfile = new CredentialProfile
        {
            Name = "SSH Profile",
            CredentialType = credentialType,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = CredentialProfileFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(credentialProfile);

        var specification = new CredentialProfileFilterSpecification("");

        // Act
        var result = await _repository.GetCredentialProfileByType(1, 10, specification, credentialType, "Name", null);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetType_ShouldThrowArgumentException_WhenTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetType(null));
    }

    [Fact]
    public async Task GetType_ShouldThrowArgumentException_WhenTypeIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetType(""));
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrowArgumentException_WhenIdIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetByReferenceIdAsync(null));
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrowArgumentException_WhenIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetByReferenceIdAsync(""));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldThrowArgumentException_WhenIdIsZeroOrNegative()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetByIdAsync(0));
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetByIdAsync(-1));
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldThrowArgumentException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsCredentialProfileNameExist(null, "valid-guid"));
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldThrowArgumentException_WhenNameIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsCredentialProfileNameExist("", "valid-guid"));
    }

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldThrowArgumentException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsCredentialProfileNameUnique(null));
    }

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldThrowArgumentException_WhenNameIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsCredentialProfileNameUnique(""));
    }

    [Fact]
    public void GetCredentialProfileByType_ShouldThrowArgumentException_WhenTypeIsNull()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => _repository.GetCredentialProfileByType((string)null));
    }

    [Fact]
    public void GetCredentialProfileByType_ShouldThrowArgumentException_WhenTypeIsEmpty()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => _repository.GetCredentialProfileByType(""));
    }

    #endregion
}
