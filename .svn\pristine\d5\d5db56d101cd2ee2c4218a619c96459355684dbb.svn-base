using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DatabaseRepositoryTests : IClassFixture<DatabaseFixture>,IClassFixture<InfraObjectFixture>,IClassFixture<BusinessServiceFixture>,IClassFixture<ComponentTypeFixture>,IClassFixture<ServerFixture>
{
    private readonly DatabaseFixture _databaseFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatabaseRepository _repository;
    private readonly DatabaseRepository _repositoryNotParent;
    private readonly InfraObjectRepository _infraObjectRepository;
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly ComponentTypeFixture _componentTypeFixture;
    private readonly ServerFixture _serverFixture;

    public DatabaseRepositoryTests(DatabaseFixture databaseFixture,InfraObjectFixture infraObjectFixture, BusinessServiceFixture businessServiceFixture,ComponentTypeFixture componentTypeFixture ,ServerFixture serverFixture)
    {
        _databaseFixture = databaseFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _infraObjectRepository = new InfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repository = new DatabaseRepository(_dbContext, DbContextFactory.GetMockUserService(), _infraObjectRepository);
        _repositoryNotParent = new DatabaseRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _infraObjectRepository);
        _businessServiceFixture = businessServiceFixture;
        _componentTypeFixture= componentTypeFixture;
        _serverFixture = serverFixture;
     

    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;

        // Act
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.Name, result.Name);
        Assert.Equal(database.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Databases);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        database.Name = "UpdatedDatabaseName";

        // Act
        _dbContext.Databases.Update(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDatabaseName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        // Act
        database.IsActive = false;

        _dbContext.Databases.Update(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsParent()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        var addedEntity = await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsNotParent()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.CompanyId = "ChHILD_COMPANY_123";
        var addedEntity = await _repositoryNotParent.AddAsync(database);
        
        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.ReferenceId, result.ReferenceId);
        Assert.Equal(database.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var bs = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);
        await _dbContext.SaveChangesAsync();
        var database = _databaseFixture.DatabaseDto;
        database.CompanyId = "ChHILD_COMPANY_123";
        database.LicenseId="c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _repositoryNotParent.AddAsync(database);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databases.Count, result.Count);
        Assert.All(result, x => Assert.Equal(DatabaseFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        _repository.AddRangeAsync(databases).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        _repositoryNotParent.AddRangeAsync(databases).Wait();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseNames Tests

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnDatabaseNames()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databases.Count, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.GetDatabaseNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsDatabaseNameUnique Tests

    [Fact]
    public async Task IsDatabaseNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "ExistingDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameUnique("ExistingDatabaseName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.IsDatabaseNameUnique("NonExistentDatabaseName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsDatabaseNameExist Tests

    [Fact]
    public async Task IsDatabaseNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "ExistingDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameExist("ExistingDatabaseName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "SameDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameExist("SameDatabaseName", database.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;

        // Act
        var result = await _repository.AddRangeAsync(databases);

        // Assert
        Assert.Equal(databases.Count, result.Count());
        Assert.Equal(databases.Count, _dbContext.Databases.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.RemoveRangeAsync(databases);

        // Assert
        Assert.Equal(databases.Count, result.Count());
        Assert.Empty(_dbContext.Databases);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetDatabaseByServerId Tests

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEntitiesWithMatchingServerId_WhenIsAllInfraTrue()
    {
        // Arrange
        var server = _serverFixture.ServerDto;
        server.ReferenceId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bca";
        await _dbContext.Servers.AddAsync(server);
        var databases = _databaseFixture.DatabaseList;

        databases[0].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bca";
        databases[1].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bm4";
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByServerId("d2c9fd6c-345e-405f-ab59-6b4da8e51bca");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("d2c9fd6c-345e-405f-ab59-6b4da8e51bca", x.ServerId));
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bca";
        databases[1].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bm4";

        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.GetDatabaseByServerId("d2c9fd6c-345e-405f-ab59-6b4da8e51bca");

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByServerId("non-existent-server-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDatabaseByDatabaseTypeId Tests

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnEntitiesWithMatchingDatabaseTypeId()
    {
        // Arrange
        var ComponentType= _componentTypeFixture.ComponentTypeDto;
        ComponentType.ReferenceId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        await _dbContext.ComponentTypes.AddAsync(ComponentType);

        var databases = _databaseFixture.DatabaseList;
        databases[0].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[1].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";

        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId("cddd5b1c-076b-4bd5-aaba-75df3b86a476");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("cddd5b1c-076b-4bd5-aaba-75df3b86a476", x.DatabaseTypeId));
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId("non-existent-database-type-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByUserName Tests

    [Fact]
    public async Task GetByUserName_ShouldReturnEntitiesWithMatchingUserName_WhenIsParent()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[1].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"safari\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[2].Properties ="" ;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserName("admin");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
    }

    [Fact]
    public async Task GetByUserName_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[1].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"safari\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[2].Properties = ""; 

        databases[0].CompanyId = "ChHILD_COMPANY_123";
        databases[1].CompanyId = "ChHILD_COMPANY_123";

        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.GetByUserName("admin");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetByUserNameAndDatabaseType Tests

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldReturnFilteredEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[1].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[1].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"dsfsf\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        databases[2].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"vcxv\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";

        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserNameAndDatabaseType("admin", "cddd5b1c-076b-4bd5-aaba-75df3b86a476");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
        Assert.All(result, x => Assert.Equal("cddd5b1c-076b-4bd5-aaba-75df3b86a476", x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldHandleCommaSeparatedDatabaseTypeIds()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        var database1 = databases.First();
        database1.DatabaseTypeId = "TYPE1";
        database1.Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
       
        var database2 = databases.Skip(1).First();
        database2.DatabaseTypeId = "TYPE2";
        database2.Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";

        databases[2].Properties = "";
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserNameAndDatabaseType("admin", "TYPE1,TYPE2");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.DatabaseTypeId == "TYPE1");
        Assert.Contains(result, x => x.DatabaseTypeId == "TYPE2");
    }

    #endregion

    //#region GetDatabaseByNodeId Tests

    //[Fact]
    //public async Task GetDatabaseByNodeId_ShouldReturnEntitiesWithMatchingNodeId_WhenIsAllInfraTrue()
    //{
    //    // Arrange
    //    var databases = _databaseFixture.DatabaseList;
    //    databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}"

    //    await _repository.AddRangeAsync(databases);

    //    // Act
    //    var result = await _repository.GetDatabaseByNodeId(DatabaseFixture.NodeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(DatabaseFixture.NodeId, x.Properties));
    //}

    //[Fact]
    //public async Task GetDatabaseByNodeId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    //{
    //    // Arrange
    //    var databases = _databaseFixture.DatabaseList;
    //    await _repositoryNotParent.AddRangeAsync(databases);

    //    // Act
    //    var result = await _repositoryNotParent.GetDatabaseByNodeId(DatabaseFixture.NodeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    // Result should be filtered based on assigned infrastructure
    //}

    //#endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(databases);
        var initialCount = databases.Count;

        var toUpdate = databases.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDatabaseName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = databases.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedDatabaseName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region Missing Method Tests - License Related

    [Fact]
    public async Task GetDatabaseCountByLicenseIds_ShouldReturnCorrectCounts()
    {
        // Arrange
        var licenseIds = new List<string> { "LICENSE_001", "LICENSE_002" };
        var siteIds = new List<string> { "SITE_001", "SITE_002" };

        // Add servers for the sites
        var servers = new List<Server>
        {
            _serverFixture.CreateServerWithProperties(siteId: "SITE_001", referenceId: "SERVER_001"),
            _serverFixture.CreateServerWithProperties(siteId: "SITE_002", referenceId: "SERVER_002")
        };
        await _dbContext.Servers.AddRangeAsync(servers);
        await _dbContext.SaveChangesAsync();

        // Add databases with license IDs
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "LICENSE_001", serverId: "SERVER_001"),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "LICENSE_001", serverId: "SERVER_002"),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "LICENSE_002", serverId: "SERVER_001")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseCountByLicenseIds(licenseIds, siteIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result["LICENSE_001"]);
        Assert.Equal(1, result["LICENSE_002"]);
    }

    [Fact]
    public async Task GetDatabaseCountByLicenseIdsAndDatabaseTypeId_ShouldReturnFilteredCounts()
    {
        // Arrange
        var licenseIds = new List<string> { "LICENSE_001" };
        var siteIds = new List<string> { "SITE_001" };
        var databaseTypeId = "TYPE_001";

        // Add servers for the sites
        var server = _serverFixture.CreateServerWithProperties(siteId: "SITE_001", referenceId: "SERVER_001");
        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();

        // Add databases with different types
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "LICENSE_001", serverId: "SERVER_001", databaseTypeId: "TYPE_001"),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "LICENSE_001", serverId: "SERVER_001", databaseTypeId: "TYPE_002")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseCountByLicenseIdsAndDatabaseTypeId(licenseIds, siteIds, databaseTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result["LICENSE_001"]);
    }

    [Fact]
    public async Task GetDatabaseCountByLicenseKey_ShouldReturnCorrectCount()
    {
        // Arrange
        var licenseId = "LICENSE_001";
        var siteIds = new List<string> { "SITE_001", "SITE_002" };

        // Add servers for the sites
        var servers = new List<Server>
        {
            _serverFixture.CreateServerWithProperties(siteId: "SITE_001", referenceId: "SERVER_001"),
            _serverFixture.CreateServerWithProperties(siteId: "SITE_002", referenceId: "SERVER_002")
        };
        await _dbContext.Servers.AddRangeAsync(servers);
        await _dbContext.SaveChangesAsync();

        // Add databases
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001"),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_002"),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: "OTHER_LICENSE", serverId: "SERVER_001")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseCountByLicenseKey(licenseId, siteIds);

        // Assert
        Assert.Equal(2, result);
    }

    [Fact]
    public async Task GetCountByTypeAndLicenseKey_ShouldReturnCorrectCount()
    {
        // Arrange
        var licenseId = "LICENSE_001";
        var type = "TYPE_001";
        var siteIds = new List<string> { "SITE_001" };

        // Add server for the site
        var server = _serverFixture.CreateServerWithProperties(siteId: "SITE_001", referenceId: "SERVER_001");
        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();

        // Add databases
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001", databaseTypeId: type),
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001", databaseTypeId: "TYPE_002")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetCountByTypeAndLicenseKey(licenseId, type, siteIds);

        // Assert
        Assert.Equal(1, result);
    }

    [Fact]
    public async Task GetCountByTypeIdsAndLicenseId_ShouldReturnCorrectCount()
    {
        // Arrange
        var licenseId = "LICENSE_001";
        var excludeTypeIds = new List<string> { "TYPE_002", "TYPE_003" };
        var siteIds = new List<string> { "SITE_001" };

        // Add server for the site
        var server = _serverFixture.CreateServerWithProperties(siteId: "SITE_001", referenceId: "SERVER_001");
        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();

        // Add databases
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001", databaseTypeId: "TYPE_001"), // Should be counted
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001", databaseTypeId: "TYPE_002"), // Should be excluded
            _databaseFixture.CreateDatabaseWithProperties(licenseId: licenseId, serverId: "SERVER_001", databaseTypeId: "TYPE_004")  // Should be counted
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetCountByTypeIdsAndLicenseId(licenseId, excludeTypeIds, siteIds);

        // Assert
        Assert.Equal(2, result); // TYPE_001 and TYPE_004 should be counted
    }

    [Fact]
    public async Task GetDatabaseListByLicenseKey_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
        var licenseId = "LICENSE_001";

        var bServic = _businessServiceFixture.BusinessServiceDto;
        
        await _dbContext.BusinessServices.AddAsync(bServic);
        await _dbContext.SaveChangesAsync();


        var databse = _databaseFixture.DatabaseList;
        databse[0].LicenseId = licenseId;
        await _repository.AddRangeAsync(databse);

        // Act
        var result = await _repository.GetDatabaseListByLicenseKey(licenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
   
    }

    [Fact]
    public async Task GetDatabaseListByLicenseKey_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var licenseId = "LICENSE_001";

        var license = new LicenseManager
        {
            Id = 1,
            ReferenceId = "LICENSE_001",
            LicenseKey = "IRTdhPyCYGGSUou1B+CzHxqfat/T8UeH6epykiCC9Rs=$OJlsrn/E+2Q+muYPM6lXkhR8sbcMATQBKXJunYthw0YZw9NFFOiN",
        };

       await _dbContext.LicenseManagers.AddAsync(license);
        await _dbContext.SaveChangesAsync();


        var bServic = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bServic);
        await _dbContext.SaveChangesAsync();


        var databse = _databaseFixture.DatabaseList;
        databse[0].LicenseId = licenseId;
        databse[0].CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddRangeAsync(databse);

        // Act
        var result = await _repositoryNotParent.GetDatabaseListByLicenseKey(licenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    #endregion

    #region Missing Method Tests - Type and Business Service Related

    [Fact]
    public async Task GetDatabaseByType_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
        var databaseTypeId = "TYPE_001";
       
        var bServic = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bServic);
        await _dbContext.SaveChangesAsync();

        var Component=_componentTypeFixture.ComponentTypeDto;
        Component.ReferenceId = databaseTypeId;

        await _dbContext.ComponentTypes.AddAsync(Component);
        await _dbContext.SaveChangesAsync();

        var databse = _databaseFixture.DatabaseList;
        databse[0].DatabaseTypeId = databaseTypeId;
       
        await _repository.AddRangeAsync(databse);

        // Act
        var result = _repository.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.Equal(databaseTypeId, x.DatabaseTypeId));
    }

    [Fact]
    public void GetDatabaseByType_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var databaseTypeId = "TYPE_001";
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(databaseTypeId: databaseTypeId, companyId: "ChHILD_COMPANY_123"),
            _databaseFixture.CreateDatabaseWithProperties(databaseTypeId: databaseTypeId, companyId: "OTHER_COMPANY")
        };
        _repositoryNotParent.AddRangeAsync(databases).Wait();

        // Act
        var result = _repositoryNotParent.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetDatabaseType_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
        var type = "Oracle";
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(type: type),
            _databaseFixture.CreateDatabaseWithProperties(type: "MySQL")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(type, result.First().Type);
    }

    [Fact]
    public async Task GetDatabaseType_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var type = "Oracle";

        var bServic = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bServic);
        await _dbContext.SaveChangesAsync();
        var databases = _databaseFixture.DatabaseList;
        databases[0].CompanyId = "ChHILD_COMPANY_123";
        databases[0].Type = type;
        databases[1].CompanyId = "OTHER_COMPANY";
        databases[2].CompanyId = "OTHER_COMPANY";
       
        await _repositoryNotParent.AddRangeAsync(databases);
        var _mockinfraRepository = new Mock<IInfraObjectRepository>();

        var infraobject = GetInfraObjectMock(databases[0].ReferenceId,databases[1].ReferenceId);

        _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

        var repositoryNotParent = new DatabaseRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);
        // Act
        var result = await repositoryNotParent.GetDatabaseType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    [Fact]
    public async Task GetDatabaseByBusinessServiceId_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
        var bService = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bService);
        await _dbContext.SaveChangesAsync();    
        var databases = _databaseFixture.DatabaseList;
        databases[1].BusinessServiceId = bService.ReferenceId;
        databases[0].BusinessServiceId = "BS0021";
        databases[2].BusinessServiceId = "BS002";

        await _repository.AddRangeAsync(databases);
        
        // Act
        var result = await _repository.GetDatabaseByBusinessServiceId(bService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(bService.ReferenceId, result.First().BusinessServiceId);
    }

    [Fact]
    public async Task GetDatabaseByBusinessServiceId_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var bService = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bService);
        await _dbContext.SaveChangesAsync();
        var databases = _databaseFixture.DatabaseList;
        databases[1].BusinessServiceId = bService.ReferenceId;
        databases[1].CompanyId = "ChHILD_COMPANY_123";
       
        await _repositoryNotParent.AddRangeAsync(databases);
        var _mockinfraRepository = new Mock<IInfraObjectRepository>();

        var infraobject = GetInfraObjectMock(databases[1].ReferenceId, databases[1].ReferenceId);

        _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

        var repositoryNotParent = new DatabaseRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);
        // Act
        var result = await repositoryNotParent.GetDatabaseByBusinessServiceId(bService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    [Fact]
    public async Task GetDatabaseByNodeId_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
        var nodeId = "NODE_001";
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(properties: nodeId),
            _databaseFixture.CreateDatabaseWithProperties(properties: "NODE_002")
        };
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByNodeId(nodeId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(nodeId, result.First().Properties);
    }

    [Fact]
    public async Task GetDatabaseByNodeId_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var nodeId = "NodeId";
        var server = _serverFixture.ServerDto;

        var bServic = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bServic);

        var databases = _databaseFixture.DatabaseList;
        databases[1].Properties = "NodeId";
        databases[1].CompanyId = "ChHILD_COMPANY_123";

        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();
        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.GetDatabaseByNodeId(nodeId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    #endregion

    #region Additional Missing Method Tests

    [Fact]
    public async Task GetByDatabaseIdsAsync_ShouldReturnFilteredDatabases()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        var idsToRetrieve = databases.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByDatabaseIdsAsync(idsToRetrieve);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, idsToRetrieve));
    }

    [Fact]
    public async Task GetByDatabaseTypeIdAndFormVersion_ShouldReturnFilteredDatabases_WhenIsParent()
    {
        // Arrange
      
        var componentType = _componentTypeFixture.ComponentTypeDto;

        await _dbContext.ComponentTypes.AddAsync(componentType);

        var bServic = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bServic);

        var databases = _databaseFixture.DatabaseList;
        databases[1].DatabaseTypeId = componentType.ReferenceId;
        databases[1].FormVersion = componentType.Version;

       
        await _dbContext.SaveChangesAsync();
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByDatabaseTypeIdAndFormVersion(componentType.ReferenceId,componentType.Version);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(componentType.ReferenceId, result.First().DatabaseTypeId);
        Assert.Equal(componentType.Version, result.First().FormVersion);
    }

    [Fact]
    public async Task GetByDatabaseTypeIdAndFormVersion_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;

        await _dbContext.ComponentTypes.AddAsync(componentType);

        var bServic = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bServic);

        var databases = _databaseFixture.DatabaseList;
        databases[1].DatabaseTypeId = componentType.ReferenceId;
        databases[1].FormVersion = componentType.Version;
        databases[1].CompanyId = "ChHILD_COMPANY_123";


        await _dbContext.SaveChangesAsync();
      
        await _repositoryNotParent.AddRangeAsync(databases);
        var _mockinfraRepository = new Mock<IInfraObjectRepository>();

        var infraobject = GetInfraObjectMock(databases[1].ReferenceId, databases[1].ReferenceId);

        _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

        var repositoryNotParent = new DatabaseRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);

        // Act
        var result = await repositoryNotParent.GetByDatabaseTypeIdAndFormVersion(componentType.ReferenceId, componentType.Version);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    [Fact]
    public async Task GetByDatabaseName_ShouldReturnDatabase_WhenExists()
    {
        // Arrange
        var databaseName = "TestDatabase";
        var database = _databaseFixture.CreateDatabaseWithProperties(name: databaseName);
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByDatabaseName(databaseName);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databaseName, result.Name);
    }

    [Fact]
    public async Task GetByDatabaseName_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByDatabaseName("NonExistentDatabase");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetDatabaseByServerId_ShouldHandleCommaSeparatedServerIds()
    {
        // Arrange
        
        var server = _serverFixture.ServerDto;
    

        var databases = _databaseFixture.DatabaseList;
        databases[1].ServerId = server.ReferenceId;
        databases[2].ServerId = server.ReferenceId;
        databases[0].ServerId = server.ReferenceId;

        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();    
       
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByServerId(server.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Contains(result, x => x.ServerId == server.ReferenceId);

    }


    
    [Fact]
    public async Task Repository_ShouldHandleEmptyLists()
    {
        // Act & Assert
        var result1 = await _repository.GetByDatabaseIdsAsync(new List<string>());
        var result2 = await _repository.GetDatabaseCountByLicenseIds(new List<string>(), new List<string>());
        var result3 = await _repository.GetCountByTypeIdsAndLicenseId("LICENSE_001", new List<string>(), new List<string>());

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
        Assert.Equal(0, result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.GetByDatabaseIdsAsync(null);
        var result2 = await _repository.GetDatabaseCountByLicenseIds(null, null);

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
    }

    [Fact]
    public async Task MapDatabases_ShouldMapAllRelatedEntities()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        var componentType = _componentTypeFixture.ComponentTypeDto;
        var server = _serverFixture.ServerDto;

        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.Servers.AddAsync(server);
        await _dbContext.SaveChangesAsync();

        var database = _databaseFixture.CreateDatabaseWithProperties(
            businessServiceId: businessService.ReferenceId,
            databaseTypeId: componentType.ReferenceId,
            serverId: server.ReferenceId
        );
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.Name, result.BusinessServiceName);
        Assert.Equal(componentType.ComponentName, result.DatabaseType);
        Assert.Equal(server.Name, result.ServerName);
    }

    [Fact]
    public void FilterByJObjectKeyEqualValue_ShouldFilterCorrectly()
    {
        // Arrange
        var databases = new List<Database>
        {
            _databaseFixture.CreateDatabaseWithProperties(properties: "{\"user\":\"admin\",\"port\":\"5432\"}"),
            _databaseFixture.CreateDatabaseWithProperties(properties: "{\"user\":\"guest\",\"port\":\"5432\"}"),
           
        };
        databases[0].Id = 1;
        databases[1].Id = 2;
        _dbContext.Databases.AddRange(databases);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterByJObjectKeyEqualValue(x => x.Properties, "user", "admin");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(1, resultList.Count);
        Assert.All(resultList, x => Assert.Contains("admin", x.Properties));
    }

    #endregion

    public IQueryable<InfraObject> GetInfraObjectMock(string database1, string database2)
    {
        var infraObjects = new List<InfraObject>
            {
    new InfraObject
    {
        Id = 1,
        ReferenceId="70bb97c9-1193-4e86-98ab-bebc88fb438c",
        CompanyId = "ChHILD_COMPANY_123",
        DatabaseProperties = JsonConvert.SerializeObject(new
        {
            PR = new
            {
                id = $"{database1}",
                name = "MongoDB_Testing_PR,RAC_PR1_Server",
                type = "PRDB"
            },
            DR = new
            {
                id = $"{database1}",
                name = "MongoDB_Testing_DR,MSSQL AlwaysOnAG_DR1Server",
                type = "DRDB"
            }
        })
    },
    new InfraObject
    {
        Id = 2,
        ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb43d8d",
        CompanyId = "ChHILD_COMPANY_123",
        DatabaseProperties =JsonConvert.SerializeObject(new
        {
            PR = new
            {
                id = $"{database2}",
                name = "MongoDB_Testing_PR,RAC_PR1_Server",
                type = "PRDB"
            },
            DR = new
            {
                id = $"{database2}",
                name = "MongoDB_Testing_DR,MSSQL AlwaysOnAG_DR1Server",
                type = "DRDB"
            }
        })
    }
        }.AsQueryable();
        return infraObjects;
    }


}
