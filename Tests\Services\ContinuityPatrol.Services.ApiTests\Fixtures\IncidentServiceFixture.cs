﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Application.Features.Incident.Commands.Update;
using ContinuityPatrol.Application.Features.Incident.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Incident.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class IncidentServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public IncidentService Service { get; }

    public CreateIncidentCommand CreateCommand { get; }
    public UpdateIncidentCommand UpdateCommand { get; }
    public GetIncidentDetailVm DetailVm { get; }
    public List<IncidentListVm> ListVm { get; }
    public PaginatedResult<IncidentListVm> PaginatedResultVm { get; }
    public BaseResponse Response { get; }

    public IncidentServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new IncidentService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateIncidentCommand>();
        UpdateCommand = Fixture.Create<UpdateIncidentCommand>();
        DetailVm = Fixture.Create<GetIncidentDetailVm>();
        ListVm = Fixture.CreateMany<IncidentListVm>(3).ToList();
        PaginatedResultVm = Fixture.Create<PaginatedResult<IncidentListVm>>();
        Response = Fixture.Create<BaseResponse>();
    }
}