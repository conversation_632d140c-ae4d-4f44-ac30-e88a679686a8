﻿using ContinuityPatrol.Domain.ViewModels.AboutCpModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class AboutCpServiceTests : IClassFixture<AboutCpServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly AboutCpService _service;
    private readonly AboutCpServiceFixture _fixture;

    public AboutCpServiceTests(AboutCpServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new AboutCpService(_clientMock.Object);
    }

    [Fact]
    public async Task GetAboutCpList_ShouldReturnList()
    {
        // Arrange
        _clientMock
            .Setup(client => client.Get<List<GetAboutCpListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.AboutCpList);

        // Act
        var result = await _service.GetAboutCpList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fixture.AboutCpList.Count, result.Count);
        Assert.Equal(_fixture.AboutCpList, result);
    }
}