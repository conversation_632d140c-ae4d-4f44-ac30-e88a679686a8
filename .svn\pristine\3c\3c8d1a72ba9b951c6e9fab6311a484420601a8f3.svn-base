﻿using ContinuityPatrol.Application.Features.TwoStepAuthentication.Commands.SendOtp;
using ContinuityPatrol.Application.Features.TwoStepAuthentication.Commands.VerifyOtp;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class TwoStepAuthenticationServiceTests : IClassFixture<TwoStepAuthenticationServiceFixture>
{
    private readonly TwoStepAuthenticationServiceFixture _fixture;

    public TwoStepAuthenticationServiceTests(TwoStepAuthenticationServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task SendOtp_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<SendOtpResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.SendOtpResponse);

        var result = await _fixture.Service.SendOtp(_fixture.SendOtpCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.SendOtpResponse, result);
    }

    [Fact]
    public async Task VerifyOtp_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<VerifyOtpResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.VerifyOtpResponse);

        var result = await _fixture.Service.VerifyOtp(_fixture.VerifyOtpCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.VerifyOtpResponse, result);
    }
}