﻿using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SiteTypeServiceTests : IClassFixture<SiteTypeServiceFixture>
{
    private readonly SiteTypeServiceFixture _fixture;

    public SiteTypeServiceTests(SiteTypeServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.SiteTypeId, _fixture.SiteTypeName);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSiteTypeList_Should_Return_List()
    {
        _fixture.ClientMock.Setup(x => x.Get<List<SiteTypeListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SiteTypeList);

        var result = await _fixture.Service.GetSiteTypeList();

        Assert.Equal(_fixture.SiteTypeList, result);
    }

    [Fact]
    public async Task IsSiteTypeExist_Should_Return_True()
    {
        _fixture.ClientMock.Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsSiteTypeExist(_fixture.SiteTypeName, _fixture.SiteTypeId);

        Assert.True(result);
    }

    [Fact]
    public async Task GetSiteTypePaginatedList_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<SiteTypeListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetSiteTypePaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
