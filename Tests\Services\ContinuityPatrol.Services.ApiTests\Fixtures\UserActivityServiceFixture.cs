﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class UserActivityServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserActivityService Service { get; }

    public CreateUserActivityCommand CreateCommand { get; }
    public BaseResponse BaseResponse { get; }

    public List<UserActivityListVm> UserActivityList { get; }
    public List<UserActivityLoginNameVm> UserActivityLoginNames { get; }

    public UserActivityServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserActivityService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateUserActivityCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        UserActivityList = fixture.CreateMany<UserActivityListVm>(3).ToList();
        UserActivityLoginNames = fixture.CreateMany<UserActivityLoginNameVm>(2).ToList();
    }
}