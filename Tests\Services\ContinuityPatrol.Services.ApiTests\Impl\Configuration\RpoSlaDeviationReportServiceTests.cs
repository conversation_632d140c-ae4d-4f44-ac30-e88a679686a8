﻿using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class RpoSlaDeviationReportServiceTests : IClassFixture<RpoSlaDeviationReportServiceFixture>
{
    private readonly RpoSlaDeviationReportServiceFixture _fixture;

    public RpoSlaDeviationReportServiceTests(RpoSlaDeviationReportServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturn_BaseResponse()
    {
        var reportId = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync(reportId);

        Assert.Equal(_fixture.Response, result);
    }
}