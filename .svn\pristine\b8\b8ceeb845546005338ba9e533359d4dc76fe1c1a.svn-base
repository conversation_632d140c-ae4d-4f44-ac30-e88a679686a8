﻿using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SingleSignOnServiceTests : IClassFixture<SingleSignOnServiceFixture>
{
    private readonly SingleSignOnServiceFixture _fixture;

    public SingleSignOnServiceTests(SingleSignOnServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSingleSignOnList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<SingleSignOnListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SsoList);

        var result = await _fixture.Service.GetSingleSignOnList();

        Assert.Equal(_fixture.SsoList, result);
    }

    [Fact]
    public async Task IsSingleSignOnProfileNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsSingleSignOnProfileNameExist(_fixture.ProfileName, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetSingleSignOnById_Should_Return_Detail()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<SingleSignOnDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SsoDetail);

        var result = await _fixture.Service.GetSingleSignOnById(_fixture.Id);

        Assert.Equal(_fixture.SsoDetail, result);
    }

    [Fact]
    public async Task GetSingleSignOnByType_Should_Return_Types()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<SingleSignOnTypeVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.TypeList);

        var result = await _fixture.Service.GetSingleSignOnByType(_fixture.TypeId);

        Assert.Equal(_fixture.TypeList, result);
    }

    [Fact]
    public async Task GetSingleSignOnNames_Should_Return_NameList()
    {
        _fixture.ClientMock
            .Setup(c => c.GetFromCache<List<SingleSignOnNameVm>>(It.IsAny<RestSharp.RestRequest>(), "GetSingleSignOnNames"))
            .ReturnsAsync(_fixture.NameList);

        var result = await _fixture.Service.GetSingleSignOnNames();

        Assert.Equal(_fixture.NameList, result);
    }

    [Fact]
    public async Task GetPaginatedSingleSignOns_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<SingleSignOnListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedList);

        var result = await _fixture.Service.GetPaginatedSingleSignOns(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedList, result);
    }
}
