﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class IncidentManagementServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public IncidentManagementService Service { get; }

    public CreateIncidentManagementCommand CreateCommand { get; }
    public UpdateIncidentManagementCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public IncidentManagementDetailVm DetailVm { get; }
    public List<IncidentManagementListVm> ListVm { get; }

    public IncidentManagementServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new IncidentManagementService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateIncidentManagementCommand>();
        UpdateCommand = Fixture.Create<UpdateIncidentManagementCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<IncidentManagementDetailVm>();
        ListVm = Fixture.CreateMany<IncidentManagementListVm>(3).ToList();
    }
}