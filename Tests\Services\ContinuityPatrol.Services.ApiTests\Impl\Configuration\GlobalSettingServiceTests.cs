﻿using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class GlobalSettingServiceTests : IClassFixture<GlobalSettingServiceFixture>
{
    private readonly GlobalSettingServiceFixture _fixture;

    public GlobalSettingServiceTests(GlobalSettingServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("123");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetGlobalSettingPaginatedList_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<GlobalSettingListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetGlobalSettingPaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsGlobalSettingNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsGlobalSettingNameExist("Setting1", null);

        Assert.True(result);
    }

    [Fact]
    public async Task GetGlobalSettingsList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<GlobalSettingListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetGlobalSettingsList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetGlobalSettingById_ShouldReturnDetail()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<GlobalSettingDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetGlobalSettingById("abc");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task Authentication_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.Authentication(_fixture.AuthCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}
