﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class UserInfraObjectServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserInfraObjectService Service { get; }

    public string CompanyId { get; }
    public string UserId { get; }
    public GetUserInfraObjectByBusinessServiceVm BusinessServiceVm { get; }
    public GetUserInfraObjectByUserIdVm UserIdVm { get; }
    public List<GetByUserIdAndPropertiesVm> PropertiesVmList { get; }

    public UserInfraObjectServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserInfraObjectService(ClientMock.Object);

        CompanyId = fixture.Create<string>();
        UserId = fixture.Create<string>();
        BusinessServiceVm = fixture.Create<GetUserInfraObjectByBusinessServiceVm>();
        UserIdVm = fixture.Create<GetUserInfraObjectByUserIdVm>();
        PropertiesVmList = fixture.Create<List<GetByUserIdAndPropertiesVm>>();
    }
}