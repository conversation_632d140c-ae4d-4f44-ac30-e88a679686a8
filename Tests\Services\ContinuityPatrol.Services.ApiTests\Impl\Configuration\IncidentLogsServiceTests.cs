﻿using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class IncidentLogsServiceTests : IClassFixture<IncidentLogsServiceFixture>
{
    private readonly IncidentLogsServiceFixture _fixture;

    public IncidentLogsServiceTests(IncidentLogsServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateIncidentLogs_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateIncidentLogs(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateIncidentLogs_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateIncidentLogs(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteIncidentLogs_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteIncidentLogs("log-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetIncidentLogsById_ShouldReturnDetailVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<IncidentLogsDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetIncidentLogsById("log-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetList_ShouldReturnListVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<IncidentLogsListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetPaginatedIncidentLogs_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<IncidentLogsListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedIncidentLogs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
