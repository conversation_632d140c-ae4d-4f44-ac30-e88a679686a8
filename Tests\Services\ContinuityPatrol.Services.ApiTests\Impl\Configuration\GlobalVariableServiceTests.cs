﻿using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class GlobalVariableServiceTests : IClassFixture<GlobalVariableServiceFixture>
{
    private readonly GlobalVariableServiceFixture _fixture;

    public GlobalVariableServiceTests(GlobalVariableServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("123");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetGlobalVariableList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(c => c.GetFromCache<List<GlobalVariableListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetGlobalVariableList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<GlobalVariableDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("abc");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsGlobalVariableNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsGlobalVariableNameExist("Threshold", null);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedGlobalVariables_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<GlobalVariableListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedGlobalVariables(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetByVariableName_ShouldReturnDetailList()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<GlobalVariableDetailVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVmList);

        var result = await _fixture.Service.GetByVariableName("MyVar");

        Assert.Equal(_fixture.DetailVmList, result);
    }
}
