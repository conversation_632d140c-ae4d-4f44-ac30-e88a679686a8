﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SmtpConfigurationServiceTests : IClassFixture<SmtpConfigurationServiceFixture>
{
    private readonly SmtpConfigurationServiceFixture _fixture;

    public SmtpConfigurationServiceTests(SmtpConfigurationServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSmtpConfigurationList_Should_Return_ListVm()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<SmtpConfigurationListVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetSmtpConfigurationList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetSmtpConfigurationById_Should_Return_DetailVm()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<SmtpConfigurationDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetSmtpConfigurationById(_fixture.Id);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task SendTestMail_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.SendTestMail(_fixture.TestEmailCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task SendMail_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<SendEmailResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(new SendEmailResponse());

        var result = await _fixture.Service.SendMail(_fixture.EmailCommand);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetPaginatedSmtpConfigurations_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<SmtpConfigurationListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedSmtpConfigurations(_fixture.Query);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
