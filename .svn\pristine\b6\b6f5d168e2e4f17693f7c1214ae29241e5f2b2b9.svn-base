﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Create;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Update;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class RpoSlaDeviationReportServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public RpoSlaDeviationReportService Service { get; }

    public CreateRpoSlaDeviationReportCommand CreateCommand { get; }
    public UpdateRpoSlaDeviationReportCommand UpdateCommand { get; }

    public BaseResponse Response { get; }

    public RpoSlaDeviationReportServiceFixture()
    {
        var fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new RpoSlaDeviationReportService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateRpoSlaDeviationReportCommand>();
        UpdateCommand = fixture.Create<UpdateRpoSlaDeviationReportCommand>();

        Response = fixture.Create<BaseResponse>();
    }
}