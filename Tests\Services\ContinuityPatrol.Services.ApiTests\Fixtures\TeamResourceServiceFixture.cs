﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class TeamResourceServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public TeamResourceService Service { get; }

    public CreateTeamResourceCommand CreateCommand { get; }
    public BaseResponse Response { get; }
    public string TeamId { get; }

    public TeamResourceServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new TeamResourceService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateTeamResourceCommand>();
        Response = fixture.Create<BaseResponse>();
        TeamId = fixture.Create<string>();
    }
}