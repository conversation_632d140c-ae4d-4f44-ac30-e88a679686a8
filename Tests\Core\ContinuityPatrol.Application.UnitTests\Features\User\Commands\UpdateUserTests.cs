﻿using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Commands;

public class UpdateUserTests : IClassFixture<UserFixture>, IClassFixture<UserInfoFixture>, IClassFixture<UserInfraObjectFixture>
{
    private readonly UserFixture _userFixture;

    private readonly UserInfoFixture _userInfoFixture;

    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository = new ();
    private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;

    private readonly UpdateUserCommandHandler _handler;

    public UpdateUserTests(UserFixture userFixture, UserInfoFixture userInfoFixture, UserInfraObjectFixture userInfraObjectFixture)
    {
        _userFixture = userFixture;

        _userInfoFixture = userInfoFixture;

        var mockPublisher = new Mock<IPublisher>();
        _mockUserRepository = UserRepositoryMocks.UpdateUserRepository(_userFixture.Users);

        _userFixture.Users[0].ReferenceId = "25se5365-8b03-63e3-2sKg-6edaa4134097";
        _userInfoFixture.UserInfos[0].UserId = _userFixture.Users[0].ReferenceId;
        userInfraObjectFixture.UserInfraObjects[0].UserId = _userFixture.Users[0].ReferenceId;

        _mockUserInfoRepository = UserInfoRepositoryMocks.UpdateUserInfoRepository(_userInfoFixture.UserInfos);

        var mockUserInfraObjectRepository = UserInfraObjectRepositoryMocks.UpdateUserInfraObjectRepository(userInfraObjectFixture.UserInfraObjects);

        _userInfoFixture.UserInfos[0].UserId = _userFixture.Users[0].ReferenceId;

        _handler = new UpdateUserCommandHandler(_userFixture.Mapper, _mockUserRepository.Object, _mockUserInfoRepository.Object, mockPublisher.Object, mockUserInfraObjectRepository.Object, _mockLoggedInUserService.Object, _mockUserRoleRepository.Object);

    }

    [Fact]
    public async Task Handle_ValidUser_UpdateToUsersRepo()
    {
        _userFixture.UpdateUserCommand.Id = _userFixture.Users[0].ReferenceId;

        var result = await _handler.Handle(_userFixture.UpdateUserCommand, CancellationToken.None);

        var user = await _mockUserRepository.Object.GetByReferenceIdAsync(result.UserId);

        Assert.Equal(_userFixture.UpdateUserCommand.LoginName, user.LoginName);
    }

    [Fact]
    public async Task Handle_Return_ValidUserResponse_When_UserUpdated()
    {
        _userFixture.UpdateUserCommand.Id = _userFixture.Users[0].ReferenceId;

        _userInfoFixture.UpdateUserInfoCommand.UserId = _userInfoFixture.UserInfos[0].UserId;

        var result = await _handler.Handle(_userFixture.UpdateUserCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateUserResponse));

        result.UserId.ShouldBeGreaterThan(0.ToString());

        result.UserId.ShouldBe(_userFixture.UpdateUserCommand.Id);

        result.UserId.ShouldBe(_userInfoFixture.UpdateUserInfoCommand.UserId);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task ThrowNotFoundException_When_InvalidUserId()
    {
        _userFixture.UpdateUserCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_userFixture.UpdateUserCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _userFixture.UpdateUserCommand.Id = _userFixture.Users[0].ReferenceId;

        _mockUserRoleRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(new Domain.Entities.UserRole { Role = "Admin" });

        await _handler.Handle(_userFixture.UpdateUserCommand, CancellationToken.None);

        _mockUserRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockUserRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.User>()), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenUserInfoIsNull()
    {
        // Arrange
        var validUserId = "123"; // any valid ID
        var updateCommand = _userFixture.UpdateUserCommand;
        updateCommand.Id = validUserId;

        var mockUser = new Domain.Entities.User { ReferenceId = validUserId, RoleName = "Admin", LoginName = "testuser" };

        _mockUserRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validUserId))
            .ReturnsAsync(mockUser); // User exists

        _mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(validUserId))
            .ReturnsAsync((Domain.Entities.UserInfo)null!); // UserInfo does NOT exist

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(updateCommand, CancellationToken.None));

        Assert.Equal($"User ({validUserId}) is not found or not authorized", exception.Message);
    }

}