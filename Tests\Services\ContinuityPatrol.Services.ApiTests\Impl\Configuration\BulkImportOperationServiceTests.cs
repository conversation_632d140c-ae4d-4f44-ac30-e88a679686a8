﻿using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BulkImportOperationServiceTests : IClassFixture<BulkImportOperationServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BulkImportOperationService _service;
    private readonly BulkImportOperationServiceFixture _fixture;

    public BulkImportOperationServiceTests(BulkImportOperationServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BulkImportOperationService(_clientMock.Object);
    }

    [Fact]
    public async Task GetBulkImportOperationList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<BulkImportOperationListVm>>(It.IsAny<RestRequest>(), "GetBulkImportOperationList"))
                   .ReturnsAsync(_fixture.OperationList);

        var result = await _service.GetBulkImportOperationList();

        Assert.Equal(_fixture.OperationList, result);
    }

    [Fact]
    public async Task GetBulkImportOperationsrunningStatus_ShouldReturnList()
    {
        _clientMock.Setup(x => x.Get<List<BulkImportOperationRunningListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.RunningList);

        var result = await _service.GetBulkImportOperationsrunningStatus();

        Assert.Equal(_fixture.RunningList, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("some-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _clientMock.Setup(x => x.Get<BulkImportOperationDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task CreateBulkImportValidator_ShouldReturnResponse()
    {
        _clientMock.Setup(x => x.Post<CreateBulkImportValidatorCommandResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ValidatorResponse);

        var result = await _service.CreateBulkImportValidator(_fixture.ValidatorCommand);

        Assert.Equal(_fixture.ValidatorResponse, result);
    }
}
