﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Create;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using TableAccessNameVm = ContinuityPatrol.Application.Features.TableAccess.Queries.GetNames.TableAccessNameVm;

namespace ContinuityPatrol.Application.Mappings;

public class TableAccessProfile : Profile
{
    public TableAccessProfile()
    {
        CreateMap<CreateTableAccessCommand, TableAccessModel>().ReverseMap();
        CreateMap<UpdateTableAccessCommand, TableAccessModel>().ReverseMap();


        CreateMap<PaginatedResult<TableAccess>, PaginatedResult<TableAccessListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

        CreateMap<TableAccess, TableAccessDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<TableAccess, TableAccessListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<TableAccess, TableAccessNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<TableAccess, SchemaNameListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<TableAccess, GetTableNameListBySchemaVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<TableAccess, CreateTableAccessCommand>().ReverseMap();

        CreateMap<CreateTableAccessCommand, TableAccess>().ReverseMap();
        CreateMap<UpdateTableAccessCommand, TableAccess>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<UpdateTableAccess, TableAccess>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<UpdateTableAccess, TableAccessListVm>().ReverseMap();
    }
}