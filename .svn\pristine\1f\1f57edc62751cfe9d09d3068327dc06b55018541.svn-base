﻿using AutoFixture;
using ContinuityPatrol.Application.Models;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class AccountServiceFixture
{
    public AuthenticationRequest AuthRequest { get; }
    public AuthenticationResponse AuthResponse { get; }

    public AccountServiceFixture()
    {
        var fixture = new Fixture();
        AuthRequest = fixture.Create<AuthenticationRequest>();
        AuthResponse = fixture.Create<AuthenticationResponse>();
    }
}