﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class RoboCopyJobServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public RoboCopyJobService Service { get; }

    public CreateRoboCopyJobCommand CreateCommand { get; }
    public UpdateRoboCopyJobCommand UpdateCommand { get; }
    public GetRoboCopyJobPaginatedQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public List<RoboCopyJobListVm> JobList { get; }
    public RoboCopyJobDetailVm JobDetail { get; }
    public PaginatedResult<RoboCopyJobListVm> PaginatedResult { get; }

    public RoboCopyJobServiceFixture()
    {
        var fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new RoboCopyJobService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateRoboCopyJobCommand>();
        UpdateCommand = fixture.Create<UpdateRoboCopyJobCommand>();
        PaginatedQuery = fixture.Create<GetRoboCopyJobPaginatedQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        JobList = fixture.Create<List<RoboCopyJobListVm>>();
        JobDetail = fixture.Create<RoboCopyJobDetailVm>();
        PaginatedResult = fixture.Create<PaginatedResult<RoboCopyJobListVm>>();
    }
}