﻿using AutoFixture;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class ComponentTypeServiceFixture
{
    public string Id { get; }
    public string Name { get; }
    public ComponentTypeDetailVm ComponentTypeDetailVm { get; }
    public List<ComponentTypeModel> ComponentTypeModels { get; }
    public List<ComponentTypeListVm> ComponentTypeList { get; }
    public PaginatedResult<ComponentTypeListVm> PaginatedComponentTypes { get; }
    public BaseResponse BaseResponse { get; }
    public CreateComponentTypeCommand CreateCommand { get; }
    public UpdateComponentTypeCommand UpdateCommand { get; }
    public GetComponentTypePaginatedListQuery Query { get; }

    public ComponentTypeServiceFixture()
    {
        var fixture = new Fixture();

        Id = fixture.Create<string>();
        Name = fixture.Create<string>();
        ComponentTypeDetailVm = fixture.Create<ComponentTypeDetailVm>();
        ComponentTypeModels = fixture.Create<List<ComponentTypeModel>>();
        ComponentTypeList = fixture.Create<List<ComponentTypeListVm>>();
        PaginatedComponentTypes = fixture.Create<PaginatedResult<ComponentTypeListVm>>();
        BaseResponse = fixture.Create<BaseResponse>();
        CreateCommand = fixture.Create<CreateComponentTypeCommand>();
        UpdateCommand = fixture.Create<UpdateComponentTypeCommand>();
        Query = fixture.Create<GetComponentTypePaginatedListQuery>();
    }
}