﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class LoadBalancerServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public LoadBalancerService Service { get; }

    public CreateLoadBalancerCommand CreateCommand { get; }
    public LoadBalancerTestConnectionCommand TestCommand { get; }
    public UpdateLoadBalancerCommand UpdateCommand { get; }
    public UpdateNodeStatusCommand NodeStatusCommand { get; }
    public UpdateLoadBalancerDefaultCommand DefaultCommand { get; }
    public GetLoadBalancerPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse Response { get; }
    public UpdateNodeStatusResponse NodeStatusResponse { get; }
    public UpdateLoadBalancerDefaultResponse DefaultResponse { get; }

    public Fixture FixtureGen { get; }

    public LoadBalancerServiceFixture()
    {
        FixtureGen = new Fixture();
        ClientMock = new Mock<IBaseClient>();

        CreateCommand = FixtureGen.Create<CreateLoadBalancerCommand>();
        TestCommand = FixtureGen.Create<LoadBalancerTestConnectionCommand>();
        UpdateCommand = FixtureGen.Create<UpdateLoadBalancerCommand>();
        NodeStatusCommand = FixtureGen.Create<UpdateNodeStatusCommand>();
        DefaultCommand = FixtureGen.Create<UpdateLoadBalancerDefaultCommand>();
        PaginatedQuery = FixtureGen.Create<GetLoadBalancerPaginatedListQuery>();

        Response = FixtureGen.Create<BaseResponse>();
        NodeStatusResponse = FixtureGen.Create<UpdateNodeStatusResponse>();
        DefaultResponse = FixtureGen.Create<UpdateLoadBalancerDefaultResponse>();

        Service = new LoadBalancerService(ClientMock.Object);
    }
}
