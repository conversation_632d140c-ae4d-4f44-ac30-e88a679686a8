﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class DataSyncOptionsServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public DataSyncOptionsService Service { get; }

    public CreateDataSyncOptionsCommand CreateCommand { get; }
    public UpdateDataSyncOptionsCommand UpdateCommand { get; }
    public GetDataSyncOptionsPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public DataSyncOptionsDetailVm DetailVm { get; }
    public List<DataSyncOptionsListVm> ListVm { get; }
    public PaginatedResult<DataSyncOptionsListVm> PaginatedVm { get; }

    public DataSyncOptionsServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new DataSyncOptionsService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateDataSyncOptionsCommand>();
        UpdateCommand = Fixture.Create<UpdateDataSyncOptionsCommand>();
        PaginatedQuery = Fixture.Create<GetDataSyncOptionsPaginatedListQuery>();

        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<DataSyncOptionsDetailVm>();
        ListVm = Fixture.Create<List<DataSyncOptionsListVm>>();
        PaginatedVm = Fixture.Create<PaginatedResult<DataSyncOptionsListVm>>();
    }
}