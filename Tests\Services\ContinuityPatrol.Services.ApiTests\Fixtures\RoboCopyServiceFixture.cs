﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class RoboCopyServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public RoboCopyService Service { get; }

    public CreateRoboCopyCommand CreateCommand { get; }
    public UpdateRoboCopyCommand UpdateCommand { get; }
    public GetRoboCopyPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public RoboCopyDetailVm DetailVm { get; }
    public List<RoboCopyListVm> RoboList { get; }
    public PaginatedResult<RoboCopyListVm> PaginatedResult { get; }

    public RoboCopyServiceFixture()
    {
        var fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new RoboCopyService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateRoboCopyCommand>();
        UpdateCommand = fixture.Create<UpdateRoboCopyCommand>();
        PaginatedQuery = fixture.Create<GetRoboCopyPaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<RoboCopyDetailVm>();
        RoboList = fixture.Create<List<RoboCopyListVm>>();
        PaginatedResult = fixture.Create<PaginatedResult<RoboCopyListVm>>();
    }
}