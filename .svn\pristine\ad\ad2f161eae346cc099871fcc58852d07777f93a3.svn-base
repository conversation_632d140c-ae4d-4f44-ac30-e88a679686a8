﻿using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BusinessFunctionServiceTests : IClassFixture<BusinessFunctionServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BusinessFunctionService _service;
    private readonly BusinessFunctionServiceFixture _fixture;

    public BusinessFunctionServiceTests(BusinessFunctionServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BusinessFunctionService(_clientMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("any-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetBusinessFunctionPaginatedList_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<BusinessFunctionListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetBusinessFunctionPaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnTrue()
    {
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsBusinessFunctionNameExist("TestName", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetBusinessFunctionNamesByBusinessServiceId_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<GetBusinessFunctionNameByBusinessServiceIdVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ByServiceIdVm);

        var result = await _service.GetBusinessFunctionNamesByBusinessServiceId("svc-id");

        Assert.Equal(_fixture.ByServiceIdVm, result);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<BusinessFunctionNameVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.NameVm);

        var result = await _service.GetBusinessFunctionNames();

        Assert.Equal(_fixture.NameVm, result);
    }

    [Fact]
    public async Task GetBusinessFunctionList_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<BusinessFunctionListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBusinessFunctionList();

        Assert.Equal(_fixture.ListVm, result);
    }
}
