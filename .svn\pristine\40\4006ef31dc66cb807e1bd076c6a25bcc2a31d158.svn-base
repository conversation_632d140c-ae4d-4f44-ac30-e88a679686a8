﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class BulkImportOperationServiceFixture
{
    public CreateBulkImportOperationCommand CreateCommand { get; }
    public UpdateBulkImportOperationCommand UpdateCommand { get; }
    public CreateBulkImportValidatorCommand ValidatorCommand { get; }
    public CreateBulkImportValidatorCommandResponse ValidatorResponse { get; }
    public BaseResponse BaseResponse { get; }
    public BulkImportOperationDetailVm DetailVm { get; }
    public List<BulkImportOperationListVm> OperationList { get; }
    public List<BulkImportOperationRunningListVm> RunningList { get; }

    public BulkImportOperationServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateBulkImportOperationCommand>();
        UpdateCommand = fixture.Create<UpdateBulkImportOperationCommand>();
        ValidatorCommand = fixture.Create<CreateBulkImportValidatorCommand>();
        ValidatorResponse = fixture.Create<CreateBulkImportValidatorCommandResponse>();
        BaseResponse = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<BulkImportOperationDetailVm>();
        OperationList = fixture.CreateMany<BulkImportOperationListVm>(3).ToList();
        RunningList = fixture.CreateMany<BulkImportOperationRunningListVm>(2).ToList();
    }
}