﻿using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class DataSyncJobServiceTests : IClassFixture<DataSyncJobServiceFixture>
{
    private readonly DataSyncJobServiceFixture _fixture;

    public DataSyncJobServiceTests(DataSyncJobServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateDataSyncJob_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateDataSyncJob(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteDataSyncJob_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteDataSyncJob("123");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetPaginatedDataSyncJobs_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<DataSyncJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedVm);

        var result = await _fixture.Service.GetPaginatedDataSyncJobs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedVm, result);
    }

    [Fact]
    public async Task GetDataSyncJobById_ShouldReturnDetail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<DataSyncJobDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetDataSyncJobById("abc");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetDataSyncJobs_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<DataSyncJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetDataSyncJobs();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task UpdateDataSyncJob_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateDataSyncJob(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}
