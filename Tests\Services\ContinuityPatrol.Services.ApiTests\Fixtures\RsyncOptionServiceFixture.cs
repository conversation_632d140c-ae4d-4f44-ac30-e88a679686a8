﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class RsyncOptionServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public RsyncOptionService Service { get; }

    public CreateRsyncOptionCommand CreateCommand { get; }
    public UpdateRsyncOptionCommand UpdateCommand { get; }
    public GetRsyncOptionPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse Response { get; }
    public RsyncOptionDetailVm DetailVm { get; }
    public List<RsyncOptionListVm> ListVm { get; }
    public PaginatedResult<RsyncOptionListVm> PaginatedResult { get; }

    public RsyncOptionServiceFixture()
    {
        var fixture = new Fixture();

        ClientMock = new Mock<IBaseClient>();
        Service = new RsyncOptionService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateRsyncOptionCommand>();
        UpdateCommand = fixture.Create<UpdateRsyncOptionCommand>();
        PaginatedQuery = fixture.Create<GetRsyncOptionPaginatedListQuery>();

        Response = fixture.Create<BaseResponse>();
        DetailVm = fixture.Create<RsyncOptionDetailVm>();
        ListVm = fixture.CreateMany<RsyncOptionListVm>(3).ToList();
        PaginatedResult = fixture.Create<PaginatedResult<RsyncOptionListVm>>();
    }
}