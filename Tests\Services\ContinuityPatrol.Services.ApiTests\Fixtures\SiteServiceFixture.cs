﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.Site.Commands.Create;
using ContinuityPatrol.Application.Features.Site.Commands.Update;
using ContinuityPatrol.Application.Features.Site.Queries.GetByType;
using ContinuityPatrol.Application.Features.Site.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Site.Queries.GetSiteByCompanyId;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using System.Collections.Generic;

public class SiteServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SiteService Service { get; }

    public CreateSiteCommand CreateCommand { get; }
    public UpdateSiteCommand UpdateCommand { get; }
    public GetSitePaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public PaginatedResult<SiteListVm> PaginatedResult { get; }
    public List<SiteListVm> SiteList { get; }
    public List<SiteBySiteTypeVm> SiteByTypeList { get; }
    public List<SiteNameVm> SiteNames { get; }
    public List<GetSiteByCompanyIdVm> SitesByCompanyId { get; }
    public SiteDetailVm SiteDetail { get; }

    public string SiteId { get; }
    public string SiteName { get; }
    public string CompanyId { get; }
    public string SiteTypeId { get; }

    public SiteServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SiteService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSiteCommand>();
        UpdateCommand = fixture.Create<UpdateSiteCommand>();
        PaginatedQuery = fixture.Create<GetSitePaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        PaginatedResult = fixture.Create<PaginatedResult<SiteListVm>>();
        SiteList = fixture.Create<List<SiteListVm>>();
        SiteByTypeList = fixture.Create<List<SiteBySiteTypeVm>>();
        SiteNames = fixture.Create<List<SiteNameVm>>();
        SitesByCompanyId = fixture.Create<List<GetSiteByCompanyIdVm>>();
        SiteDetail = fixture.Create<SiteDetailVm>();

        SiteId = fixture.Create<string>();
        SiteName = fixture.Create<string>();
        CompanyId = fixture.Create<string>();
        SiteTypeId = fixture.Create<string>();
    }
}
