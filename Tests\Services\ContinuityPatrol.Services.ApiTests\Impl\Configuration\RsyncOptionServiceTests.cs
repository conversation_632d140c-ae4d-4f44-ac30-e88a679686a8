﻿using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class RsyncOptionServiceTests : IClassFixture<RsyncOptionServiceFixture>
{
    private readonly RsyncOptionServiceFixture _fixture;

    public RsyncOptionServiceTests(RsyncOptionServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturn_BaseResponse()
    {
        var id = Guid.NewGuid().ToString();

        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturn_DetailVm()
    {
        var id = Guid.NewGuid().ToString();

        _fixture.ClientMock
            .Setup(c => c.Get<RsyncOptionDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId(id);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetRsyncOptionList_ShouldReturn_ListVm()
    {
        _fixture.ClientMock
            .Setup(c => c.GetFromCache<List<RsyncOptionListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetRsyncOptionList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task IsRsyncOptionNameExist_ShouldReturn_True()
    {
        var name = "OptionX";
        string? id = Guid.NewGuid().ToString();

        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsRsyncOptionNameExist(name, id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedRsyncOptions_ShouldReturn_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<RsyncOptionListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedRsyncOptions(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
