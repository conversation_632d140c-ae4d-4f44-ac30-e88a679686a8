using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AzureStorageAccountMonitorLogsRepositoryTests : IClassFixture<AzureStorageAccountMonitorlogsFixture>
{
    private readonly AzureStorageAccountMonitorlogsFixture _azureStorageAccountMonitorlogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AzureStorageAccountMonitorLogsRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public AzureStorageAccountMonitorLogsRepositoryTests(AzureStorageAccountMonitorlogsFixture azureStorageAccountMonitorlogsFixture)
    {
        _azureStorageAccountMonitorlogsFixture = azureStorageAccountMonitorlogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new AzureStorageAccountMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsDto;

        // Act
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(azureStorageAccountMonitorlogs.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(azureStorageAccountMonitorlogs.InfraObjectId, result.InfraObjectId);
        Assert.Equal(azureStorageAccountMonitorlogs.Type, result.Type);
        Assert.Single(_dbContext.AzureStorageAccountMonitorlogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsDto;
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        azureStorageAccountMonitorlogs.Type = "UpdatedType";

        _dbContext.AzureStorageAccountMonitorlogs.Update(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(azureStorageAccountMonitorlogs.ReferenceId);

        Assert.Equal("UpdatedType", result.Type);

    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {

        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsDto;
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        // Act
        azureStorageAccountMonitorlogs.IsActive = false;

        _dbContext.AzureStorageAccountMonitorlogs.Update(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsDto;
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(azureStorageAccountMonitorlogs.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsDto;
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(azureStorageAccountMonitorlogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(azureStorageAccountMonitorlogs.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(azureStorageAccountMonitorlogs.ReferenceId, result.ReferenceId);
        Assert.Equal(azureStorageAccountMonitorlogs.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        await _repository.AddRangeAsync(azureStorageAccountMonitorlogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(azureStorageAccountMonitorlogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;

        // Act
        var result = await _repository.AddRangeAsync(azureStorageAccountMonitorlogs);

        // Assert
        Assert.Equal(azureStorageAccountMonitorlogs.Count, result.Count());
        Assert.Equal(azureStorageAccountMonitorlogs.Count, _dbContext.AzureStorageAccountMonitorlogs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        await _repository.AddRangeAsync(azureStorageAccountMonitorlogs);

        // Act
        var result = await _repository.RemoveRangeAsync(azureStorageAccountMonitorlogs);

        // Assert
        Assert.Equal(azureStorageAccountMonitorlogs.Count, result.Count());
        Assert.Empty(_dbContext.AzureStorageAccountMonitorlogs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        var targetInfraObjectId = "TEST_INFRA_OBJECT_ID";
        azureStorageAccountMonitorlogs.First().InfraObjectId = targetInfraObjectId;
        await _repository.AddRangeAsync(azureStorageAccountMonitorlogs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.InfraObjectId == targetInfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetInfraObjectId, result.First().InfraObjectId);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var azureStorageAccountMonitorlogs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        await _repository.AddRangeAsync(azureStorageAccountMonitorlogs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.InfraObjectId == "NON_EXISTENT_INFRA_OBJECT_ID");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByInfraObjectId Tests

    //[Fact]
    //public async Task GetByInfraObjectId_ShouldReturnLogsInDateRange()
    //{
    //    // Arrange
    //    var infraObjectId = "INFRA_001";
    //    var baseDate = DateTime.Now.Date;
    //    var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
    //    var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

    //    var logs = new List<AzureStorageAccountMonitorlogs>
    //    {
    //        new AzureStorageAccountMonitorlogs
    //        {
    //            InfraObjectId = infraObjectId,
    //            CreatedDate = baseDate.AddDays(-3),
    //            Type = "InRange1",
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            IsActive = true
    //        },
    //        new AzureStorageAccountMonitorlogs
    //        {
    //            InfraObjectId = infraObjectId,
    //            CreatedDate = baseDate.AddDays(-1),
    //            Type = "InRange2",
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            IsActive = true
    //        },
    //        new AzureStorageAccountMonitorlogs
    //        {
    //            InfraObjectId = infraObjectId,
    //            CreatedDate = baseDate.AddDays(-10),
    //            Type = "OutOfRange",
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            IsActive = true
    //        }
    //    };

    //    await _repository.AddRangeAsync(logs);

    //    // Act
    //    var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(2, result.Count);
    //    Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
    //    Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
    //    Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    //}

    //[Fact]
    //public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoLogsInDateRange()
    //{
    //    // Arrange
    //    var infraObjectId = "INFRA_001";
    //    var baseDate = DateTime.Now.Date;
    //    var startDate = baseDate.AddDays(10).ToString("yyyy-MM-dd");
    //    var endDate = baseDate.AddDays(15).ToString("yyyy-MM-dd");

    //    var logs = new List<AzureStorageAccountMonitorlogs>
    //    {
    //        new AzureStorageAccountMonitorlogs
    //        {
    //            InfraObjectId = infraObjectId,
    //            CreatedDate = baseDate.AddDays(-5),
    //            Type = "OutOfRange",
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            IsActive = true
    //        }
    //    };

    //    await _repository.AddRangeAsync(logs);

    //    // Act
    //    var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Empty(result);
    //}

    //[Fact]
    //public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenInfraObjectIdNotFound()
    //{
    //    // Arrange
    //    var logs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
    //    await _repository.AddRangeAsync(logs);

    //    var baseDate = DateTime.Now.Date;
    //    var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
    //    var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

    //    // Act
    //    var result = await _repository.GetByInfraObjectId("NON_EXISTENT_INFRA", startDate, endDate);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Empty(result);
    //}

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogsOfSpecificType()
    {
        // Arrange
        var targetType = "ERROR";

        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new AzureStorageAccountMonitorlogs
            {
                Type = targetType,
                InfraObjectId = "INFRA_001",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new AzureStorageAccountMonitorlogs
            {
                Type = targetType,
                InfraObjectId = "INFRA_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new AzureStorageAccountMonitorlogs
            {
                Type = "INFO",
                InfraObjectId = "INFRA_003",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(targetType, x.Type));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeNotFound()
    {
        // Arrange
        var logs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldNotReturnInactiveLogs()
    {
        // Arrange
        var targetType = "ERROR";

        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new AzureStorageAccountMonitorlogs
            {
                Type = targetType,
                InfraObjectId = "INFRA_001",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new AzureStorageAccountMonitorlogs
            {
                Type = targetType,
                InfraObjectId = "INFRA_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = false
            }
        };

        _dbContext.AzureStorageAccountMonitorlogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var logs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList;
        var log1 = logs[0];
        var log2 = logs[1];
      
        // Act
       await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(log1);
        await _dbContext.AzureStorageAccountMonitorlogs.AddAsync(log2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.AzureStorageAccountMonitorlogs.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var logs = _azureStorageAccountMonitorlogsFixture.AzureStorageAccountMonitorlogsList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(logs);
        var initialCount = logs.Count;

        var toUpdate = logs.Take(2).ToList();
        toUpdate.ForEach(x => x.Type = "UpdatedType");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = logs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Type == "UpdatedType").ToList();
        Assert.Equal(2, updated.Count);
    }

    #region GetByInfraObjectId Tests - Additional Coverage

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var infraObjectId = "INFRA_ACTIVE_TEST";
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "Storage",
                CreatedDate = DateTime.Today.AddDays(-2),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "Storage",
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = false // Should be filtered out by Active() extension
            }
        };

        await _dbContext.AzureStorageAccountMonitorlogs.AddRangeAsync(logs);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldOrderByCreatedDate()
    {
        // Arrange
        var infraObjectId = "INFRA_ORDER_TEST";
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "Storage",
                CreatedDate = DateTime.Today.AddDays(-1), // Later date
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "Storage",
                CreatedDate = DateTime.Today.AddDays(-3), // Earlier date
                IsActive = true
            }
        };

        await _dbContext.AzureStorageAccountMonitorlogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        // Verify ordering - should be ordered by CreatedDate ascending
        Assert.True(result[0].CreatedDate <= result[1].CreatedDate);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleEmptyInfraObjectId()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetByInfraObjectId("", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullInfraObjectId()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetByInfraObjectId(null, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests - Additional Coverage

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var targetType = "TestType";

        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_001",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_002",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = false // Should be filtered out by Active() extension
            }
        };

        await _dbContext.AzureStorageAccountMonitorlogs.AddRangeAsync(logs);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(targetType, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleEmptyType()
    {
        // Arrange
        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_001",
                Type = "SomeType",
                CreatedDate = DateTime.Today,
                IsActive = true
            }
        };

        await _dbContext.AzureStorageAccountMonitorlogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleNullType()
    {
        // Arrange
        var logs = new List<AzureStorageAccountMonitorlogs>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_001",
                Type = "SomeType",
                CreatedDate = DateTime.Today,
                IsActive = true
            }
        };

        await _dbContext.AzureStorageAccountMonitorlogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion
    #endregion
}
