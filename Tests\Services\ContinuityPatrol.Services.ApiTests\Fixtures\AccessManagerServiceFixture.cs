﻿using AutoFixture;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class AccessManagerServiceFixture
{
    public List<AccessManagerListVm> ListVm { get; }
    public PaginatedResult<AccessManagerListVm> PaginatedListVm { get; }
    public BaseResponse BaseResponse { get; }
    public GetByRoleIdVm RoleIdVm { get; }
    public CreateAccessManagerCommand CreateCommand { get; }
    public UpdateAccessManagerCommand UpdateCommand { get; }
    public GetAccessManagerPaginatedListQuery PaginatedQuery { get; }

    public AccessManagerServiceFixture()
    {
        var fixture = new Fixture();
        ListVm = fixture.CreateMany<AccessManagerListVm>(3).ToList();
        PaginatedListVm = fixture.Create<PaginatedResult<AccessManagerListVm>>();
        BaseResponse = fixture.Create<BaseResponse>();
        RoleIdVm = fixture.Create<GetByRoleIdVm>();
        CreateCommand = fixture.Create<CreateAccessManagerCommand>();
        UpdateCommand = fixture.Create<UpdateAccessManagerCommand>();
        PaginatedQuery = fixture.Create<GetAccessManagerPaginatedListQuery>();
    }
}