<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>CGExecution Profile QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/17.0.1/sinon.min.js"></script>
    <script type="text/javascript">
        var RootUrl = '/';
        console.log("Html loaded");
    </script>
    <script src="/js/Common/common.js"></script>
    <script src="/js/CyberResiliency/CGExecutionReport/CGExecutionReport.js"></script>
    <script src="/js/CyberResiliency/CGExecutionReport/CGExecutionReportTest.js"></script>
    <style>
        #qunit-fixture {
            display: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
    <div id="qunit-fixtures"></div>
</body>
</html>