﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Features.User.Commands.UserUnLock;
using ContinuityPatrol.Application.Features.User.Queries.GetDetail;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserServiceTestsFixture
{
    public readonly Mock<IBaseClient> MockClient;
    public readonly Fixture Fixture;
    public readonly UserService UserService;

    public UserServiceTestsFixture()
    {
        Fixture = new Fixture();
        MockClient = new Mock<IBaseClient>();
        UserService = new UserService(MockClient.Object);
    }
}

public class UserServiceTests : IClassFixture<UserServiceTestsFixture>
{
    private readonly UserServiceTestsFixture _fixture;

    public UserServiceTests(UserServiceTestsFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Call_Post()
    {
        var command = _fixture.Fixture.Create<CreateUserCommand>();
        _fixture.MockClient.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.CreateAsync(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Call_Put()
    {
        var command = _fixture.Fixture.Create<UpdateUserCommand>();
        _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.UpdateAsync(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Delete()
    {
        var userId = "testId";
        _fixture.MockClient.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.DeleteAsync(userId);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task CreateDefaultUser_Should_Call_Post()
    {
        var command = _fixture.Fixture.Create<CreateDefaultUserCommand>();
        _fixture.MockClient.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.CreateDefaultUser(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UsersAuthentication_Should_Call_Post()
    {
        var command = _fixture.Fixture.Create<UserLockCommand>();
        _fixture.MockClient.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.UsersAuthentication(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UserUnLock_Should_Call_Post()
    {
        var command = _fixture.Fixture.Create<UserUnLockCommand>();
        _fixture.MockClient.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.UserUnLock(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateUserPassword_Should_Call_Put()
    {
        var command = _fixture.Fixture.Create<UpdatePasswordCommand>();
        _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.UpdateUserPassword(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task ForgotPassword_Should_Call_Put()
    {
        var command = _fixture.Fixture.Create<ForgotPasswordCommand>();
        _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.ForgotPassword(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task ResetPassword_Should_Call_Put()
    {
        var command = _fixture.Fixture.Create<ResetPasswordCommand>();
        _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.ResetPassword(command);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateUserProfileImage_Should_Call_Put()
    {
        var command = _fixture.Fixture.Create<UpdateUserInfoCommand>();
        _fixture.MockClient.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>())).ReturnsAsync(new BaseResponse());

        var result = await _fixture.UserService.UpdateUserProfileImage(command);

        Assert.NotNull(result);
    }
    
}
public class UserService_GetMethodsTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IBaseClient> _mockClient;
    private readonly UserService _userService;

    public UserService_GetMethodsTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _mockClient = _fixture.Freeze<Mock<IBaseClient>>();
        _userService = new UserService(_mockClient.Object);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnUserDetailVm()
    {
        var userId = _fixture.Create<string>();
        var expected = _fixture.Create<UserDetailVm>();
        _mockClient.Setup(x => x.Get<UserDetailVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetByReferenceId(userId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDomainGroups_ShouldReturnListOfStrings()
    {
        var domainName = _fixture.Create<string>();
        var groupName = _fixture.Create<string>();
        var expected = _fixture.Create<List<string>>();
        _mockClient.Setup(x => x.Get<List<string>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetDomainGroups(domainName, groupName);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDomainList_ShouldReturnListOfStrings()
    {
        var expected = _fixture.Create<List<string>>();
        _mockClient.Setup(x => x.Get<List<string>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetDomainList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetDomainUsers_ShouldReturnListOfStrings()
    {
        var domainName = _fixture.Create<string>();
        var domainUserName = _fixture.Create<string>();
        var expected = _fixture.Create<List<string>>();
        _mockClient.Setup(x => x.Get<List<string>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetDomainUsers(domainName, domainUserName);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserByLoginName_ShouldReturnUserLoginNameVm()
    {
        var loginName = _fixture.Create<string>();
        var expected = _fixture.Create<UserLoginNameVm>();
        _mockClient.Setup(x => x.Get<UserLoginNameVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserByLoginName(loginName);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserByRole_ShouldReturnUsersByUserRoleVm()
    {
        var roleId = _fixture.Create<string>();
        var expected = _fixture.Create<List<UsersByUserRoleVm>>();
        _mockClient.Setup(x => x.Get<List<UsersByUserRoleVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserByRole(roleId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserInfraObject_ShouldReturnVm()
    {
        var userId = _fixture.Create<string>();
        var expected = _fixture.Create<GetUserInfraObjectByUserIdVm>();
        _mockClient.Setup(x => x.Get<GetUserInfraObjectByUserIdVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserInfraObject(userId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserNames_ShouldReturnListOfUserNameVm()
    {
        var expected = _fixture.Create<List<UserNameVm>>();
        _mockClient.Setup(x => x.GetFromCache<List<UserNameVm>>(It.IsAny<RestRequest>(), It.IsAny<string>())).ReturnsAsync(expected);

        var result = await _userService.GetUserNames();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserPaginatedList_ShouldReturnPaginatedResult()
    {
        var query = _fixture.Create<GetUserPaginatedListQuery>();
        var expected = _fixture.Create<PaginatedResult<UserViewListVm>>();
        _mockClient.Setup(x => x.Get<PaginatedResult<UserViewListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserPaginatedList(query);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserProfile_ShouldReturnProfileVm()
    {
        var userId = _fixture.Create<string>();
        var expected = _fixture.Create<UserProfileDetailVm>();
        _mockClient.Setup(x => x.Get<UserProfileDetailVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserProfile(userId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUserProfileById_ShouldReturnInfoVm()
    {
        var userId = _fixture.Create<string>();
        var expected = _fixture.Create<UserInfoDetailVm>();
        _mockClient.Setup(x => x.Get<UserInfoDetailVm>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUserProfileById(userId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetUsers_ShouldReturnListOfUserListVm()
    {
        var expected = _fixture.Create<List<UserListVm>>();
        _mockClient.Setup(x => x.Get<List<UserListVm>>(It.IsAny<RestRequest>())).ReturnsAsync(expected);

        var result = await _userService.GetUsers();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task HasUserAsync_ShouldReturnTrue()
    {
        _mockClient.Setup(x => x.Get<bool>(It.IsAny<RestRequest>())).ReturnsAsync(true);

        var result = await _userService.HasUserAsync();

        Assert.True(result);
    }

    [Fact]
    public async Task IsLoginNameExist_ShouldReturnTrue()
    {
        var loginName = _fixture.Create<string>();
        var id = _fixture.Create<string>();
        _mockClient.Setup(x => x.Get<bool>(It.IsAny<RestRequest>())).ReturnsAsync(true);

        var result = await _userService.IsLoginNameExist(loginName, id);

        Assert.True(result);
    }

    [Fact]
    public async Task IsNewPasswordInLastFive_ShouldReturnFalse()
    {
        var userId = _fixture.Create<string>();
        var password = _fixture.Create<string>();
        _mockClient.Setup(x => x.Get<bool>(It.IsAny<RestRequest>())).ReturnsAsync(false);

        var result = await _userService.IsNewPasswordInLastFive(userId, password);

        Assert.False(result);
    }
}