using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestCyberJobManagementSpecification : Specification<CyberJobManagement>
{
    public TestCyberJobManagementSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.Name.Contains(searchTerm);
        }
    }
}

public class CyberJobManagementRepositoryTests : IClassFixture<CyberJobManagementFixture>, IClassFixture<CyberAirGapFixture>, IClassFixture<WorkflowFixture>
{
    private readonly CyberJobManagementFixture _cyberJobManagementFixture;
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly WorkflowFixture _workflowFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberJobManagementRepository _repository;
    private readonly CyberJobManagementRepository _repositoryNotParent;

    public CyberJobManagementRepositoryTests(CyberJobManagementFixture cyberJobManagementFixture, CyberAirGapFixture cyberAirGapFixture, WorkflowFixture workflowFixture)
    {
        _cyberJobManagementFixture = cyberJobManagementFixture;
        _cyberAirGapFixture = cyberAirGapFixture;
        _workflowFixture = workflowFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberJobManagementRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new CyberJobManagementRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region GetCyberJobByAirGapId Tests

    [Fact]
    public async Task GetCyberJobByAirGapId_ShouldReturnJobsForAirGap()
    {
        // Arrange
        var airgapId = "e7efe010-687a-4a71-85c1-7a47e29367f1";
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        jobs[0].AirgapId = airgapId;
        jobs[1].AirgapId = airgapId;

      await  _repository.AddRangeAsync(jobs);


        // Act
        var result = await _repository.GetCyberJobByAirGapId(airgapId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(airgapId, x.AirgapId));
    }

    [Fact]
    public async Task GetCyberJobByAirGapId_ShouldReturnEmpty_WhenNoJobsForAirGap()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByAirGapId("NON_EXISTENT_AIRGAP");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCyberJobByWorkflowId Tests

    [Fact]
    public async Task GetCyberJobByWorkflowId_ShouldReturnJobsForWorkflow()
    {
        // Arrange
        var workflowId = "5d390aad-142c-4b5a-9a74-5bb05140b969";
      var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        jobs[0].WorkflowId = workflowId;
        jobs[1].WorkflowId = workflowId;

        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByWorkflowId(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(workflowId, x.WorkflowId));
    }

    [Fact]
    public async Task GetCyberJobByWorkflowId_ShouldReturnEmpty_WhenNoJobsForWorkflow()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByWorkflowId("NON_EXISTENT_WORKFLOW");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var job = _cyberJobManagementFixture.CyberJobManagementDto;
        job.Name = "ExistingJobName";
        await _dbContext.CyberJobManagements.AddAsync(job);
         _dbContext.SaveChanges();
        // Act
        var result = await _repository.IsNameExist("ExistingJobName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.IsNameExist("NonExistentJobName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var job = _cyberJobManagementFixture.CyberJobManagementDto;
        job.Name = "SameJobName";
        await _dbContext.CyberJobManagements.AddAsync(job);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("SameJobName", job.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var job1 = _cyberJobManagementFixture.CyberJobManagementDto;
        job1.Name = "DuplicateName";
        job1.ReferenceId = Guid.NewGuid().ToString();

        var job2 = new CyberJobManagement
        {
            Name = "DuplicateName",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.CyberJobManagements.AddRangeAsync(job1, job2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("DuplicateName", job1.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementPaginationList;
        await _repository.AddRangeAsync(cyberJobs);

        var specification = new TestCyberJobManagementSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementPaginationList;
        cyberJobs[0].Name = "FilteredJob";
        cyberJobs[1].Name = "AnotherFilteredJob";
        cyberJobs[2].Name = "DifferentJob";

        await _repository.AddRangeAsync(cyberJobs);

        var specification = new TestCyberJobManagementSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.Name));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleSorting()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementPaginationList.Take(5).ToList();
        cyberJobs[0].Name = "Alpha";
        cyberJobs[1].Name = "Beta";
        cyberJobs[2].Name = "Gamma";
        cyberJobs[3].Name = "Delta";
        cyberJobs[4].Name = "Epsilon";

        await _repository.AddRangeAsync(cyberJobs);

        var specification = new TestCyberJobManagementSpecification();

        // Act - Sort ascending
        var resultAsc = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Act - Sort descending
        var resultDesc = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "desc");

        // Assert
        Assert.NotNull(resultAsc);
        Assert.NotNull(resultDesc);
        Assert.True(resultAsc.Succeeded);
        Assert.True(resultDesc.Succeeded);

        // Verify sorting (first item in asc should be different from first item in desc)
        if (resultAsc.Data.Any() && resultDesc.Data.Any())
        {
            Assert.NotEqual(resultAsc.Data.First().Name, resultDesc.Data.First().Name);
        }
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementList;
        _repository.AddRangeAsync(cyberJobs).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion

    #region MapCyberJobManagement Tests

    [Fact]
    public async Task ListAllAsync_ShouldMapAirGapName_WhenAirGapExists()
    {
        // Arrange
        var airGapId = Guid.NewGuid().ToString();

        // Add CyberAirGap first
        var cyberAirGap = new CyberAirGap
        {
            ReferenceId = airGapId,
            Name = "MappedAirGapName",
            IsActive = true
        };
        await _dbContext.CyberAirGaps.AddAsync(cyberAirGap);
        await _dbContext.SaveChangesAsync();

        var cyberJob = new CyberJobManagement
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestJob",
            AirgapId = airGapId,
            AirgapName = "OriginalAirGapName",
            IsActive = true
        };
        await _repository.AddAsync(cyberJob);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("MappedAirGapName", result.First().AirgapName);
    }

    [Fact]
    public async Task ListAllAsync_ShouldMapWorkflowName_WhenWorkflowExists()
    {
        // Arrange
        var workflowId = Guid.NewGuid().ToString();

        // Add Workflow first
        var workflow = new Workflow
        {
            ReferenceId = workflowId,
            Name = "MappedWorkflowName",
            IsActive = true
        };
        await _dbContext.Workflows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var cyberJob = new CyberJobManagement
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestJob",
            WorkflowId = workflowId,
            WorkflowName = "OriginalWorkflowName",
            IsActive = true
        };
        await _repository.AddAsync(cyberJob);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("MappedWorkflowName", result.First().WorkflowName);
    }

    [Fact]
    public async Task ListAllAsync_ShouldKeepOriginalNames_WhenRelatedEntitiesNotExist()
    {
        // Arrange
        var cyberJob = new CyberJobManagement
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestJob",
            AirgapId = Guid.NewGuid().ToString(), // Non-existent air gap
            AirgapName = "OriginalAirGapName",
            WorkflowId = Guid.NewGuid().ToString(), // Non-existent workflow
            WorkflowName = "OriginalWorkflowName",
            IsActive = true
        };
        await _repository.AddAsync(cyberJob);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("OriginalAirGapName", result.First().AirgapName);
        Assert.Equal("OriginalWorkflowName", result.First().WorkflowName);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist(null, "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("", "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsWhitespace()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("   ", "valid-guid"));
    }

    [Fact]
    public async Task GetCyberJobByAirGapId_ShouldHandleNullAirGapId()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(cyberJobs);

        // Act
        var result = await _repository.GetCyberJobByAirGapId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetCyberJobByWorkflowId_ShouldHandleNullWorkflowId()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(cyberJobs);

        // Act
        var result = await _repository.GetCyberJobByWorkflowId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var cyberJobs = _cyberJobManagementFixture.CyberJobManagementList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(cyberJobs);
        var initialCount = cyberJobs.Count;

        var toUpdate = cyberJobs.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedJobName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = cyberJobs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedJobName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenReferenceIdExists()
    {
        // Arrange
        var job = _cyberJobManagementFixture.CyberJobManagementDto ;
        await _dbContext.CyberJobManagements.AddAsync(job);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(job.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(job.ReferenceId, result.ReferenceId);
        Assert.Equal(job.Name, result.Name); // Add more field comparisons as needed
    }
    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenReferenceIdDoesNotExist()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }
    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrowException()
    {
       
      await Assert.ThrowsAsync<ArgumentNullException>(()=> _repository.GetByReferenceIdAsync(null));
       
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrowExceptionOnEmptyId()
    {
       
      await Assert.ThrowsAsync<InvalidArgumentException>(()=> _repository.GetByReferenceIdAsync(""));
       
    }
}
