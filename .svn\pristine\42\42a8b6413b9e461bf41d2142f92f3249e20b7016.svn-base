﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByLicenseKey;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByOsType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByServerName;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByUserName;
using ContinuityPatrol.Application.Features.Server.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class ServerServiceTests : IClassFixture<ServerServiceFixture>
{
    private readonly ServerServiceFixture _fixture;

    public ServerServiceTests(ServerServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<CreateServerCommand>();

        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(command);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        var serverId = Guid.NewGuid().ToString();

        _fixture.ClientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync(serverId);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task GetByLicenseKey_Should_Return_List()
    {
        var licenseId = Guid.NewGuid().ToString();
        var expected = _fixture.Fixture.Create<List<ServerByLicenseKeyVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerByLicenseKeyVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByLicenseKey(licenseId);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var id = Guid.NewGuid().ToString();
        var expected = _fixture.Fixture.Create<ServerDetailVm>();

        _fixture.ClientMock.Setup(x => x.Get<ServerDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByReferenceId(id);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByRoleTypeAndServerType_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerRoleTypeVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerRoleTypeVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByRoleTypeAndServerType("typeA", "typeB");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByServerName_Should_Return_ServerByServerNameVm()
    {
        var expected = _fixture.Fixture.Create<ServerByServerNameVm>();

        _fixture.ClientMock.Setup(x => x.Get<ServerByServerNameVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByServerName("server1");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByServerOsType_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<GetServerByOsTypeVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<GetServerByOsTypeVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByServerOsType("linux");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByType_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerTypeVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerTypeVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetByType("type1");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetPaginatedServers_Should_Return_Result()
    {
        var query = _fixture.Fixture.Create<GetServerPaginatedListQuery>();
        var expected = _fixture.Fixture.Create<PaginatedResult<ServerViewListVm>>();

        _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<ServerViewListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetPaginatedServers(query);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerByIpAddress_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerListVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerByIpAddress("127.0.0.1");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerBySiteId_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerListVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerBySiteId("site123");

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerByUserName_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerByUserNameVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerByUserNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerByUserName("admin", "linux", true);

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerList_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerListVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetServerNames_Should_Return_List()
    {
        var expected = _fixture.Fixture.Create<List<ServerNameVm>>();

        _fixture.ClientMock.Setup(x => x.Get<List<ServerNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetServerNames();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task IsServerNameExist_Should_Return_True()
    {
        _fixture.ClientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsServerNameExist("MyServer", null);

        Assert.True(result);
    }

    [Fact]
    public async Task SaveAllServer_Should_Return_Response()
    {
        var command = _fixture.Fixture.Build<SaveAllServerCommand>()
            .With(x => x.ServerId, Guid.NewGuid().ToString())
            .Create();

        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.SaveAllServer(command);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task SaveAllServer_Should_Throw_Exception_When_ServerId_Is_Empty()
    {
        var command = _fixture.Fixture.Build<SaveAllServerCommand>()
            .With(x => x.ServerId, string.Empty)
            .Create();

        await Assert.ThrowsAsync<Exception>(() => _fixture.Service.SaveAllServer(command));
    }
    [Fact]
    public async Task ServerTestConnection_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<ServerTestConnectionCommand>();

        _fixture.ClientMock.Setup(x => x.Get<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.ServerTestConnection(command);

        Assert.Equal(_fixture.Response, result);
    }
    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<UpdateServerCommand>();

        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateAsync(command);

        Assert.Equal(_fixture.Response, result);
    }
    [Fact]
    public async Task UpdateServerFormVersion_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<UpdateServerVersionCommand>();

        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateServerFormVersion(command);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task UpdateServerPassword_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<UpdateBulkPasswordCommand>();

        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateServerPassword(command);

        Assert.Equal(_fixture.Response, result);
    }
    [Fact]
    public async Task SaveAsServer_Should_Return_Response()
    {
        var command = _fixture.Fixture.Create<SaveAsServerCommand>();

        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.SaveAsServer(command);

        Assert.Equal(_fixture.Response, result);
    }

}
