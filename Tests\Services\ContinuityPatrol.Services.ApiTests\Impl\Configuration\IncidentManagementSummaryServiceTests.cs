﻿using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class IncidentManagementSummaryServiceTests : IClassFixture<IncidentManagementSummaryServiceFixture>
{
    private readonly IncidentManagementSummaryServiceFixture _fixture;

    public IncidentManagementSummaryServiceTests(IncidentManagementSummaryServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("test-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<IncidentManagementSummaryDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("test-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetIncidentManagementSummaryList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(c => c.GetFromCache<List<IncidentManagementSummaryListVm>>(It.IsAny<RestRequest>(), "GetIncidentManagementSummaryList"))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetIncidentManagementSummaryList();

        Assert.Equal(_fixture.ListVm, result);
    }
}
