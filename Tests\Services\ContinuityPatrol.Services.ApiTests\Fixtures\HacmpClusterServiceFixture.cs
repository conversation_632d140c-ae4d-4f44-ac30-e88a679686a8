﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class HacmpClusterServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public HacmpClusterService Service { get; }

    public CreateHacmpClusterCommand CreateCommand { get; }
    public UpdateHacmpClusterCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<HacmpClusterListVm> ListVm { get; }
    public HacmpClusterDetailVm DetailVm { get; }
    public GetHacmpClusterPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<HacmpClusterListVm> PaginatedResult { get; }

    public HacmpClusterServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new HacmpClusterService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateHacmpClusterCommand>();
        UpdateCommand = Fixture.Create<UpdateHacmpClusterCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        ListVm = Fixture.CreateMany<HacmpClusterListVm>(3).ToList();
        DetailVm = Fixture.Create<HacmpClusterDetailVm>();
        PaginatedQuery = Fixture.Create<GetHacmpClusterPaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<HacmpClusterListVm>>();
    }
}