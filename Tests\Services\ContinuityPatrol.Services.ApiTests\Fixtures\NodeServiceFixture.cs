﻿using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.Features.Node.Commands.Update;
using ContinuityPatrol.Application.Features.Node.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class NodeServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public NodeService Service { get; }

    public CreateNodeCommand CreateCommand { get; }
    public UpdateNodeCommand UpdateCommand { get; }
    public GetNodePaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public NodeDetailVm NodeDetail { get; }
    public Fixture FixtureGen { get; }

    public NodeServiceFixture()
    {
        FixtureGen = new Fixture();
        ClientMock = new Mock<IBaseClient>();

        CreateCommand = FixtureGen.Create<CreateNodeCommand>();
        UpdateCommand = FixtureGen.Create<UpdateNodeCommand>();
        PaginatedQuery = FixtureGen.Create<GetNodePaginatedListQuery>();

        BaseResponse = FixtureGen.Create<BaseResponse>();
        NodeDetail = FixtureGen.Create<NodeDetailVm>();

        Service = new NodeService(ClientMock.Object);
    }
}