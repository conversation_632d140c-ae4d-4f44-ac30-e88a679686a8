﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.SiteLocation.Commands.Create;
using ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;
using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using System.Collections.Generic;

public class SiteLocationServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SiteLocationService Service { get; }

    public CreateSiteLocationCommand CreateCommand { get; }
    public UpdateSiteLocationCommand UpdateCommand { get; }
    public GetSiteLocationPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public List<SiteLocationListVm> ListResponse { get; }
    public SiteLocationDetailVm DetailResponse { get; }
    public PaginatedResult<SiteLocationListVm> PaginatedList { get; }

    public string Id { get; }
    public string Name { get; }

    public SiteLocationServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SiteLocationService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSiteLocationCommand>();
        UpdateCommand = fixture.Create<UpdateSiteLocationCommand>();
        PaginatedQuery = fixture.Create<GetSiteLocationPaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        ListResponse = fixture.Create<List<SiteLocationListVm>>();
        DetailResponse = fixture.Create<SiteLocationDetailVm>();
        PaginatedList = fixture.Create<PaginatedResult<SiteLocationListVm>>();

        Id = fixture.Create<string>();
        Name = fixture.Create<string>();
    }
}
