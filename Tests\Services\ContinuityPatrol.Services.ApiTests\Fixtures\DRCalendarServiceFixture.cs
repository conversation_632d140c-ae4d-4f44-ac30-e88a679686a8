﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using Moq;

public class DRCalendarServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public DRCalendarService Service { get; }

    public CreateDrCalendarCommand CreateCommand { get; }
    public UpdateDrCalendarCommand UpdateCommand { get; }
    public GetDrCalendarPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public DrCalendarDetailVm DetailVm { get; }
    public List<DrCalendarActivityListVm> ActivityList { get; }
    public PaginatedResult<DrCalendarActivityListVm> PaginatedResult { get; }
    public GetUpcomingDrillCountVm DrillCountVm { get; }

    private readonly IFixture _fixture;

    public DRCalendarServiceFixture()
    {
        // 🔧 Use AutoMoq to automatically mock interfaces like IFormFile
        _fixture = new Fixture().Customize(new AutoMoqCustomization { ConfigureMembers = true });

        ClientMock = new Mock<IBaseClient>();
        Service = new DRCalendarService(ClientMock.Object);

        // These objects might contain IFormFile, which will now be auto-mocked
        CreateCommand = _fixture.Create<CreateDrCalendarCommand>();
        UpdateCommand = _fixture.Create<UpdateDrCalendarCommand>();
        PaginatedQuery = _fixture.Create<GetDrCalendarPaginatedListQuery>();

        BaseResponse = _fixture.Create<BaseResponse>();
        DetailVm = _fixture.Create<DrCalendarDetailVm>();
        ActivityList = _fixture.Create<List<DrCalendarActivityListVm>>();
        PaginatedResult = _fixture.Create<PaginatedResult<DrCalendarActivityListVm>>();
        DrillCountVm = _fixture.Create<GetUpcomingDrillCountVm>();
    }
}
