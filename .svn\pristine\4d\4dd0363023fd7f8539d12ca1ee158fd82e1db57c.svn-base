﻿using ContinuityPatrol.Application.Features.Setting.Queries.GetBySKey;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SettingServiceTests : IClassFixture<SettingServiceFixture>
{
    private readonly SettingServiceFixture _fixture;

    public SettingServiceTests(SettingServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSettingPaginatedList_Should_Return_Result()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<SettingListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetSettingPaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsSettingNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsSettingNameExist(_fixture.Key, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetSettingsList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<SettingListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetSettingsList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetSettingBySKey_Should_Return_Item()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<GetSettingBySKeyVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SKeyVm);

        var result = await _fixture.Service.GetSettingBySKey(_fixture.Key);

        Assert.Equal(_fixture.SKeyVm, result);
    }
}
