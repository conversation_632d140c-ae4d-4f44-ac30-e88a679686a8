using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestCyberComponentGroupSpecification : Specification<CyberComponentGroup>
{
    public TestCyberComponentGroupSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.GroupName.Contains(searchTerm);
        }
    }
}

public class CyberComponentGroupRepositoryTests : IClassFixture<CyberComponentGroupFixture>, IClassFixture<SiteFixture>
{
    private readonly CyberComponentGroupFixture _cyberComponentGroupFixture;
    private readonly SiteFixture _siteFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberComponentGroupRepository _repository;
    private readonly CyberComponentGroupRepository _repositoryNotParent;

    public CyberComponentGroupRepositoryTests(CyberComponentGroupFixture cyberComponentGroupFixture, SiteFixture siteFixture)
    {
        _cyberComponentGroupFixture = cyberComponentGroupFixture;
        _siteFixture = siteFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberComponentGroupRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new CyberComponentGroupRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region GetCyberComponentGroupsBySiteId Tests

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldReturnGroupsForSite()
    {
        // Arrange
        var siteId = "bb6d2c67-f512-4292-93cc-0bc87043b18b";
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        componentGroups[0].SiteId = siteId; 
        componentGroups[2].SiteId = siteId; 
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(siteId, x.SiteId));
    }

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldReturnEmpty_WhenNoGroupsForSite()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId("NON_EXISTENT_SITE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetComponentGroupsByComponentId Tests

    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldReturnGroupsContainingComponent()
    {
        // Arrange
        var componentId = "351da211-cb11-4bf8-86c3-afce9328ecaa";
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        componentGroups[0].ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp2\"},{\"id\":\"291aa018-3e80-4f75-89ae-77f38326e16c\",\"name\":\"test43\"}]";
        componentGroups[1].ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp2\"},{\"id\":\"291aa018-3e80-4f75-89ae-77f38326e16c\",\"name\":\"test43\"}]";

        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId(componentId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(componentId, x.ComponentProperties));
    }

    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldReturnEmpty_WhenNoGroupsContainComponent()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId("NON_EXISTENT_COMPONENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "ExistingGroupName";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("ExistingGroupName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.IsNameExist("NonExistentGroupName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForSameEntity()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "SameGroupName";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("SameGroupName", componentGroup.ReferenceId);

        // Assert
        Assert.True(result); // Should return false when name exists for the same entity (not unique)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "ExistingGroupName";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("ExistingGroupName", differentId);

        // Assert
        Assert.True(result); // Should return true when name exists for a different entity (not unique)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        var componentGroup1 = new CyberComponentGroup
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "DuplicateGroupName",
            IsActive = true
        };
        var componentGroup2 = new CyberComponentGroup
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "DuplicateGroupName",
            IsActive = true
        };

        await _dbContext.CyberComponentGroups.AddRangeAsync(new[] { componentGroup1, componentGroup2 });
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("DuplicateGroupName", differentId);

        // Assert
        Assert.True(result); // Should return true when multiple entities exist with same name (not unique)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNoEntitiesWithName()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("NonExistentGroupName", validGuid);

        // Assert
        Assert.False(result); // Should return false when no entities exist with the name (unique)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenOnlyOneEntityWithNameAndSameId()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "UniqueGroupName";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("UniqueGroupName", componentGroup.ReferenceId);

        // Assert
        Assert.True(result); // Should return false when only one entity exists with the name and it's the same entity (unique)
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleCaseSensitiveComparison()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "CaseSensitiveGroupName";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var resultExactCase = await _repository.IsNameExist("CaseSensitiveGroupName", differentId);
        var resultDifferentCase = await _repository.IsNameExist("casesensitivegroupname", differentId);

        // Assert
        Assert.True(resultExactCase); // Should return true for exact case match
        Assert.False(resultDifferentCase); // Should return false for different case (case sensitive comparison)
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "Group@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("Group@#$%^&*()_+-={}[]|\\:;\"'<>?,./", differentId);

        // Assert
        Assert.True(result); // Should handle special characters correctly
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleVeryLongGroupName()
    {
        // Arrange
        var longGroupName = new string('A', 1000); // Very long name
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = longGroupName;
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist(longGroupName, differentId);

        // Assert
        Assert.True(result); // Should handle very long names correctly
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleUnicodeCharactersInName()
    {
        // Arrange
        var unicodeGroupName = "グループ名前测试组名Группа名前";
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = unicodeGroupName;
        await _dbContext.CyberComponentGroups.AddAsync(componentGroup);
        await _dbContext.SaveChangesAsync();

        var differentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist(unicodeGroupName, differentId);

        // Assert
        Assert.True(result); // Should handle Unicode characters correctly
    }

    #endregion



    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleSiteAndComponentAssignments()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;

        componentGroups[0].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd963";
        componentGroups[0].GroupName = "DatabaseGroup";
        componentGroups[1].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd965";
        componentGroups[1].GroupName = "WebGroup";
        componentGroups[2].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd964";
        componentGroups[2].GroupName = "ApplicationGroup";


        await _repository.AddRangeAsync(componentGroups);

        // Act
        var dbsite= await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd963"));
        var website = await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd965"));
        var appsite = await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd964"));

        // Assert
        Assert.Single(dbsite);
        Assert.Single(website);
        Assert.Single(appsite);
        Assert.Contains("Database", dbsite.First().GroupName);
        Assert.Contains("App", appsite.First().GroupName);
        Assert.Contains("Web", website.First().GroupName);
    }

    #endregion

    #region Group Name and Component Properties Tests

    [Fact]
    public async Task Repository_ShouldFilterByGroupName()
    {
        // Arrange

        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;

        componentGroups[0].GroupName = "ApplicationGroup";
 
        componentGroups[1].GroupName = "DatabaseGroup";

        componentGroups[2].GroupName = "WebGroup";
   
        await _repository.AddRangeAsync(componentGroups);
        // Act
        var databaseGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Database"));
        var webGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Web"));
        var applicationGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Application"));

        // Assert
        Assert.Single(databaseGroups);
        Assert.Single(webGroups);
        Assert.Single(applicationGroups);
        Assert.Contains("Database", databaseGroups.First().GroupName);
        Assert.Contains("Web", webGroups.First().GroupName);
        Assert.Contains("Application", applicationGroups.First().GroupName);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupPaginationList;
        await _repository.AddRangeAsync(componentGroups);

        var specification = new TestCyberComponentGroupSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "GroupName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupPaginationList;
        componentGroups[0].GroupName = "FilteredGroup";
        componentGroups[1].GroupName = "AnotherFilteredGroup";
        componentGroups[2].GroupName = "DifferentGroup";

        await _repository.AddRangeAsync(componentGroups);

        var specification = new TestCyberComponentGroupSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "GroupName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.GroupName));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        _repository.AddRangeAsync(componentGroups).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion

    #region MapCyberComponentGroup Tests

    [Fact]
    public async Task ListAllAsync_ShouldMapSiteName_WhenSiteExists()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();

        // Add Site first
        var site = new Site
        {
            ReferenceId = siteId,
            Name = "MappedSiteName",
            IsActive = true
        };
        await _dbContext.Sites.AddAsync(site);
        await _dbContext.SaveChangesAsync();

        var componentGroup = new CyberComponentGroup
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "TestGroup",
            SiteId = siteId,
            SiteName = "OriginalSiteName",
            IsActive = true
        };
        await _repository.AddAsync(componentGroup);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("MappedSiteName", result.First().SiteName);
    }

    [Fact]
    public async Task ListAllAsync_ShouldKeepOriginalSiteName_WhenSiteNotExists()
    {
        // Arrange
        var componentGroup = new CyberComponentGroup
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "TestGroup",
            SiteId = Guid.NewGuid().ToString(), // Non-existent site
            SiteName = "OriginalSiteName",
            IsActive = true
        };
        await _repository.AddAsync(componentGroup);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("OriginalSiteName", result.First().SiteName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldMapSiteName_WhenSiteExists()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();

        // Add Site first
        var site = new Site
        {
            ReferenceId = siteId,
            Name = "MappedSiteName",
            IsActive = true
        };
        await _dbContext.Sites.AddAsync(site);
        await _dbContext.SaveChangesAsync();

        var componentGroup = new CyberComponentGroup
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "TestGroup",
            SiteId = siteId,
            SiteName = "OriginalSiteName",
            IsActive = true
        };
        await _repository.AddAsync(componentGroup);

        // Act
        var result = await _repository.GetByReferenceIdAsync(componentGroup.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("MappedSiteName", result.SiteName);
        Assert.Equal(componentGroup.ReferenceId, result.ReferenceId);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist(null, "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("", "valid-guid"));
    }

    [Fact]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsWhitespace()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist("   ", "valid-guid"));
    }

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldHandleNullSiteId()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldHandleEmptySiteId()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

   
    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldHandlePartialMatches()
    {
        // Arrange
        var componentId = "351da211";
        var componentGroups = new List<CyberComponentGroup>
        {
            new CyberComponentGroup
            {
                GroupName = "Group1",
                ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp1\"}]", // Contains the ID
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponentGroup
            {
                GroupName = "Group2",
                ComponentProperties = "[{\"id\":\"291aa018-3e80-4f75-89ae-77f38326e16c\",\"name\":\"comp2\"}]", // Doesn't contain the ID
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId(componentId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Group1", result.First().GroupName);
    }

    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldHandleInvalidJsonInComponentProperties()
    {
        // Arrange
        var componentId = "351da211-cb11-4bf8-86c3-afce9328ecaa";
        var componentGroups = new List<CyberComponentGroup>
        {
            new CyberComponentGroup
            {
                GroupName = "Group1",
                ComponentProperties = "invalid-json", // Invalid JSON
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponentGroup
            {
                GroupName = "Group2",
                ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp1\"}]", // Valid JSON with ID
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId(componentId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Group2", result.First().GroupName);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(componentGroups);
        var initialCount = componentGroups.Count;

        var toUpdate = componentGroups.Take(2).ToList();
        toUpdate.ForEach(x => x.GroupName = "UpdatedGroupName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = componentGroups.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.GroupName == "UpdatedGroupName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
