﻿using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class RsyncJobServiceTests : IClassFixture<RsyncJobServiceFixture>
{
    private readonly RsyncJobServiceFixture _fixture;

    public RsyncJobServiceTests(RsyncJobServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateRsyncJob_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateRsyncJob(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteRsyncJob_ShouldReturn_BaseResponse()
    {
        var id = "rsync-id";

        _fixture.ClientMock
            .Setup(x => x.Get<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteRsyncJob(id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetPaginatedRsyncJobs_ShouldReturn_Result()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<RsyncJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedRsyncJobs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetRsyncJobById_ShouldReturn_Detail()
    {
        var id = "rsync-detail-id";

        _fixture.ClientMock
            .Setup(x => x.Get<RsyncJobDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetRsyncJobById(id);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetRsyncJobs_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<RsyncJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetRsyncJobs();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task UpdateRsyncJob_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateRsyncJob(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}
