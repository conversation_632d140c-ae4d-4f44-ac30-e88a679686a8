﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using System.Collections.Generic;

public class SingleSignOnServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SingleSignOnService Service { get; }

    public CreateSingleSignOnCommand CreateCommand { get; }
    public UpdateSingleSignOnCommand UpdateCommand { get; }
    public GetSingleSignOnPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public string Id { get; }
    public string ProfileName { get; }
    public string TypeId { get; }

    public List<SingleSignOnListVm> SsoList { get; }
    public SingleSignOnDetailVm SsoDetail { get; }
    public List<SingleSignOnTypeVm> TypeList { get; }
    public List<SingleSignOnNameVm> NameList { get; }
    public PaginatedResult<SingleSignOnListVm> PaginatedList { get; }

    public SingleSignOnServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SingleSignOnService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSingleSignOnCommand>();
        UpdateCommand = fixture.Create<UpdateSingleSignOnCommand>();
        PaginatedQuery = fixture.Create<GetSingleSignOnPaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        Id = fixture.Create<string>();
        ProfileName = fixture.Create<string>();
        TypeId = fixture.Create<string>();

        SsoList = fixture.Create<List<SingleSignOnListVm>>();
        SsoDetail = fixture.Create<SingleSignOnDetailVm>();
        TypeList = fixture.Create<List<SingleSignOnTypeVm>>();
        NameList = fixture.Create<List<SingleSignOnNameVm>>();
        PaginatedList = fixture.Create<PaginatedResult<SingleSignOnListVm>>();
    }
}
