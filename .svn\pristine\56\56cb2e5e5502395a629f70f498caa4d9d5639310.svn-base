using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class HeatMapStatusViewRepositoryTests : IClassFixture<HeatMapStatusViewFixture>,IClassFixture<BusinessServiceFixture>,IClassFixture<InfraObjectFixture>,IClassFixture<BusinessFunctionFixture>, IDisposable
{
    private readonly HeatMapStatusViewFixture _heatMapStatusViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly HeatMapStatusViewRepository _repositoryParent;
    private readonly HeatMapStatusViewRepository _repositoryIsNotParent;
    private readonly BusinessServiceFixture _businessSeviceFixture;
    private readonly InfraObjectFixture _infraObjectFixture;
    private readonly BusinessFunctionFixture _businessFunctionFixture;

    public HeatMapStatusViewRepositoryTests(HeatMapStatusViewFixture heatMapStatusViewFixture,BusinessServiceFixture businessServiceFixture, InfraObjectFixture infraObjectFixture,BusinessFunctionFixture businessFunctionFixture) 
    {
        _heatMapStatusViewFixture = heatMapStatusViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repositoryParent = new HeatMapStatusViewRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryIsNotParent = new HeatMapStatusViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _businessSeviceFixture = businessServiceFixture;
        _infraObjectFixture = infraObjectFixture;
        _businessFunctionFixture = businessFunctionFixture;

    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.HeatMapStatusViews.RemoveRange(_dbContext.HeatMapStatusViews);
        await _dbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveHeatMapStatusViews_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();

        var businessSevice = _businessSeviceFixture.BusinessServiceList;

        await _dbContext.BusinessServices.AddRangeAsync(businessSevice);
        await ClearDatabase();
        var businessFnction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFnction);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessSevice[0].ReferenceId,
                BusinessServiceName = businessSevice[0].Name,
                BusinessFunctionId = "BF_123",
                BusinessFunctionName = "Business Function 1",
                InfraObjectId = "IO_123",
                InfraObjectName = "Infrastructure Object 1",
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessSevice[1].ReferenceId,
                BusinessServiceName = businessSevice[1].Name,
                BusinessFunctionId = "BF_456",
                BusinessFunctionName = "Business Function 2",
                InfraObjectId = "IO_456",
                InfraObjectName = "Infrastructure Object 2",
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessSevice[2].ReferenceId,
                BusinessServiceName = businessSevice[2].Name,
                BusinessFunctionId = "BF_789",
                BusinessFunctionName = "Business Function 3",
                InfraObjectId = "IO_789",
                InfraObjectName = "Infrastructure Object 3",
                EntityId = "E_789",
                HeatmapType = "Network",
                HeatmapStatus = "Warning",
                IsAffected = false,
                IsActive = false // This should be excluded
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
         _dbContext.SaveChanges();

        // Act
        var result = await _repositoryParent.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count); // Only active records
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Contains(result, x => x.BusinessServiceName == businessSevice[0].Name);
        Assert.Contains(result, x => x.BusinessServiceName == businessSevice[1].Name);
        Assert.DoesNotContain(result, x => x.BusinessServiceName == businessSevice[2].Name);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoActiveData()
    {
        // Arrange
        await ClearDatabase();

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Business Service 1",
            IsActive = false
        };

         _dbContext.HeatMapStatusViews.Add(heatMapStatusView);
         _dbContext.SaveChanges();

        // Act
        var result = await _repositoryParent.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsHeatMapStatusView_WhenIsAllInfraTrue()
    {
        // Arrange
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Business Service 1";
        await _dbContext.BusinessServices.AddAsync(businessSevice);
        await _dbContext.SaveChangesAsync();
        await ClearDatabase();
       
        var referenceId = Guid.NewGuid().ToString();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = referenceId,
            BusinessServiceId = businessSevice.ReferenceId,
            BusinessServiceName = businessSevice.Name,
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Business Function 1",
            InfraObjectId = "IO_123",
            InfraObjectName = "Infrastructure Object 1",
            EntityId = "E_123",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            IsActive = true
        };

        _dbContext.HeatMapStatusViews.Add(heatMapStatusView);
         _dbContext.SaveChanges();

        // Act
        var result = await _repositoryParent.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Business Service 1", result.BusinessServiceName);
        Assert.Equal("Server", result.HeatmapType);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
       
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repositoryParent.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }
    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsAssignedBusinessService_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var referenceId = Guid.NewGuid().ToString();
        var businessService = _businessSeviceFixture.BusinessServiceDto;

    

        var heatMapStatus = new HeatMapStatusView
        {
            Id = 99, // To validate OrderByDescending
            ReferenceId = referenceId,
            BusinessServiceId = businessService.ReferenceId,
            BusinessServiceName = businessService.Name,
            InfraObjectId = "IO_456",
            HeatmapType = "Database",
            HeatmapStatus = "Warning",
            IsAffected = true,
            IsActive = true
        };

        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryIsNotParent.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(businessService.Name, result.BusinessServiceName);
        Assert.Equal(heatMapStatus.HeatmapType, result.HeatmapType);
    }

    #endregion

    #region GetHeatMapStatusTypeAsync Tests

    [Fact]
    public async Task GetHeatMapStatusTypeAsync_ReturnsFilteredResults_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
      

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                HeatmapType = "Server",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                HeatmapType = "Database",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusTypeAsync("Server", true);

        // Assert
        Assert.Single(result);
        Assert.Equal("Server", result.First().HeatmapType);
        Assert.True(result.First().IsAffected);
    }

    [Fact]
    public async Task GetHeatMapStatusTypeAsync_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
       

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            HeatmapType = "Database",
            IsAffected = false,
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusTypeAsync("Server", true);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusType Tests

    [Fact]
    public async Task GetHeatMapStatusType_ReturnsQueryable_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                HeatmapType = "Database",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var query = _repositoryParent.GetHeatMapStatusType("Server");
        var result = query.ToList();

        // Assert
        Assert.Single(result);
        Assert.Equal("Server", result.First().HeatmapType);
    }

    #endregion

    #region GetImpactDetail Tests

    [Fact]
    public async Task GetImpactDetail_ReturnsAffectedActiveRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessSevice = _businessSeviceFixture.BusinessServiceList;
        businessSevice[0].Name = "Affected Service 1";
        businessSevice[1].Name = "Business Service 2";
        businessSevice[2].Name = "Business Service 1";
        await _dbContext.BusinessServices.AddRangeAsync(businessSevice);
        await ClearDatabase();
        var businessFnction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFnction);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessSevice[0].ReferenceId,
                BusinessServiceName =businessSevice[0].Name,
                BusinessFunctionId=businessFnction.ReferenceId,
                BusinessFunctionName=businessFnction.Name,
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessSevice[1].ReferenceId,
                BusinessServiceName = businessSevice[1].Name,
                BusinessFunctionId=businessFnction.ReferenceId,
                BusinessFunctionName=businessFnction.Name,
                HeatmapType = "Database",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                BusinessServiceName = "Affected Service 2",
                 BusinessFunctionId=businessFnction.ReferenceId,
                BusinessFunctionName=businessFnction.Name,
                HeatmapType = "Network",
                IsAffected = true,
                IsActive = false // Should be excluded
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                 BusinessServiceId = businessSevice[2].ReferenceId,
                BusinessServiceName = businessSevice[2].Name,
                 BusinessFunctionId=businessFnction.ReferenceId,
                BusinessFunctionName=businessFnction.Name,
                HeatmapType = "Application",
                IsAffected = true,
                IsActive = true
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
         _dbContext.SaveChanges();

        // Act
        var result = await _repositoryParent.GetImpactDetail();

        // Assert
        Assert.Equal(2, result.Count); // Only affected and active records
        Assert.All(result, x => Assert.True(x.IsAffected));
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Contains(result, x => x.BusinessServiceName == businessSevice[0].Name);
        Assert.Contains(result, x => x.BusinessServiceName == businessSevice[2].Name);
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Not Affected Service");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == businessSevice[1].Name);
    }

    [Fact]
    public async Task GetImpactDetail_ReturnsEmpty_WhenNoAffectedActiveRecords()
    {
        // Arrange
        await ClearDatabase();
        

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                IsAffected = true,
                IsActive = false
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
        _dbContext.SaveChanges();

        // Act
        var result = await _repositoryParent.GetImpactDetail();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapDetailByInfraObjectId Tests

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Service 1";
        await _dbContext.BusinessServices.AddAsync(businessSevice);
        var infraobject = _infraObjectFixture.InfraObjectDto;
        var infraObjectId = infraobject.ReferenceId;
        infraobject.Name = "IO_123";
        await _dbContext.InfraObjects.AddAsync(infraobject);
        await _dbContext.SaveChangesAsync();

        var heatMapType = "Server";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                Id=11,
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                 BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                HeatmapType = heatMapType,
                BusinessServiceName = "Service 1",
                IsActive = true
            },
            new HeatMapStatusView
            {
                 Id=12,
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                HeatmapType = "Database", // Different type
                BusinessServiceName = "Service 2",
                IsActive = true
            },
            new HeatMapStatusView
            {
                 Id=13,
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "IO_456", // Different infra object
                HeatmapType = heatMapType,
                BusinessServiceName = "Service 3",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapDetailByInfraObjectId(infraObjectId, heatMapType);

        // Assert
        Assert.Single(result);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
        Assert.Equal(heatMapType, result.First().HeatmapType);
        Assert.Equal("Service 1", result.First().BusinessServiceName);
    }

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectId_IsCaseInsensitive()
    {
        // Arrange
        var infraobject = _infraObjectFixture.InfraObjectDto;

        infraobject.Name = "IO_123";
        await _dbContext.InfraObjects.AddAsync(infraobject);
        await _dbContext.SaveChangesAsync();
        await ClearDatabase();
    
        var infraObjectId = infraobject.ReferenceId;
        var heatMapType = "Server";

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraObjectId,
            HeatmapType = heatMapType,
            BusinessServiceName = "Service 1",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapDetailByInfraObjectId(infraObjectId, "SERVER");

        // Assert
        Assert.Single(result);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
        Assert.Equal(heatMapType, result.First().HeatmapType);
    }

    #endregion

    #region GetHeatMapDetailByInfraObjectAndEntityId Tests

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectAndEntityId_ReturnsMatchingRecord_WhenIsAllInfraTrue()
    {
        // Arrange
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Matching Service";
        await _dbContext.BusinessServices.AddAsync(businessSevice); 
        var infraobject = _infraObjectFixture.InfraObjectDto;
        var infraObjectId = infraobject.ReferenceId;
        infraobject.Name = "IO_123";
        await _dbContext.InfraObjects.AddAsync(infraobject);
        await _dbContext.SaveChangesAsync();
 
        await ClearDatabase();
      

        var entityId = "E_456";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                EntityId = entityId,
                 BusinessServiceId = businessSevice.ReferenceId,
                BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                BusinessServiceName = "Matching Service",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                EntityId = "E_789", // Different entity
                  BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                BusinessServiceName = "Different Entity Service",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "IO_999", // Different infra object
                EntityId = entityId,
                BusinessServiceName = "Different Infra Service",
                  BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapDetailByInfraObjectAndEntityId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(entityId, result.EntityId);
        Assert.Equal("Matching Service", result.BusinessServiceName);
        Assert.Equal("Server", result.HeatmapType);
    }

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectAndEntityId_ReturnsNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();
        

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "IO_123",
            EntityId = "E_456",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapDetailByInfraObjectAndEntityId("IO_999", "E_999");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetHeatMapListByBusinessServiceId Tests

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Business Service 1";
        await _dbContext.BusinessServices.AddAsync(businessSevice);
        await _dbContext.SaveChangesAsync();
       

        var businessServiceId = businessSevice.ReferenceId;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456", // Different business service
                BusinessServiceName = "Service 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessServiceId, x.BusinessServiceId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidGuid = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repositoryParent.GetHeatMapListByBusinessServiceId(invalidGuid));
    }

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ThrowsException_WhenEmptyGuid()
    {
        // Arrange
        var emptyGuid = "";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repositoryParent.GetHeatMapListByBusinessServiceId(emptyGuid));
    }

    #endregion

    #region GetHeatMapListByBusinessFunctionId Tests

    [Fact]
    public async Task GetHeatMapListByBusinessFunctionId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        var businessFunctionId = businessFunction.ReferenceId;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = businessFunctionId,
                BusinessFunctionName = "Function 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = businessFunctionId,
                BusinessFunctionName = "Function 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = "BF_456", // Different business function
                BusinessFunctionName = "Function 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapListByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessFunctionId, x.BusinessFunctionId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapListByBusinessFunctionId_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidGuid = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repositoryParent.GetHeatMapListByBusinessFunctionId(invalidGuid));
    }

    #endregion

    #region GetHeatMapStatusByEntityId Tests

    [Fact]
    public async Task GetHeatMapStatusByEntityId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        

        var entityId = "E_123";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = entityId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = entityId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_456", // Different entity
                BusinessServiceName = "Service 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusByEntityId(entityId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(entityId, x.EntityId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapStatusByEntityId_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityId = "E_123",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusByEntityId("E_999");

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusByEntityIds Tests

    [Fact]
    public async Task GetHeatMapStatusByEntityIds_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
       

        var entityIds = new List<string> { "E_123", "E_456" };

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_123",
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_456",
                BusinessServiceName = "Service 2",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_789", // Not in the list
                BusinessServiceName = "Service 3",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusByEntityIds(entityIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.EntityId == "E_123");
        Assert.Contains(result, x => x.EntityId == "E_456");
        Assert.DoesNotContain(result, x => x.EntityId == "E_789");
    }

    [Fact]
    public async Task GetHeatMapStatusByEntityIds_ReturnsEmpty_WhenEmptyList()
    {
        // Arrange
        await ClearDatabase();
        
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityId = "E_123",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusByEntityIds(new List<string>());

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusesByBusinessServiceIdAndType Tests

    [Fact]
    public async Task GetHeatMapStatusesByBusinessServiceIdAndType_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
       
        var businessServiceId = "BS_123";
        var type = "Server";
        var isAffected = true;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = type,
                IsAffected = isAffected,
                BusinessServiceName = "Matching Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = type,
                IsAffected = false, // Different affected status
                BusinessServiceName = "Non-Affected Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = "Database", // Different type
                IsAffected = isAffected,
                BusinessServiceName = "Different Type Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456", // Different business service
                HeatmapType = type,
                IsAffected = isAffected,
                BusinessServiceName = "Different Service",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapStatusesByBusinessServiceIdAndType(businessServiceId, type, isAffected);

        // Assert
        Assert.Single(result);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
        Assert.Equal(type, result.First().HeatmapType);
        Assert.Equal(isAffected, result.First().IsAffected);
        Assert.Equal("Matching Service", result.First().BusinessServiceName);
    }

    #endregion

    #region Complete Coverage Tests for Missing Methods

    [Fact]
    public async Task AssignedInfraObjectIds_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var infraObjects = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", BusinessServiceName = "Service 1" },
            new HeatMapStatusView { InfraObjectId = "80bb97c9-1193-4e86-98ab-bebc88fb438c", BusinessServiceName = "Service 2" },
            new HeatMapStatusView { InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb4999", BusinessServiceName = "Service 3" }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var queryableInfraObjects = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - This will execute the complete logic inside AssignedInfraObjectIds
        var result = _repositoryIsNotParent.AssignedInfraObjectIds(queryableInfraObjects);
        var resultList = result.ToList(); // Force execution

        // Assert
        Assert.Equal(2, resultList.Count);
        Assert.Contains(resultList, x => x.InfraObjectId == "70bb97c9-1193-4e86-98ab-bebc88fb438c");
        Assert.Contains(resultList, x => x.InfraObjectId == "80bb97c9-1193-4e86-98ab-bebc88fb438c");
        Assert.DoesNotContain(resultList, x => x.InfraObjectId == "INFRA_999");
    }

    [Fact]
    public async Task AssignedBusinessServices_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var businessServices = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa", BusinessServiceName = "Service 1" },
            new HeatMapStatusView { BusinessServiceId = "c9b3cd51-f687-4667-be33-46f82b7086fa", BusinessServiceName = "Service 2" },
            new HeatMapStatusView { BusinessServiceId = "c9b3cd51-f999-4667-be33-46f82b7086fa", BusinessServiceName = "Service 3" }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(businessServices);
        await _dbContext.SaveChangesAsync();

        var queryableBusinessServices = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - This will execute the complete logic inside AssignedBusinessServices
        var result = _repositoryIsNotParent.AssignedBusinessServices(queryableBusinessServices);

        // Assert
        Assert.Single(result);
        Assert.Equal("c9b3cd51-f688-4667-be33-46f82b7086fa", result.First().BusinessServiceId);
        Assert.Equal("Service 1", result.First().BusinessServiceName);
    }

    [Fact]
    public async Task AssignedBusinessFunctions_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var businessFunctions = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93", BusinessFunctionName = "Function 1" },
            new HeatMapStatusView { BusinessFunctionId = "1acf2f57-7a9c-4as78-a3bf-26db53117e93", BusinessFunctionName = "Function 2" },
            new HeatMapStatusView { BusinessFunctionId = "1acf2f57-7999-4a56-a3bf-26db53117e93", BusinessFunctionName = "Function 3" }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var queryableBusinessFunctions = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - This will execute the complete logic inside AssignedBusinessFunctions
        var result = _repositoryIsNotParent.AssignedBusinessFunctions(queryableBusinessFunctions);

        // Assert
        Assert.Single(result);
        Assert.Equal("1acf2f57-7a9c-4a56-a3bf-26db53117e93", result.First().BusinessFunctionId);
        Assert.Equal("Function 1", result.First().BusinessFunctionName);
    }

    [Fact]
    public async Task AssignedInfraObjects_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var infraObjects = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { InfraObjectId = "80bb97c9-1193-4e86-98ab-bebc88fb438c", InfraObjectName = "Infra 1" },
            new HeatMapStatusView { InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", InfraObjectName = "Infra 2" },
            new HeatMapStatusView { InfraObjectId = "INFRA_999", InfraObjectName = "Infra 3" }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var queryableInfraObjects = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - This will execute the complete logic inside AssignedInfraObjects
        var result = _repositoryIsNotParent.AssignedInfraObjects(queryableInfraObjects);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.InfraObjectId == "80bb97c9-1193-4e86-98ab-bebc88fb438c");
        Assert.Contains(result, x => x.InfraObjectId == "70bb97c9-1193-4e86-98ab-bebc88fb438c");
        Assert.DoesNotContain(result, x => x.InfraObjectId == "INFRA_999");
    }

    [Fact]
    public async Task GetByInfraObjectId_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var infraObject = new HeatMapStatusView
        {
            ReferenceId= "70bb97c9-1193-4e86-98ab-bebc88fb438c",
            InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c",
            BusinessServiceName = "Test Service",
            HeatmapType = "Server"
        };

        await _dbContext.HeatMapStatusViews.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        // Act - This will execute the complete logic inside GetByInfraObjectId
        var result = _repositoryIsNotParent.GetByInfraObjectId(infraObject);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("70bb97c9-1193-4e86-98ab-bebc88fb438c", result.InfraObjectId);
        Assert.Equal("Test Service", result.BusinessServiceName);
        Assert.Equal("Server", result.HeatmapType);
    }

    [Fact]
    public async Task GetPaginatedAssignedBusinessServicesByHeatMap_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();
        var businessServices = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa", BusinessServiceName = "Assigned Service 1" },
            new HeatMapStatusView { BusinessServiceId = "BS_002", BusinessServiceName = "Non-Assigned Service" },
            new HeatMapStatusView { BusinessServiceId = "BS_999", BusinessServiceName = "Non-Assigned Service 2" }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(businessServices);
        await _dbContext.SaveChangesAsync();

        var queryableBusinessServices = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - Use reflection to call the private method and execute its complete logic
        var method = _repositoryIsNotParent.GetType()
            .GetMethod("GetPaginatedAssignedBusinessServicesByHeatMap",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        var result = method?.Invoke(_repositoryIsNotParent, new object[] { queryableBusinessServices }) as IQueryable<HeatMapStatusView>;

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal("c9b3cd51-f688-4667-be33-46f82b7086fa", resultList.First().BusinessServiceId);
        Assert.Equal("Assigned Service 1", resultList.First().BusinessServiceName);
    }

    [Fact]
    public async Task MapHeatMapStatus_ExecutesCompleteLogic_WhenCalled()
    {
        // Arrange
        await ClearDatabase();

        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.Name = "Test Business Service";
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "Test Business Function";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.Name = "Test Infra Object";
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            InfraObjectId = infraObject.ReferenceId,
            EntityId = "E_123",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            ErrorMessage = "No Error",
            Properties = "Test Properties",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TestUser",
            LastModifiedDate = DateTime.UtcNow
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        var queryableHeatMap = _dbContext.HeatMapStatusViews.AsQueryable();

        // Act - Use reflection to call the private method and execute its complete logic
        var method = _repositoryParent.GetType()
            .GetMethod("MapHeatMapStatus",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        var result = method?.Invoke(_repositoryParent, new object[] { queryableHeatMap }) as IQueryable<HeatMapStatusView>;

        // Assert
        Assert.NotNull(result);
        var mappedResult = result.ToList();
        Assert.Single(mappedResult);

        var mapped = mappedResult.First();
        Assert.Equal(businessService.ReferenceId, mapped.BusinessServiceId);
        Assert.Equal("Test Business Service", mapped.BusinessServiceName);
        Assert.Equal(businessFunction.ReferenceId, mapped.BusinessFunctionId);
        Assert.Equal("Test Business Function", mapped.BusinessFunctionName);
        Assert.Equal(infraObject.ReferenceId, mapped.InfraObjectId);
        Assert.Equal("Test Infra Object", mapped.InfraObjectName);
    }

    [Fact]
    public async Task GetByInfraObjectIdAsync_ExecutesCompleteLogic_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "IO_123";
        var entityId = "E_456";

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraObjectId,
            EntityId = entityId,
            BusinessServiceName = "Test Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This will execute the IsParent = true branch
        var result = _repositoryParent.GetByInfraObjectIdAsync(infraObjectId, entityId,
            x => x.InfraObjectId.Equals(infraObjectId) && x.EntityId.Equals(entityId));

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(infraObjectId, resultList.First().InfraObjectId);
        Assert.Equal(entityId, resultList.First().EntityId);
    }

    [Fact]
    public async Task GetByInfraObjectIdAsync_ExecutesCompleteLogic_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "IO_123";
        var entityId = "E_456";

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraObjectId,
            EntityId = entityId,
            BusinessServiceName = "Test Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This will execute the IsParent = false branch (FilterBy)
        var result = _repositoryIsNotParent.GetByInfraObjectIdAsync(infraObjectId, entityId,
            x => x.InfraObjectId.Equals(infraObjectId) && x.EntityId.Equals(entityId));

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(infraObjectId, resultList.First().InfraObjectId);
        Assert.Equal(entityId, resultList.First().EntityId);
    }

    #endregion

    #region Specific Method Coverage Tests

    [Fact]
    public async Task GetHeatMapStatusType_Paginated_ExecutesIsAllInfraFalseBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.Name = "Assigned Service";
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "Assigned Function";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.Name = "Assigned Infra";
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessServiceName = businessService.Name,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the GetPaginatedAssignedBusinessServicesByHeatMap branch
        var result = await _repositoryIsNotParent.GetHeatMapStatusType("Server", 1, 10,
            new HeatMapStatusFilterSpecification(""), "Id", "asc");

        // Assert
        Assert.Single(result.Data);
        Assert.Equal(businessService.ReferenceId, result.Data.First().BusinessServiceId);
    }

    [Fact]
    public async Task GetHeatMapStatusTypeAsync_ExecutesAssignedInfraObjectIdsBranch()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c",
                InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // This should be in assigned infra objects
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the AssignedInfraObjectIds branch
        var result = await _repositoryIsNotParent.GetHeatMapStatusTypeAsync("Server", true);

        // Assert
        Assert.Single(result);
        Assert.Equal("70bb97c9-1193-4e86-98ab-bebc88fb438c", result.First().InfraObjectId);
    }

    [Fact]
    public async Task GetHeatMapStatusType_NonPaginated_ExecutesGetPaginatedAssignedBusinessServicesByHeatMapBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.Name = "Assigned Service";
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "Assigned Function";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.Name = "Assigned Infra";
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            InfraObjectId = infraObject.ReferenceId,
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the GetPaginatedAssignedBusinessServicesByHeatMap branch
        var query = _repositoryIsNotParent.GetHeatMapStatusType("Server");
        var result = query.ToList();

        // Assert
        Assert.Single(result);
        Assert.Equal(businessService.ReferenceId, result.First().BusinessServiceId);
    }

    [Fact]
    public async Task GetHeatMapStatusesByBusinessServiceIdAndType_ExecutesAssignedInfraObjectIdsBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var type = "Server";
        var isAffected = true;

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // This should be in assigned infra objects
            HeatmapType = type,
            IsAffected = isAffected,
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the AssignedInfraObjectIds branch
        var result = await _repositoryIsNotParent.GetHeatMapStatusesByBusinessServiceIdAndType(businessServiceId, type, isAffected);

        // Assert
        Assert.Single(result);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
        Assert.Equal("70bb97c9-1193-4e86-98ab-bebc88fb438c", result.First().InfraObjectId);
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ExecutesAllBranches_WhenBusinessServiceIdIsNull()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // Assigned infra object
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the null businessServiceId branch with IsAllInfra = false
        var result = await _repositoryIsNotParent.GroupHeatMapTypeByServiceId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ServerDownCount);
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ExecutesAllBranches_WhenBusinessServiceIdIsProvided()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // Assigned infra object
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the provided businessServiceId branch with IsAllInfra = false
        var result = await _repositoryIsNotParent.GroupHeatMapTypeByServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ServerDownCount);
    }

    [Fact]
    public async Task GetHeatMapListByBusinessFunctionId_ExecutesAssignedBusinessFunctionsBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "Assigned Function";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        var businessFunctionId = businessFunction.ReferenceId;

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessFunctionId = businessFunctionId,
            BusinessFunctionName = "Assigned Function",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the AssignedBusinessFunctions branch
        var result = await _repositoryIsNotParent.GetHeatMapListByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Single(result);
        Assert.Equal(businessFunctionId, result.First().BusinessFunctionId);
    }

    #endregion

    #region Additional Branch Coverage Tests

    [Fact]
    public async Task AssignedBusinessServices_ThrowExceptionEmptyAssignedBusinessServicesPath()
    {
        // Arrange - Create a mock with empty assigned business services
        var mockLoggedInUser = new Mock<ILoggedInUserService>();
        mockLoggedInUser.Setup(x => x.IsAllInfra).Returns(false);
        mockLoggedInUser.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUser.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUser.Setup(x => x.AssignedInfras).Returns("{}");

        var repository = new HeatMapStatusViewRepository(_dbContext, mockLoggedInUser.Object);

        var businessServices = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" }
        }.AsQueryable();

        // Act - This should execute the path where AssignedBusinessServices.Count = 0
        await Assert.ThrowsAsync<NullReferenceException>(() => Task.FromResult(repository.AssignedBusinessServices(businessServices)));

      
    }

    [Fact]
    public async  Task AssignedBusinessFunctions_ThrowExceptionEmptyAssignedBusinessServicesPath()
    {
        // Arrange - Create a mock with empty assigned business services
        var mockLoggedInUser = new Mock<ILoggedInUserService>();
        mockLoggedInUser.Setup(x => x.IsAllInfra).Returns(false);
        mockLoggedInUser.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUser.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUser.Setup(x => x.AssignedInfras).Returns("{}");

        var repository = new HeatMapStatusViewRepository(_dbContext, mockLoggedInUser.Object);

        var businessFunctions = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessServiceId="", BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93", BusinessFunctionName = "Function 1" }
        }.AsQueryable();

        // Act - This should execute the path where AssignedBusinessServices.Count = 0
        await Assert.ThrowsAsync<NullReferenceException>( () => Task.FromResult(repository.AssignedBusinessFunctions(businessFunctions)));

        
    }

    [Fact]
    public void AssignedBusinessFunctions_ExecutesEmptyAssignedBusinessFunctionsPath()
    {
        // Arrange
        var businessFunctions = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { BusinessFunctionId = "BF_999", BusinessFunctionName = "Function 1" }
        }.AsQueryable();

        // Act - This should execute the path where assignedBusinessFunctions.Count = 0
        var result = _repositoryIsNotParent.AssignedBusinessFunctions(businessFunctions);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task AssignedInfraObjects_ExecutesEmptyAssignedBusinessServicesPath()
    {
        // Arrange - Create a mock with empty assigned business services
        var mockLoggedInUser = new Mock<ILoggedInUserService>();
        mockLoggedInUser.Setup(x => x.IsAllInfra).Returns(false);
        mockLoggedInUser.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUser.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUser.Setup(x => x.AssignedInfras).Returns("{}");

        var repository = new HeatMapStatusViewRepository(_dbContext, mockLoggedInUser.Object);

        var infraObjects = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { InfraObjectId = "INFRA_001", InfraObjectName = "Infra 1" }
        }.AsQueryable();

        // Assert
       
        await Assert.ThrowsAsync<NullReferenceException>(() => Task.FromResult(repository.AssignedInfraObjects(infraObjects)));
       
 
    }

    [Fact]
    public void AssignedInfraObjects_ExecutesEmptyAssignedBusinessFunctionsPath()
    {
        // Arrange
        var infraObjects = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { InfraObjectId = "INFRA_999", InfraObjectName = "Infra 1" }
        }.AsQueryable();

        // Act - This should execute the path where assignedBusinessFunctions.Count = 0
        var result = _repositoryIsNotParent.AssignedInfraObjects(infraObjects);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedInfraObjects_ExecutesEmptyAssignedInfraObjectsPath()
    {
        // Arrange - Create a scenario where there are business functions but no infra objects
        var infraObjects = new List<HeatMapStatusView>
        {
            new HeatMapStatusView { InfraObjectId = "INFRA_999", InfraObjectName = "Infra 1" }
        }.AsQueryable();

        // Act - This should execute the path where assignedBusinessInfraObjects.Count = 0
        var result = _repositoryIsNotParent.AssignedInfraObjects(infraObjects);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectAndEntityId_ExecutesGetByInfraObjectIdBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.Name = "Assigned Service";
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "Assigned Function";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.Name = "Assigned Infra";
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var infraObjectId = infraObject.ReferenceId;
        var entityId = "E_123";

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraObjectId,
            EntityId = entityId,
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the GetByInfraObjectId branch (IsAllInfra = false)
        var result = await _repositoryIsNotParent.GetHeatMapDetailByInfraObjectAndEntityId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(entityId, result.EntityId);
    }

    [Fact]
    public async Task GetByHeatMapTypeAndBusinessServiceIds_ExecutesAssignedBusinessServicesBranch()
    {
        // Arrange
        await ClearDatabase();
        var type = "Server";
        var businessServiceIds = new List<string> { "c9b3cd51-f688-4667-be33-46f82b7086fa" }; // This is in assigned business services

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
            BusinessServiceName = "Assigned Service",
            HeatmapType = type,
            IsAffected = true,
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the AssignedBusinessServices branch (IsParent = false)
        var result = await _repositoryIsNotParent.GetByHeatMapTypeAndBusinessServiceIds(type, businessServiceIds);

        // Assert
        Assert.Single(result);
        Assert.Equal("c9b3cd51-f688-4667-be33-46f82b7086fa", result.First().BusinessServiceId);
    }

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ExecutesAssignedBusinessServicesBranch()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.Name = "Assigned Service";
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        var businessServiceId = businessService.ReferenceId;

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Assigned Service",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act - This should execute the AssignedBusinessServices branch (IsParent = false)
        var result = await _repositoryIsNotParent.GetHeatMapListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Single(result);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddHeatMapStatusView_WhenValidHeatMapStatusView()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            InfraObjectId = "IO_123",
            InfraObjectName = "Test Infrastructure Object",
            EntityId = "E_123",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            Properties = "Test Properties",
            ErrorMessage = "Test Error Message",
            PropertyKey = "TestKey",
            IsActive = true
        };

        // Act
        var result = await _repositoryParent.AddAsync(heatMapStatusView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(heatMapStatusView.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(heatMapStatusView.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Equal(heatMapStatusView.InfraObjectName, result.InfraObjectName);
        Assert.Equal(heatMapStatusView.HeatmapType, result.HeatmapType);
        Assert.Equal(heatMapStatusView.HeatmapStatus, result.HeatmapStatus);
        Assert.Equal(heatMapStatusView.IsAffected, result.IsAffected);
        Assert.Single(_dbContext.HeatMapStatusViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenHeatMapStatusViewIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repositoryParent.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsHeatMapStatusView_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetByIdAsync(heatMapStatusView.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(heatMapStatusView.Id, result.Id);
        Assert.Equal(heatMapStatusView.BusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repositoryParent.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateHeatMapStatusView_WhenValidHeatMapStatusView()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Original Service",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            IsActive = true
        };

        _dbContext.HeatMapStatusViews.Add(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        heatMapStatusView.BusinessServiceName = "Updated Service";
        heatMapStatusView.HeatmapStatus = "Offline";
        heatMapStatusView.IsAffected = true;

        // Act
        var result = await _repositoryParent.UpdateAsync(heatMapStatusView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service", result.BusinessServiceName);
        Assert.Equal("Offline", result.HeatmapStatus);
        Assert.True(result.IsAffected);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenHeatMapStatusViewIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repositoryParent.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveHeatMapStatusView_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repositoryParent.DeleteAsync(heatMapStatusView);

        // Assert
        var deletedView = await _dbContext.HeatMapStatusViews.FindAsync(heatMapStatusView.Id);
        Assert.Null(deletedView);
    }

    #endregion

    #region GetHeatMapByInfraObjectId Tests - 100% Coverage

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldReturnMatchingHeatMapStatusViews_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var infraObjectId = infraObject.ReferenceId;
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectId,
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectId,
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "DIFFERENT_INFRA_ID", // Different infra object
                EntityId = "E_789",
                HeatmapType = "Network",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldReturnAssignedInfraObjects_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var infraObjectId = infraObject.ReferenceId;
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectId, // Assigned infra object
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "UNASSIGNED_INFRA_ID", // Non-assigned infra object
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var result = await repositoryNotAllInfra.GetHeatMapByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only assigned infra object should be returned
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "DIFFERENT_INFRA_ID",
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectId("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldThrowException_WhenInvalidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repositoryParent.GetHeatMapByInfraObjectId("invalid-guid"));
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldThrowException_WhenEmptyGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repositoryParent.GetHeatMapByInfraObjectId(""));
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectId_ShouldThrowException_WhenNullGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _repositoryParent.GetHeatMapByInfraObjectId(null));
    }

    #endregion

    #region GetHeatMapByInfraObjectIds Tests - 100% Coverage

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldReturnMatchingHeatMapStatusViews_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObjects = _infraObjectFixture.InfraObjectList.Take(2).ToList();
        await _dbContext.InfraObjects.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var infraObjectIds = infraObjects.Select(x => x.ReferenceId).ToList();
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectIds[0],
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectIds[1],
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "DIFFERENT_INFRA_ID", // Not in the list
                EntityId = "E_789",
                HeatmapType = "Network",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectIds(infraObjectIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.InfraObjectId, infraObjectIds));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldReturnAssignedInfraObjects_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var assignedInfraObject = _infraObjectFixture.InfraObjectDto;
        assignedInfraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
        var unassignedInfraObject = _infraObjectFixture.InfraObjectList[1];
        unassignedInfraObject.ReferenceId = "UNASSIGNED_INFRA_ID";

        await _dbContext.InfraObjects.AddRangeAsync(new[] { assignedInfraObject, unassignedInfraObject });
        await _dbContext.SaveChangesAsync();

        var infraObjectIds = new List<string> { assignedInfraObject.ReferenceId, unassignedInfraObject.ReferenceId };
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = assignedInfraObject.ReferenceId, // Assigned infra object
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = unassignedInfraObject.ReferenceId, // Non-assigned infra object
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var result = await repositoryNotAllInfra.GetHeatMapByInfraObjectIds(infraObjectIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only assigned infra object should be returned
        Assert.Equal(assignedInfraObject.ReferenceId, result.First().InfraObjectId);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "DIFFERENT_INFRA_ID",
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        var nonExistentIds = new List<string> { "NON_EXISTENT_ID_1", "NON_EXISTENT_ID_2" };

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectIds(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldReturnEmpty_WhenEmptyIdsList()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectIds(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldHandleNullIdsList()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectIds(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetHeatMapByInfraObjectIds_ShouldReturnMultipleMatchesForSingleInfraObject()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var infraObjectId = infraObject.ReferenceId;
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectId,
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObjectId,
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GetHeatMapByInfraObjectIds(new List<string> { infraObjectId });

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
    }

    #endregion

    #region Additional Branch Coverage Tests for Complete Coverage

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnCorrectlyFilteredResults()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.HeatMapStatusViews.AsQueryable();
        var result = repositoryNotAllInfra.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(infraObject.ReferenceId, resultList.First().InfraObjectId);
    }

    [Fact]
    public async Task AssignedBusinessServices_ShouldReturnCorrectlyFilteredResults()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        businessService.ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa"; // This is in assigned business services
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.HeatMapStatusViews.AsQueryable();
        var result = repositoryNotAllInfra.AssignedBusinessServices(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(businessService.ReferenceId, result.First().BusinessServiceId);
    }

    [Fact]
    public async Task AssignedBusinessFunctions_ShouldReturnCorrectlyFilteredResults()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93"; // This is in assigned business functions
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "Server",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.HeatMapStatusViews.AsQueryable();
        var result = repositoryNotAllInfra.AssignedBusinessFunctions(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(businessFunction.ReferenceId, result.First().BusinessFunctionId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnCorrectResult_WhenInfraObjectExists()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            InfraObjectId = infraObject.ReferenceId,
            EntityId = "E_123",
            HeatmapType = "Server",
            IsActive = true
        };

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var result = repositoryNotAllInfra.GetByInfraObjectId(heatMapStatusView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObject.ReferenceId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnNull_WhenInfraObjectNotAssigned()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.ReferenceId = "UNASSIGNED_INFRA_ID"; // This is NOT in assigned infras
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            InfraObjectId = infraObject.ReferenceId,
            EntityId = "E_123",
            HeatmapType = "Server",
            IsActive = true
        };

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var result = repositoryNotAllInfra.GetByInfraObjectId(heatMapStatusView);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GroupHeatMapTypeByServiceId Tests - 100% Coverage

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ShouldReturnCorrectCounts_WhenBusinessServiceIdProvided()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var businessServiceId = businessService.ReferenceId;
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "server", // Case insensitive
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_456",
                HeatmapType = "database",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_789",
                HeatmapType = "replication",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_101",
                HeatmapType = "server", // Second server
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "DIFFERENT_BS_ID", // Different business service
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_102",
                HeatmapType = "server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_103",
                HeatmapType = "server",
                IsAffected = false, // Not affected
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GroupHeatMapTypeByServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.ServerDownCount);
        Assert.Equal(1, result.DatabaseDownCount);
        Assert.Equal(1, result.ReplicationDownCount);
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ShouldReturnAllAffectedCounts_WhenBusinessServiceIdNotProvided()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "DIFFERENT_BS_ID",
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_456",
                HeatmapType = "database",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "ANOTHER_BS_ID",
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_789",
                HeatmapType = "replication",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "YET_ANOTHER_BS_ID",
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_101",
                HeatmapType = "server",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GroupHeatMapTypeByServiceId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ServerDownCount);
        Assert.Equal(0, result.DatabaseDownCount);
        Assert.Equal(0, result.ReplicationDownCount);
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ShouldReturnAssignedInfraObjects_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        infraObject.ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c"; // This is in assigned infras
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var businessServiceId = businessService.ReferenceId;
        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId, // Assigned infra object
                EntityId = "E_123",
                HeatmapType = "server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = "UNASSIGNED_INFRA_ID", // Non-assigned infra object
                EntityId = "E_456",
                HeatmapType = "database",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(true);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new HeatMapStatusViewRepository(_dbContext, mockUserService.Object);

        // Act
        var result = await repositoryNotAllInfra.GroupHeatMapTypeByServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ServerDownCount);
        Assert.Equal(0, result.DatabaseDownCount); // Not assigned, so not counted
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ShouldHandleEmptyResults()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repositoryParent.GroupHeatMapTypeByServiceId("NON_EXISTENT_BS_ID");

        // Assert
        Assert.Null(result); // FirstOrDefaultAsync returns null when no results
    }

    [Fact]
    public async Task GroupHeatMapTypeByServiceId_ShouldHandleWhitespaceBusinessServiceId()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessSeviceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                InfraObjectId = infraObject.ReferenceId,
                EntityId = "E_123",
                HeatmapType = "server",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryParent.GroupHeatMapTypeByServiceId("   ");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.ServerDownCount); // Should count all affected items
    }

    #endregion

}
