﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class IncidentLogsServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public IncidentLogsService Service { get; }

    public CreateIncidentLogsCommand CreateCommand { get; }
    public UpdateIncidentLogsCommand UpdateCommand { get; }
    public GetIncidentLogsPaginatedQuery PaginatedQuery { get; }
    public BaseResponse BaseResponse { get; }
    public IncidentLogsDetailVm DetailVm { get; }
    public List<IncidentLogsListVm> ListVm { get; }
    public PaginatedResult<IncidentLogsListVm> PaginatedResult { get; }

    public IncidentLogsServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new IncidentLogsService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateIncidentLogsCommand>();
        UpdateCommand = Fixture.Create<UpdateIncidentLogsCommand>();
        PaginatedQuery = Fixture.Create<GetIncidentLogsPaginatedQuery>();
        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<IncidentLogsDetailVm>();
        ListVm = Fixture.CreateMany<IncidentLogsListVm>(3).ToList();
        PaginatedResult = Fixture.Create<PaginatedResult<IncidentLogsListVm>>();
    }
}