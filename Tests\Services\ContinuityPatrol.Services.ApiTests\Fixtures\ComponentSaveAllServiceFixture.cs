﻿using AutoFixture;
using ContinuityPatrol.Application.Features.ComponentSaveAll.Queries.GetDetail;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class ComponentSaveAllServiceFixture
{
    public string ComponentId { get; }
    public ComponentSaveAllDetailVm ComponentDetailVm { get; }

    public ComponentSaveAllServiceFixture()
    {
        var fixture = new Fixture();

        ComponentId = fixture.Create<string>();
        ComponentDetailVm = fixture.Create<ComponentSaveAllDetailVm>();
    }
}