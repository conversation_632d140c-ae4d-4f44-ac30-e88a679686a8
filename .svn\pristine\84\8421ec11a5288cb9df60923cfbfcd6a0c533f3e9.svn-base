﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;

public class UserServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserService Service { get; }

    public IFixture Fixture { get; }

    public UserServiceFixture()
    {
        Fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserService(ClientMock.Object);
    }
}