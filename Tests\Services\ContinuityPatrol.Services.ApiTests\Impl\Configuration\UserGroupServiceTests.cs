﻿using ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserGroupServiceTests : IClassFixture<UserGroupServiceFixture>
{
    private readonly UserGroupServiceFixture _fixture;

    public UserGroupServiceTests(UserGroupServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);

        _fixture.ClientMock.Verify(x => x.ClearCache("GetCompanyNamesOnLogin"), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);

        _fixture.ClientMock.Verify(x => x.ClearCache("GetCompanyNamesOnLogin"), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        var id = "group123";

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);

        _fixture.ClientMock.Verify(x => x.ClearCache("GetCompanyNamesOnLogin"), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedUserGroups_Should_Return_PaginatedResult()
    {
        var query = new GetUserGroupPaginatedListQuery { PageNumber = 1, PageSize = 10 };

        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<UserGroupListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedUserGroups(query);

        Assert.NotNull(result);
        Assert.Equal(_fixture.PaginatedResult.TotalCount, result.TotalCount);
    }

    [Fact]
    public async Task IsGroupNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsGroupNameExist("Admins", "1");

        Assert.True(result);
    }

    [Fact]
    public async Task GetUserGroupList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<UserGroupListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserGroupList);

        var result = await _fixture.Service.GetUserGroupList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserGroupList.Count, result.Count);
    }
}
