﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentDaily.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class IncidentDailyServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public IncidentDailyService Service { get; }

    public CreateIncidentDailyCommand CreateCommand { get; }
    public UpdateIncidentDailyCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public IncidentDailyDetailVm DetailVm { get; }
    public List<IncidentDailyListVm> ListVm { get; }
    public GetIncidentDailyPaginatedQuery PaginatedQuery { get; }
    public PaginatedResult<IncidentDailyListVm> PaginatedResult { get; }

    public IncidentDailyServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new IncidentDailyService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateIncidentDailyCommand>();
        UpdateCommand = Fixture.Create<UpdateIncidentDailyCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<IncidentDailyDetailVm>();
        ListVm = Fixture.CreateMany<IncidentDailyListVm>(3).ToList();
        PaginatedQuery = Fixture.Create<GetIncidentDailyPaginatedQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<IncidentDailyListVm>>();
    }
}