﻿using Moq;
using RestSharp;
using Xunit;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BulkImportServiceTests : IClassFixture<BulkImportServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BulkImportService _service;
    private readonly BulkImportServiceFixture _fixture;

    public BulkImportServiceTests(BulkImportServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BulkImportService(_clientMock.Object);
    }

    [Fact]
    public async Task CreateBulkImport_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateBulkImport(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task NextBulkImportAction_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.NextBulkImportAction(_fixture.NextCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task RollBackBulkImportAction_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.RollBackBulkImportAction(_fixture.RollbackCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}