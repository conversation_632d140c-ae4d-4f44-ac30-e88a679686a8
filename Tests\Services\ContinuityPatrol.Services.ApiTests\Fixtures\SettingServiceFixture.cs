﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Queries.GetBySKey;
using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using System.Collections.Generic;

public class SettingServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SettingService Service { get; }

    public CreateSettingCommand CreateCommand { get; }
    public UpdateSettingCommand UpdateCommand { get; }
    public GetSettingPaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public string Id { get; }
    public string Key { get; }

    public List<SettingListVm> ListVm { get; }
    public PaginatedResult<SettingListVm> PaginatedResult { get; }
    public GetSettingBySKeyVm SKeyVm { get; }

    public SettingServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SettingService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSettingCommand>();
        UpdateCommand = fixture.Create<UpdateSettingCommand>();
        PaginatedQuery = fixture.Create<GetSettingPaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        Id = fixture.Create<string>();
        Key = fixture.Create<string>();
        ListVm = fixture.Create<List<SettingListVm>>();
        PaginatedResult = fixture.Create<PaginatedResult<SettingListVm>>();
        SKeyVm = fixture.Create<GetSettingBySKeyVm>();
    }
}
