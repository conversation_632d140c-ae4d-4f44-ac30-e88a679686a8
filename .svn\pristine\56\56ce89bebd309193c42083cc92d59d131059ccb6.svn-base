﻿using AutoFixture;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration.Fixtures;

public class CredentialProfileServiceFixture
{
    public List<CredentialProfileListVm> CredentialProfileList { get; }
    public List<CredentialProfileNameVm> CredentialProfileNameList { get; }
    public CredentialProfileDetailVm DetailVm { get; }
    public PaginatedResult<CredentialProfileListVm> PaginatedResult { get; }
    public BaseResponse BaseResponse { get; }
    public CreateCredentialProfileCommand CreateCommand { get; }
    public UpdateCredentialProfileCommand UpdateCommand { get; }
    public GetCredentialProfilePaginatedListQuery PaginatedListQuery { get; }

    public CredentialProfileServiceFixture()
    {
        var fixture = new Fixture();

        CredentialProfileList = fixture.Create<List<CredentialProfileListVm>>();
        CredentialProfileNameList = fixture.Create<List<CredentialProfileNameVm>>();
        DetailVm = fixture.Create<CredentialProfileDetailVm>();
        PaginatedResult = fixture.Create<PaginatedResult<CredentialProfileListVm>>();
        BaseResponse = fixture.Create<BaseResponse>();
        CreateCommand = fixture.Create<CreateCredentialProfileCommand>();
        UpdateCommand = fixture.Create<UpdateCredentialProfileCommand>();
        PaginatedListQuery = fixture.Create<GetCredentialProfilePaginatedListQuery>();
    }
}