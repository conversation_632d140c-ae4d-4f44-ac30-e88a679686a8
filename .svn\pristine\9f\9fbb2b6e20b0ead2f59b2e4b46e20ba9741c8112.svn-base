﻿using AutoFixture;
using AutoFixture.AutoMoq;
using Moq;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

public class SiteTypeServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SiteTypeService Service { get; }

    public CreateSiteTypeCommand CreateCommand { get; }
    public UpdateSiteTypeCommand UpdateCommand { get; }
    public GetSiteTypePaginatedListQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public List<SiteTypeListVm> SiteTypeList { get; }
    public PaginatedResult<SiteTypeListVm> PaginatedResult { get; }

    public string SiteTypeId { get; }
    public string SiteTypeName { get; }

    public SiteTypeServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SiteTypeService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSiteTypeCommand>();
        UpdateCommand = fixture.Create<UpdateSiteTypeCommand>();
        PaginatedQuery = fixture.Create<GetSiteTypePaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        SiteTypeList = fixture.Create<List<SiteTypeListVm>>();
        PaginatedResult = fixture.Create<PaginatedResult<SiteTypeListVm>>();

        SiteTypeId = fixture.Create<string>();
        SiteTypeName = fixture.Create<string>();
    }
}