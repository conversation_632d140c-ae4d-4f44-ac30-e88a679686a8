﻿using ContinuityPatrol.Application.Features.Site.Queries.GetByType;
using ContinuityPatrol.Application.Features.Site.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Site.Queries.GetSiteByCompanyId;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SiteServiceTests : IClassFixture<SiteServiceFixture>
{
    private readonly SiteServiceFixture _fixture;

    public SiteServiceTests(SiteServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.SiteId);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSitePaginatedList_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<SiteListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetSitePaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsSiteNameExist_Should_Return_True()
    {
        _fixture.ClientMock.Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsSiteNameExist(_fixture.SiteName, _fixture.SiteId);

        Assert.True(result);
    }

    [Fact]
    public async Task GetSiteByTypeAndCompanyId_Should_Return_List()
    {
        _fixture.ClientMock.Setup(x => x.Get<List<SiteBySiteTypeVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SiteByTypeList);

        var result = await _fixture.Service.GetSiteByTypeAndCompanyId(_fixture.CompanyId, _fixture.SiteTypeId);

        Assert.Equal(_fixture.SiteByTypeList, result);
    }

    [Fact]
    public async Task GetSiteNames_Should_Return_List()
    {
        _fixture.ClientMock.Setup(x => x.Get<List<SiteNameVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SiteNames);

        var result = await _fixture.Service.GetSiteNames();

        Assert.Equal(_fixture.SiteNames, result);
    }

    [Fact]
    public async Task GetSiteByCompanyId_Should_Return_List()
    {
        _fixture.ClientMock.Setup(x => x.Get<List<GetSiteByCompanyIdVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SitesByCompanyId);

        var result = await _fixture.Service.GetSiteByCompanyId(_fixture.CompanyId);

        Assert.Equal(_fixture.SitesByCompanyId, result);
    }

    [Fact]
    public async Task GetSiteById_Should_Return_Detail()
    {
        _fixture.ClientMock.Setup(x => x.Get<SiteDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SiteDetail);

        var result = await _fixture.Service.GetSiteById(_fixture.SiteId);

        Assert.Equal(_fixture.SiteDetail, result);
    }

    [Fact]
    public async Task GetSites_Should_Return_List()
    {
        _fixture.ClientMock.Setup(x => x.Get<List<SiteListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SiteList);

        var result = await _fixture.Service.GetSites();

        Assert.Equal(_fixture.SiteList, result);
    }
}
