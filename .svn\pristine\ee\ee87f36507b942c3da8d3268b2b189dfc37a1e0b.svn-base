﻿using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class AdPasswordJobServiceTests : IClassFixture<AdPasswordJobServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly AdPasswordJobService _service;
    private readonly AdPasswordJobServiceFixture _fixture;

    public AdPasswordJobServiceTests(AdPasswordJobServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new AdPasswordJobService(_clientMock.Object);
    }

    [Fact]
    public async Task GetAdPasswordJobList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<AdPasswordJobListVm>>(It.IsAny<RestRequest>(), "GetAdPasswordJobList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetAdPasswordJobList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("test-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _clientMock.Setup(x => x.Get<AdPasswordJobDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsAdPasswordJobNameExist_ShouldReturnTrue()
    {
        _clientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsAdPasswordJobNameExist("JobName", "some-id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedAdPasswordJobs_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(x => x.Get<PaginatedResult<AdPasswordJobListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetPaginatedAdPasswordJobs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
