﻿using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BulkImportOperationGroupServiceTests : IClassFixture<BulkImportOperationGroupServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BulkImportOperationGroupService _service;
    private readonly BulkImportOperationGroupServiceFixture _fixture;

    public BulkImportOperationGroupServiceTests(BulkImportOperationGroupServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BulkImportOperationGroupService(_clientMock.Object);
    }

    [Fact]
    public async Task GetBulkImportOperationGroupList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<BulkImportOperationGroupListVm>>(It.IsAny<RestRequest>(), "GetBulkImportOperationGroupList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBulkImportOperationGroupList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("some-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _clientMock.Setup(x => x.Get<BulkImportOperationGroupDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetBulkImportOperationGroupByOperationId_ShouldReturnList()
    {
        _clientMock.Setup(x => x.Get<List<BulkImportOperationGroupListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBulkImportOperationGroupByOperationId("op-id");

        Assert.Equal(_fixture.ListVm, result);
    }
}
