﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;
using Microsoft.Data.SqlClient;
using MySqlConnector;
using Npgsql;
using System.Data;
using System.Data.Common;

namespace ContinuityPatrol.Persistence.Repositories;

public class OracleRACMonitorLogsRepository : BaseRepository<OracleRACMonitorLogs>, IOracleRacMonitorLogsRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IConfiguration _config;
    public OracleRACMonitorLogsRepository(ApplicationDbContext dbContext, IConfiguration config) : base(dbContext)
    {
        _dbContext = dbContext;
        _config = config;
    }

    public async Task<List<OracleRACMonitorLogs>> GetByInfraObjectId(string infraObjectId, string startDate,
        string endDate)
    {
        
        var tableName = GetTableName<OracleRACMonitorLogs>();

        var config = _config.GetConnectionString("Default");
        var dbProvider = _config.GetConnectionString("DBProvider");

        var decryptString = CryptographyHelper.Decrypt(config);

        var dbProviderString = CryptographyHelper.Decrypt(dbProvider);

        var schema = GetDatabaseNameFromConnectionString(decryptString, dbProviderString);
        var tableExist = await IsTableExistAsync($"{tableName}_bkp", schema, dbProviderString);

        if (tableExist)
        {
            string sqlQuery = string.Empty;
            if (dbProviderString.ToLower().Equals("oracle"))
            {
                var startdate = startDate.ToDateTime();
                var enddate = endDate.ToDateTime();
                startDate = startdate.ToString("dd-MM-yyyy");
                endDate = enddate.ToString("dd-MM-yyyy");
                sqlQuery = $"SELECT * FROM \"{schema}\".\"{tableName}_bkp\" WHERE TRUNC(\"CreatedDate\") >= TO_DATE('{startDate}', 'DD-MM-YYYY') AND TRUNC(\"CreatedDate\") <= TO_DATE('{endDate}', 'DD-MM-YYYY')";
            }
            else
            {
                sqlQuery = $"SELECT * FROM {tableName}_bkp WHERE CreatedDate >= {startDate} AND CreatedDate <= {endDate}";
            }

            var logTable = await _dbContext.OracleRacMonitorLogs.Active()
                .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() && x.CreatedDate.Date <= endDate.ToDateTime()).OrderBy(x => x.CreatedDate)
                .ToListAsync();

            var logBackupTable = await _dbContext.OracleRacMonitorLogs
                .FromSqlRaw(sqlQuery)
                .ToListAsync();

            var combinedLogs = logTable.Concat(logBackupTable).ToList();

            return combinedLogs;
        }

        return await _dbContext.OracleRacMonitorLogs.Active()
            .Where(x => x.InfraObjectId.Equals(infraObjectId) &&
                        x.CreatedDate.Date >= startDate.ToDateTime() &&
                        x.CreatedDate.Date <= endDate.ToDateTime())
            .ToListAsync();
    }

    public async Task<List<OracleRACMonitorLogs>> GetDetailByType(string type)
    {
        return await _dbContext.OracleRacMonitorLogs.Active().Where(x => x.Type.Equals(type)).ToListAsync();
    }
    private string GetDatabaseNameFromConnectionString(string connectionString, string provider)
    {
        DbConnectionStringBuilder builder = provider.ToLower() switch
        {
            "mysql" => new MySqlConnectionStringBuilder(connectionString),
            "oracle" => new OracleConnectionStringBuilder(connectionString),
            "mssql" => new SqlConnectionStringBuilder(connectionString),
            "npgsql" => new NpgsqlConnectionStringBuilder(connectionString),
            _ => throw new ArgumentException("Unsupported provider name.")
        };
        if (builder.TryGetValue("Database", out var databaseName)) return databaseName.ToString();
        else
        {
            builder.TryGetValue("User Id", out var dbName); return dbName.ToString().ToUpper();
        }

        throw new ArgumentException("Unable to extract database name from connection string.");
    }
    public virtual string GetTableName<T>()
    {
        var entityType = DbContext.Model.FindEntityType(typeof(T));

        var tableName = entityType?.GetTableName();

        return tableName;
    }
    public virtual async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
    {
        if (!DbContext.Database.IsRelational())
            return false;

        var connection = DbContext.Database.GetDbConnection();

        if (connection.State != ConnectionState.Open)
            connection.Open(); // Open the connection synchronously if it's closed

        var schemaTable = providerName.ToLower().Equals("oracle") ? await connection.GetSchemaAsync("Tables", new[] { schemaName, tableName }) : await connection.GetSchemaAsync("Tables", new[] { null, schemaName, tableName });

        //return schemaTable.Rows.OfType<DataRow>().Any(row =>
        //    row["TABLE_SCHEMA"].ToString() == schemaName &&
        //    row["TABLE_NAME"].ToString() == tableName);

        var schema = providerName.ToLower().Equals("oracle") ? "OWNER" : "TABLE_SCHEMA";
        return schemaTable.Rows.OfType<DataRow>().Any(row =>
            row[schema].ToString() == schemaName &&
            row["TABLE_NAME"].ToString() == tableName);

        // Check if the table exists by querying the schema
        //var tableExists = (await connection.GetSchemaAsync("Tables"))
        //    .Rows.OfType<DataRow>().Any(row => row["TABLE_NAME"].ToString() == tableName);
    }
}