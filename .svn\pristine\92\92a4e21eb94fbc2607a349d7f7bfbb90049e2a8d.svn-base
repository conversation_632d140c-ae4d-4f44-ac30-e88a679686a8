﻿using ContinuityPatrol.Application.Features.Node.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class NodeServiceTests : IClassFixture<NodeServiceFixture>
{
    private readonly NodeServiceFixture _fixture;

    public NodeServiceTests(NodeServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        var nodeId = "test-id";
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(nodeId);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetNodeList_ShouldReturnList()
    {
        var expected = _fixture.FixtureGen.Create<List<NodeListVm>>();
        _fixture.ClientMock
            .Setup(c => c.Get<List<NodeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetNodeList();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetNodeNames_ShouldReturnList()
    {
        var expected = _fixture.FixtureGen.Create<List<NodeNameVm>>();
        _fixture.ClientMock
            .Setup(c => c.Get<List<NodeNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetNodeNames();

        Assert.Equal(expected, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        var id = "sample-id";
        _fixture.ClientMock
            .Setup(c => c.Get<NodeDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.NodeDetail);

        var result = await _fixture.Service.GetByReferenceId(id);

        Assert.Equal(_fixture.NodeDetail, result);
    }

    [Fact]
    public async Task IsNodeNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsNodeNameExist("TestNode", "123");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedNodes_ShouldReturnPaginatedResult()
    {
        var expected = _fixture.FixtureGen.Create<PaginatedResult<NodeListVm>>();
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<NodeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(expected);

        var result = await _fixture.Service.GetPaginatedNodes(_fixture.PaginatedQuery);

        Assert.Equal(expected, result);
    }
}
