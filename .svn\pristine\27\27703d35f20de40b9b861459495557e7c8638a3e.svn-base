﻿using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class DrReadyServiceTests : IClassFixture<DrReadyServiceFixture>
{
    private readonly DrReadyServiceFixture _fixture;

    public DrReadyServiceTests(DrReadyServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateInfraObjectSchedulerStatus_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateInfraObjectSchedulerStatus(_fixture.StatusCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateInfraObjectSchedulerState_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateInfraObjectSchedulerState(_fixture.StateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("sample-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetDrReadyPaginatedList_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<InfraObjectSchedulerListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetDrReadyPaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetPaginatedInfraObjectSchedulerLogs_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<InfraObjectSchedulerLogsListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.LogsPaginatedResult);

        var result = await _fixture.Service.GetPaginatedInfraObjectSchedulerLogs(_fixture.LogsQuery);

        Assert.Equal(_fixture.LogsPaginatedResult, result);
    }
}
