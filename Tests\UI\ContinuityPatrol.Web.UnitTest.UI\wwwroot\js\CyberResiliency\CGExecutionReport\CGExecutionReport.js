﻿
let types = [], dataTable, selectedValues = [];
function ExecutionHistorydebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {

    let selectedValues = [];
    let dataTable = $('#CGExecutionTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/CyberResiliency/CGExecutionReport/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "workflowType" : sortIndex === 2 ? "cgCount" : sortIndex === 3 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.type = 'Cyber Resiliency'
                    d.startDate = $('#startDate').find("input[type=date]").val() === "" ? '' : $('#startDate').find("input[type=date]").val()
                    d.endDate = $('#endDate').find("input[type=date]").val() === "" ? '' : $('#endDate').find("input[type=date]").val();
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    if (json?.data?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                        if (json.data.data) {
                            json.data.data = json?.data?.data.reverse();
                        }
                       
                        
                    }
                    return json?.data?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "jobId", "name": "Job Id", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.jobId || '-'}" > ${row.jobId || '-'}</span></td>`
                        }
                        return data;
                    }
                },

                {
                    "data": "enableStartTime", "name": "Start Time", "autoWidth": true,

                    "render": function (data, type, row) {
                        let splitDate = row.enableStartTime.replace('T', ' ').split(' ')[0]
                        let splitTime = row.enableStartTime.replace('T', ' ').split(' ')[1].split(':')
                        let modifyTime = splitTime[0] + ':' + splitTime[1]

                        if (type === 'display') {
                            return `<td><span title="${splitDate + ' ' + modifyTime || 'NA'}">${splitDate + ' ' + modifyTime || 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "enableEndTime", "name": "End Time", "autoWidth": true,

                    "render": function (data, type, row) {

                        let splitDate = row.enableEndTime.replace('T', ' ').split(' ')[0]
                        let splitTime = row.enableEndTime.replace('T', ' ').split(' ')[1].split(':')
                        let modifyTime = splitTime[0] + ':' + splitTime[1]

                        if (type === 'display') {
                            return `<td><span title="${splitDate + ' ' + modifyTime || 'NA'}">${splitDate + ' ' + modifyTime || 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "cgCount", "name": "CG Count", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            let status = row.status
                            status = status && Number(status?.slice(0, status.length - 1))
                            let statusBadge = status === 100 ? 'bg-success' : status >= 75 && status <= 99 ? 'bg-warning' : status <= 74 && status >= 1 ? 'bg-danger' : 'bg-secondary';


                            return `<td>
                            <div class="d-flex align-items-center gap-3">
    <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="${status}" aria-valuemin="0" aria-valuemax="100" style="height: 13px;font-size: 10px;width:70%">
  <div class="progress-bar ${statusBadge}" style="width: ${status}%"><span title="${status}"> ${status + '%' || "0%"}</span></div>
</div>
<span title="${row.cgCount}"> ${row.cgCount || "NA"}</span>
                            </div>
                        

                           </td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {

                            let fontColor = row.conditionActionId === 3 ? 'text-danger' : row.conditionActionId === 0 ? 'text-success' : row.conditionActionId === 5 ? 'text-primary' : '';
                            let icon = row.conditionActionId === 3 ? 'cp-error' : row.conditionActionId === 0 ? 'cp-success' : row.conditionActionId === 5 ? 'cp-reload cp-animate' : '';
                            let text = row.conditionActionId === 3 ? 'Error' : row.conditionActionId === 0 ? 'Success' : row.conditionActionId === 5 ? 'Running' : '-';

                            return `<td><span class="${fontColor}"><i class='${icon} me-1'></i>${text}</span></td>`
                        }
                        return data;
                    }
                }, 
                {
                    "data": null, "name": "Condition Operation", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {

                            let buttonContainer = `<td><span class="d-flex gap-1" >
                              <button type="button" class="btn btn-sm btn-outline-primary border-0 updateOperations" index="${1}" operationId="${row.workflowOperationId}"><i class="cp-circle-playnext fs-7 me-1"></i><span class="align-middle">Next</span></button>
                              <button type="button" class="btn btn-sm btn-outline-primary border-0 updateOperations" index="${2}" operationId="${row.workflowOperationId}"><i class="cp-retry fs-7 me-1"></i><span class="align-middle">Retry</span></button>
                              <button type="button" class="btn btn-sm btn-outline-primary border-0 updateOperations" index="${4}" operationId="${row.workflowOperationId}"><i class="cp-success fs-7 me-1"></i><span class="align-middle">Mark As Completed</span></button>
                            <button class="btn btn-sm btn-outline-primary d-none"  style="--bs-btn-padding-y: .15rem; --bs-btn-padding-x: .4rem; --bs-btn-font-size: .65rem;">Skip</button >
                            <button class="btn btn-sm btn-outline-primary d-none" style="--bs-btn-padding-y: .15rem; --bs-btn-padding-x: .4rem; --bs-btn-font-size: .65rem;">Retry</button>
                            <button class="btn btn-sm btn-outline-success d-none" style="--bs-btn-padding-y: .15rem; --bs-btn-padding-x: .4rem; --bs-btn-font-size: .65rem;">Mark As Completed</button></span>
                            </td>`
                            let emptyContainer = `<td class="text-align"><span>-</span></td>`

                            return row.conditionActionId === 3 ? buttonContainer : emptyContainer
                        }
                        return data;
                    }
                }, 
                {
                    "render": function (data, type, row) {
                        return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Download" class="btnReportDownload" operationId='${row.workflowOperationId}' workflowName='${row.WorkFlowName}'>
                               <i class="cp-download text-primary"></i>
                                </span>
                                
                                </span>
                            </div>`;
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }

        const NameCheckbox = $("#Name");
        const CountryNameCheckbox = $("#CountryName");

        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (CountryNameCheckbox.is(':checked')) {
            selectedValues.push(CountryNameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $("#startDate,#endDate").on("keypress", function (e) {
        e.preventDefault()
    })
    $("#endDate, #startDate").on('change', function () {

        if ($('#endDate').find("input[type=date]").val() && $('#startDate').find("input[type=date]").val()) {
            dataTable.ajax.reload()
            return true;
        }

        if ($('#endDate').find("input[type=date]").val() || $('#startDate').find("input[type=date]").val()) {
            $('#btnRefreshDate').show()   
        }

        
        if ($('#startDate').find("input[type=date]").val() == "" && $('#endDate').find("input[type=date]").val()) {
            $("#startdate-error").text("Select the start date before end date").addClass('field-validation-error');
            return false;
        }
        else if ($('#endDate').find("input[type=date]").val() != "" && $('#startDate').find("input[type=date]").val() > $('#endDate').find("input[type=date]").val()) {
            $("#startdate-error").text("Start date lesser than end date").addClass('field-validation-error');
            return false;
        } else if ($('#endDate').find("input[type=date]").val() == "" || $('#startDate').find("input[type=date]").val()) {
            $('#endDate').find("input[type=date]").val()
            $("#startdate-error").text('').removeClass('field-validation-error');
            return true;
        } else {
            $("#startdate-error").text('').removeClass('field-validation-error');
            return true;
        }
      
    })

    $('#btnRefreshDate').on('click', function () {
        $("#start_date, #end_date").val('');
        dataTable.ajax.reload()
        setTimeout(() => {
            $(this).hide()
        },300)
    })

    $(document).on('click', '.btnReportDownload', async function () {
        let operationId = $(this).attr('operationId')
        let workflowName = $(this).attr('workflowName')

        const url = `/CyberResiliency/CGExecutionReport/DownloadReport?workflowOperationId=${operationId}&workflowName=${workflowName}`;
        const response = await fetch(url);
        if (response.ok) {
            const blob = await response.blob();
            var alertClass, reportIconClass, message;
            if (blob.size > 0 && blob.type === "application/pdf") {
                const DateTime = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
                downloadFile(blob, "CGExecutionReport " + "_" + + DateTime + ".pdf", "application/pdf");
                alertClass = "success-toast";
                reportIconClass = "cp-check toast_icon";
                message = "CG Execution Report downloaded successfully";
            }
            else {
                alertClass = "warning-toast";
                reportIconClass = "cp-exclamation toast_icon";
                message = "No Data Found";
            }
        }
        else {
            alertClass = "warning-toast";
            reportIconClass = "cp-exclamation toast_icon";
            message = "CG Execution Report Download Error";
        }
        $('#alertClass').removeClass().addClass(alertClass);
        $('#icon_Detail').removeClass().addClass(reportIconClass);
        $('#notificationAlertmessage').text(message);
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');


    })
})
function downloadFile(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}

$(document).on('click', '.updateOperations',async function () {
    let operationId = $(this).attr('operationId')
    let index = Number($(this).attr('index'))

    let getBtnContainer = $(this).parent().parent()

    let data = {
        workflowOperationId: operationId, 
        conditionActionId: index,
         __RequestVerificationToken: gettoken()
    }

    await $.ajax({
        type: "POST",
        url: RootUrl + "CyberResiliency/CGExecutionReport/UpdateCondition",
        data: data,
        dataType: "json",
        success: function (result) {
            if (result.data.success) {
                notificationAlert('success', result.data.message)
                if (index == 1) {
                    getBtnContainer.prev().empty().append(`<span class="${'text-info'}"><i class='${'cp-skipped'} me-1'></i>${'Skip'}</span>`)
                } else if (index == 2) {
                    getBtnContainer.prev().empty().append(`<span class="${'text-warning'}"><i class='${'cp-retry'} me-1'></i>${'Retry'}</span>`)
                } else if (index == 2) {
                    getBtnContainer.prev().empty().append(`<span class="${'text-success'}"><i class='${'cp-success'} me-1'></i>${'Completed'}</span>`)
                }
                getBtnContainer.css('pointer-events', 'none').css('opacity', '0.5')
            } else {
                errorNotification(result)
            }
        }
    })

})
