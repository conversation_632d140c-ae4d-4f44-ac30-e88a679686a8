﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendTestEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class SmtpConfigurationServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public SmtpConfigurationService Service { get; }

    public CreateSmtpConfigurationCommand CreateCommand { get; }
    public UpdateSmtpConfigurationCommand UpdateCommand { get; }
    public SendTestEmailCommand TestEmailCommand { get; }
    public SendEmailCommand EmailCommand { get; }
    public GetSmtpConfigurationPaginatedListQuery Query { get; }

    public BaseResponse BaseResponse { get; }
    public SmtpConfigurationListVm ListVm { get; }
    public SmtpConfigurationDetailVm DetailVm { get; }
    public PaginatedResult<SmtpConfigurationListVm> PaginatedResult { get; }
    public string Id { get; }

    public SmtpConfigurationServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new SmtpConfigurationService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateSmtpConfigurationCommand>();
        UpdateCommand = fixture.Create<UpdateSmtpConfigurationCommand>();
        TestEmailCommand = fixture.Create<SendTestEmailCommand>();
        EmailCommand = fixture.Create<SendEmailCommand>();
        Query = fixture.Create<GetSmtpConfigurationPaginatedListQuery>();

        BaseResponse = fixture.Create<BaseResponse>();
        ListVm = fixture.Create<SmtpConfigurationListVm>();
        DetailVm = fixture.Create<SmtpConfigurationDetailVm>();
        PaginatedResult = fixture.Create<PaginatedResult<SmtpConfigurationListVm>>();
        Id = fixture.Create<string>();
    }
}
