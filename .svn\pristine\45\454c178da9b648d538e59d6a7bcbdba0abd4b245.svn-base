﻿using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserInfraObjectServiceTests : IClassFixture<UserInfraObjectServiceFixture>
{
    private readonly UserInfraObjectServiceFixture _fixture;

    public UserInfraObjectServiceTests(UserInfraObjectServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task GetUserInfraObjects_Should_Return_Expected_Result()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<GetUserInfraObjectByBusinessServiceVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BusinessServiceVm);

        var result = await _fixture.Service.GetUserInfraObjects(_fixture.CompanyId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BusinessServiceVm, result);
    }

    [Fact]
    public async Task GetUserById_Should_Return_Expected_Result()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<GetUserInfraObjectByUserIdVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserIdVm);

        var result = await _fixture.Service.GetUserById(_fixture.UserId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserIdVm, result);
    }

    [Fact]
    public async Task GetByUserIdAndProperties_Should_Return_Expected_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<GetByUserIdAndPropertiesVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PropertiesVmList);

        var result = await _fixture.Service.GetByUserIdAndProperties();

        Assert.NotNull(result);
        Assert.Equal(_fixture.PropertiesVmList, result);
    }
}