﻿using AutoFixture;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class AdPasswordExpireServiceFixture
{
    public CreateAdPasswordExpireCommand CreateCommand { get; }
    public UpdateAdPasswordExpireCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<AdPasswordExpireListVm> ListVm { get; }
    public AdPasswordExpireDetailVm DetailVm { get; }
    public GetAdPasswordExpirePaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<AdPasswordExpireListVm> PaginatedResult { get; }

    public AdPasswordExpireServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateAdPasswordExpireCommand>();
        UpdateCommand = fixture.Create<UpdateAdPasswordExpireCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        ListVm = fixture.CreateMany<AdPasswordExpireListVm>(3).ToList();
        DetailVm = fixture.Create<AdPasswordExpireDetailVm>();
        PaginatedQuery = fixture.Create<GetAdPasswordExpirePaginatedListQuery>();
        PaginatedResult = fixture.Create<PaginatedResult<AdPasswordExpireListVm>>();
    }
}