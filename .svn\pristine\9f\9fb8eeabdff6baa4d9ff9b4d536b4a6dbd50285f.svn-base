﻿using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class RoboCopyServiceTests : IClassFixture<RoboCopyServiceFixture>
{
    private readonly RoboCopyServiceFixture _fixture;

    public RoboCopyServiceTests(RoboCopyServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("sample-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturn_DetailVm()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<RoboCopyDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("sample-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetRoboCopyList_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<RoboCopyListVm>>(It.IsAny<RestRequest>(), "GetRoboCopyList"))
            .ReturnsAsync(_fixture.RoboList);

        var result = await _fixture.Service.GetRoboCopyList();

        Assert.Equal(_fixture.RoboList, result);
    }

    [Fact]
    public async Task IsRoboCopyNameExist_ShouldReturn_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsRoboCopyNameExist("test-name", "test-id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedRoboCopys_ShouldReturn_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<RoboCopyListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedRoboCopys(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
