﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Services.Api.Impl.Admin;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Admin;

public class LicenseInfoServiceTests : IClassFixture<LicenseInfoFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly LicenseInfoService _service;
    private readonly LicenseInfoFixture _fixture;

    public LicenseInfoServiceTests(LicenseInfoFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new LicenseInfoService(_clientMock.Object);
    }

    [Fact]
    public async Task GetLicenseInfo_ReturnsList()
    {
        _clientMock.Setup(c => c.Get<List<LicenseInfoListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetLicenseInfo();

        Assert.Equal(_fixture.ListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetLicenseInfoById_ReturnsDetailView()
    {
        _clientMock.Setup(c => c.Get<LicenseInfoDetailViewVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailViewVm);

        var result = await _service.GetLicenseInfoById(_fixture.Id);

       
    }

    [Fact]
    public async Task CreateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

       
    }

    [Fact]
    public async Task UpdateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        
    }

    [Fact]
    public async Task DeleteAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync(_fixture.Id);

       
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseId_ReturnsDetailList()
    {
        _clientMock.Setup(c => c.Get<List<LicenseInfoDetailVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailListVm);

        var result = await _service.GetLicenseInfoByLicenseId(_fixture.Id);

        Assert.Equal(_fixture.DetailListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetAvailableCountByLicenseId_ReturnsAvailableCount()
    {
        _clientMock.Setup(c => c.Get<AvailableCountVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.AvailableCount);

        var result = await _service.GetAvailableCountByLicenseId(_fixture.Id);

        
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceId_ReturnsList()
    {
        _clientMock.Setup(c => c.Get<List<LicenseInfoByBusinessServiceIdListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ByBusinessServiceIdList);

        var result = await _service.GetLicenseByBusinessServiceId(_fixture.Id);

        Assert.Equal(_fixture.ByBusinessServiceIdList.Count, result.Count);
    }

    [Fact]
    public async Task GetPaginatedLicenseInfo_ReturnsPaginatedResult()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<LicenseInfoListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetPaginatedLicenseInfo(_fixture.PaginatedQuery);

        
    }

    [Fact]
    public async Task GetLicenseInfoByEntity_ReturnsList()
    {
        _clientMock.Setup(c => c.Get<List<LicenseInfoByEntityListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ByEntityList);

        var result = await _service.GetLicenseInfoByEntity(_fixture.Id, _fixture.Entity, _fixture.EntityType);

        Assert.Equal(_fixture.ByEntityList.Count, result.Count);
    }

    [Fact]
    public async Task GetLicenseInfoByType_ReturnsList()
    {
        _clientMock.Setup(c => c.Get<List<LicenseInfoTypeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ByTypeList);

        var result = await _service.GetLicenseInfoByType(_fixture.Id, _fixture.Type, _fixture.EntityType);

        Assert.Equal(_fixture.ByTypeList.Count, result.Count);
    }
}
