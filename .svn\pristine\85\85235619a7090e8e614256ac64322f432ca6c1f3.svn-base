﻿using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class SiteLocationServiceTests : IClassFixture<SiteLocationServiceFixture>
{
    private readonly SiteLocationServiceFixture _fixture;

    public SiteLocationServiceTests(SiteLocationServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSiteLocationList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<SiteLocationListVm>>(It.IsAny<RestSharp.RestRequest>(), "GetSiteLocationList"))
            .ReturnsAsync(_fixture.ListResponse);

        var result = await _fixture.Service.GetSiteLocationList();

        Assert.Equal(_fixture.ListResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<SiteLocationDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.DetailResponse);

        var result = await _fixture.Service.GetByReferenceId(_fixture.Id);

        Assert.Equal(_fixture.DetailResponse, result);
    }

    [Fact]
    public async Task IsSiteLocationNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsSiteLocationNameExist(_fixture.Name, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedSiteLocations_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<SiteLocationListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedList);

        var result = await _fixture.Service.GetPaginatedSiteLocations(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedList, result);
    }
}
