﻿using ContinuityPatrol.Application.Features.Incident.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Incident.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class IncidentServiceTests : IClassFixture<IncidentServiceFixture>
{
    private readonly IncidentServiceFixture _fixture;

    public IncidentServiceTests(IncidentServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateIncident_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateIncident(_fixture.CreateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteIncident_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteIncident("test-id");

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task GetIncidentById_ShouldReturnDetailVm()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<GetIncidentDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetIncidentById("test-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetIncidentList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<IncidentListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetIncidentList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetPaginated_ShouldReturnPaginatedResult()
    {
        var query = _fixture.Fixture.Create<GetIncidentPaginatedListQuery>();

        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<IncidentListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResultVm);

        var result = await _fixture.Service.GetPaginated(query);

        Assert.Equal(_fixture.PaginatedResultVm, result);
    }

    [Fact]
    public async Task UpdateIncident_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateIncident(_fixture.UpdateCommand);

        Assert.Equal(_fixture.Response, result);
    }
}
