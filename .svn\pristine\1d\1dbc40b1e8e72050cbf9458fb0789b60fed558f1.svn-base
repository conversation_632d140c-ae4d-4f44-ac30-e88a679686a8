﻿QUnit.module("CGExecutionReport.js - Helper Functions", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`<a id="downloadLink"></a>`);
        window.URL.createObjectURL = () => "blob:url";
        document.body.appendChild = () => { };
        document.body.removeChild = () => { };
    });

    QUnit.test("ExecutionHistorydebounce delays execution", assert => {
        const done = assert.async();
        let called = false;
        const fn = () => { called = true; };
        const debounced = ExecutionHistorydebounce(fn, 200);
        debounced();
        setTimeout(() => {
            assert.ok(called, "Function executed after delay");
            done();
        }, 250);
    });

    QUnit.test("downloadFile creates link and clicks it", assert => {
        const blob = new Blob(["test"], { type: "application/pdf" });
        const fileName = "testfile.pdf";
        downloadFile(blob, fileName, "application/pdf");
        assert.ok(true, "downloadFile executed without error");
    });
});

QUnit.module("CGExecutionReport.js - Search Field", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input id="search-inp" />
            <input type="checkbox" id="Name" value="name:" checked />
            <input type="checkbox" id="CountryName" value="country:" checked />
            <div class="dataTables_empty"></div>
            <table id="CGExecutionTable"></table>
        `);

        window.selectedValues = [];

        window.dataTable = {
            ajax: {
                reload: function (cb) {
                    if (cb) {
                        cb({ recordsFiltered: 0 });
                    }
                    $('.dataTables_empty').text('No matching records found');
                    return this;
                }
            }
        };

        $.fn.DataTable = function () {
            return window.dataTable;
        };

        window.commonDebounce = function (func) {
            return function () {
                func.apply(this, arguments);
            };
        };
    });

    QUnit.test("Search field reloads table with filters", assert => {
        $('#search-inp').val("Test");

        const event = $.Event('input');
        const NameCheckbox = $("#Name");
        const CountryNameCheckbox = $("#CountryName");
        const inputValue = $('#search-inp').val();

        if (NameCheckbox.is(':checked')) {
            window.selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (CountryNameCheckbox.is(':checked')) {
            window.selectedValues.push(CountryNameCheckbox.val() + inputValue);
        }

        window.dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        });

        assert.deepEqual(
            window.selectedValues,
            ["name:Test", "country:Test"],
            "Filters are pushed to selectedValues"
        );
        assert.equal(
            $('.dataTables_empty').text(),
            "No matching records found",
            "Text updated as expected"
        );
    });
});

QUnit.module("CGExecutionReport.js - Date Filters", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <div id="startDate"><input type="date" value="2025-01-01"/></div>
            <div id="endDate"><input type="date" value="2025-01-02"/></div>
            <span id="startdate-error"></span>
            <button id="btnRefreshDate" style="display:none;">Refresh</button>
        `);

        window.dataTable = {
            ajax: {
                reload: () => {
                    $('#btnRefreshDate').show();
                }
            }
        };
    });

    QUnit.test("StartDate and EndDate change triggers reload", assert => {
        $('#startDate input').val("2025-01-01");
        $('#endDate input').val("2025-01-02");

        $('#startDate input').trigger("change");
        $('#endDate input').trigger("change");

        assert.ok(true, "Date change event handled");
    });

    QUnit.test("StartDate empty shows error", assert => {
        $('#qunit-fixture').html(`
        <div id="startDate"><input type="date" value="" /></div>
        <div id="endDate"><input type="date" value="2025-01-02" /></div>
        <span id="startdate-error"></span>
    `);
        $('#startDate input').val(""); 
        $('#endDate input').val("2025-01-02").trigger("change");
        const start = $('#startDate').find("input[type=date]").val();
        const end = $('#endDate').find("input[type=date]").val();
        if (start === "" && end) {
            $("#startdate-error").text("Select the start date before end date").addClass('field-validation-error');
        }
        assert.strictEqual($("#startdate-error").text(), "Select the start date before end date", "Validation error shown");
    });

    QUnit.test("btnRefreshDate click clears dates and hides button", assert => {
        const done = assert.async();

        $('#btnRefreshDate').trigger("click");
        setTimeout(() => {
            assert.ok(true, "Refresh logic executed");
            done();
        }, 300);
    });
});
QUnit.module("CGExecutionReport.js - Download Button", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <span class="btnReportDownload" operationId="123" workflowName="TestWorkflow"></span>
            <div id="alertClass"></div>
            <div id="icon_Detail"></div>
            <div id="notificationAlertmessage"></div>
            <div id="mytoastrdata"></div>
        `);

        window.fetch = async () => ({
            ok: true,
            blob: async () => new Blob(["PDF content"], { type: "application/pdf" })
        });

        window.downloadFile = () => { };
        window.$.fn.toast = () => ({ toast: () => { } });
    });

    QUnit.test("btnReportDownload click handles successful download", async assert => {
        const done = assert.async();

        $('.btnReportDownload').trigger("click");

        setTimeout(() => {
            assert.ok(true, "Download triggered");
            done();
        }, 500);
    });
});
QUnit.module("CGExecutionReport.js - updateOperations", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <div class="btn-wrapper">
                <div class="prev-status"></div>
                <span class="d-flex gap-1">
                    <button class="updateOperations" index="1" operationId="123"></button>
                </span>
            </div>
        `);

        window.gettoken = () => "mockToken";
        window.notificationAlert = () => { };
        window.errorNotification = () => { };
        window.RootUrl = "";

        window.$.ajax = async ({ type, url, data, success }) => {
            success({ data: { success: true, message: "Updated" } });
        };
    });

    QUnit.test("Clicking updateOperations sends update and modifies UI", async assert => {
        const done = assert.async();

        $(".updateOperations").trigger("click");
        setTimeout(() => {
            assert.ok(true, "Operation update triggered");
            done();
        }, 300);
    });
});