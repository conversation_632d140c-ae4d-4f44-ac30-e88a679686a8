﻿using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class TableAccessServiceTests : IClassFixture<TableAccessServiceFixture>
{
    private readonly TableAccessServiceFixture _fixture;

    public TableAccessServiceTests(TableAccessServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task GetTableAccessPaginatedList_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<TableAccessListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetTableAccessPaginatedList(_fixture.Query);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsTableAccessNameExist_Should_Return_True()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsTableAccessNameExist(_fixture.TableAccessName, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetTableNamesBySchemaName_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<GetTableNameListBySchemaVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.TableNames);

        var result = await _fixture.Service.GetTableNamesBySchemaName(_fixture.SchemaName);

        Assert.Equal(_fixture.TableNames, result);
    }

    [Fact]
    public async Task GetSchemaNames_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<SchemaNameListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SchemaNames);

        var result = await _fixture.Service.GetSchemaNames();

        Assert.Equal(_fixture.SchemaNames, result);
    }

    [Fact]
    public async Task GetAllTableAccesses_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<TableAccessListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.TableAccessList);

        var result = await _fixture.Service.GetAllTableAccesses();

        Assert.Equal(_fixture.TableAccessList, result);
    }
}
