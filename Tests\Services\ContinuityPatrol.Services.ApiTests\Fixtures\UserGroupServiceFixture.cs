﻿using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Create;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

public class UserGroupServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserGroupService Service { get; }

    public CreateUserGroupCommand CreateCommand { get; }
    public UpdateUserGroupCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public PaginatedResult<UserGroupListVm> PaginatedResult { get; }
    public List<UserGroupListVm> UserGroupList { get; }

    public UserGroupServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserGroupService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateUserGroupCommand>();
        UpdateCommand = fixture.Create<UpdateUserGroupCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        PaginatedResult = fixture.Create<PaginatedResult<UserGroupListVm>>();
        UserGroupList = fixture.CreateMany<UserGroupListVm>(3).ToList();
    }
}