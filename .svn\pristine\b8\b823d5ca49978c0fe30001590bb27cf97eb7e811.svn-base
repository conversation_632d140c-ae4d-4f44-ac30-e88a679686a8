﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SmsConfigurationModel;
using ContinuityPatrol.Shared.Core.Responses;


namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class SmsConfigurationServiceTests : IClassFixture<SmsConfigurationServiceFixture>
{
    private readonly SmsConfigurationServiceFixture _fixture;

    public SmsConfigurationServiceTests(SmsConfigurationServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetSmsConfigurations_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<SmsConfigurationListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SmsConfigList);

        var result = await _fixture.Service.GetSmsConfigurations();

        Assert.Equal(_fixture.SmsConfigList, result);
    }

    [Fact]
    public async Task GetSmsConfigurationById_Should_Return_Detail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<SmsConfigurationDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.SmsConfigDetail);

        var result = await _fixture.Service.GetSmsConfigurationById(_fixture.Id);

        Assert.Equal(_fixture.SmsConfigDetail, result);
    }
}
