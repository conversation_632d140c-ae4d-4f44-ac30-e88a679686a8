﻿using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class UserInfoServiceTests : IClassFixture<UserInfoServiceFixture>
{
    private readonly UserInfoServiceFixture _fixture;

    public UserInfoServiceTests(UserInfoServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_BaseResponse()
    {
        var userId = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(userId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_UserInfoDetailVm()
    {
        var userId = "test-id";

        _fixture.ClientMock
            .Setup(x => x.Get<UserInfoDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UserDetail);

        var result = await _fixture.Service.GetByReferenceId(userId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.UserDetail, result);
    }
}
