﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class DataSetServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public Fixture Fixture { get; }

    public List<DataSetListVm> DataSetList { get; }
    public DataSetDetailVm DetailVm { get; }
    public DataSetRunQueryVm RunQueryVm { get; }
    public GetDataSetByIdVm RunQueryByIdVm { get; }

    public BaseResponse BaseResponse { get; }
    public PaginatedResult<DataSetListVm> PaginatedResult { get; }

    public CreateDataSetCommand CreateCommand { get; }
    public UpdateDataSetCommand UpdateCommand { get; }
    public GetDataSetPaginatedListQuery PaginatedQuery { get; }

    public DataSetServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();

        DataSetList = Fixture.Create<List<DataSetListVm>>();
        DetailVm = Fixture.Create<DataSetDetailVm>();
        RunQueryVm = Fixture.Create<DataSetRunQueryVm>();
        RunQueryByIdVm = Fixture.Create<GetDataSetByIdVm>();

        BaseResponse = Fixture.Create<BaseResponse>();
        PaginatedResult = Fixture.Create<PaginatedResult<DataSetListVm>>();

        CreateCommand = Fixture.Create<CreateDataSetCommand>();
        UpdateCommand = Fixture.Create<UpdateDataSetCommand>();
        PaginatedQuery = Fixture.Create<GetDataSetPaginatedListQuery>();
    }
}