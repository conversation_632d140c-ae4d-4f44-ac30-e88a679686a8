﻿using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class RoboCopyJobServiceTests : IClassFixture<RoboCopyJobServiceFixture>
{
    private readonly RoboCopyJobServiceFixture _fixture;

    public RoboCopyJobServiceTests(RoboCopyJobServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateRoboCopyJob_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateRoboCopyJob(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteRoboCopyJob_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteRoboCopyJob("test-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetPaginatedRoboCopyJobs_ShouldReturn_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<RoboCopyJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedRoboCopyJobs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetRoboCopyJobById_ShouldReturn_Detail()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<RoboCopyJobDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.JobDetail);

        var result = await _fixture.Service.GetRoboCopyJobById("test-id");

        Assert.Equal(_fixture.JobDetail, result);
    }

    [Fact]
    public async Task GetRoboCopyJobs_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<RoboCopyJobListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.JobList);

        var result = await _fixture.Service.GetRoboCopyJobs();

        Assert.Equal(_fixture.JobList, result);
    }

    [Fact]
    public async Task UpdateRoboCopyJob_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateRoboCopyJob(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}
