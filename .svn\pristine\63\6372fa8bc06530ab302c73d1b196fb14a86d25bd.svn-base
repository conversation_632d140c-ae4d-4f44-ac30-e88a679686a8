using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using static Wmhelp.XPath2.Tokenizer;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestZertoVpgMonitorLogsSpecification : Specification<ZertoVpgMonitorLog>
{
    public TestZertoVpgMonitorLogsSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.InfraObjectName.Contains(searchTerm) || x.IsActive && x.WorkflowName.Contains(searchTerm);
        }
    }
}

public class ZertoVpgMonitorLogsRepositoryTests : IClassFixture<ZertoVpgMonitorLogFixture>
{
    private readonly ZertoVpgMonitorLogFixture _zertoVpgMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ZertoVpgMonitorLogsRepository _repository;
    private readonly ZertoVpgMonitorLogsRepository _repositoryNotParent;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public ZertoVpgMonitorLogsRepositoryTests(ZertoVpgMonitorLogFixture zertoVpgMonitorLogFixture)
    {
        _zertoVpgMonitorLogFixture = zertoVpgMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new ZertoVpgMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
        _repositoryNotParent = new ZertoVpgMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddZertoVpgMonitorLog_Successfully()
    {
        // Arrange
        var zerto = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogDto;

        // Act
        await _repository.AddAsync(zerto);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(zerto.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal(zerto.WorkflowName, result.WorkflowName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnZertoVpgMonitorLog_WhenExists()
    {
        // Arrange
        var zertoVpgMonitorLog = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogDto;
        await _repository.AddAsync(zertoVpgMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(zertoVpgMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(zertoVpgMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(zertoVpgMonitorLog.WorkflowName, result.WorkflowName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("eb01b17e-2ed8-4d79-8ea0-3a9ac38c899d");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateZertoVpgMonitorLog_Successfully()
    {
        // Arrange
        var zertoVpgMonitorLog = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogDto;
        await _repository.AddAsync(zertoVpgMonitorLog);

        // Act
        zertoVpgMonitorLog.DataLagValue = "00:01:25";
        await _repository.UpdateAsync(zertoVpgMonitorLog);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(zertoVpgMonitorLog.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("00:01:25", result.DataLagValue);

    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        // Arrange
        var zertoVpgMonitorLog = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogDto;
        await _repository.AddAsync(zertoVpgMonitorLog);

        // Act
        zertoVpgMonitorLog.IsActive = false;
        await _repository.UpdateAsync(zertoVpgMonitorLog);
        _dbContext.SaveChanges();


        // Assert
        var result = await _repository.GetByReferenceIdAsync(zertoVpgMonitorLog.ReferenceId);
        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLogs_WhenIsParent()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogList;
        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(zertoVpgMonitorLogs.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion



    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogPaginationList;
        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        var specification = new TestZertoVpgMonitorLogsSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "InfraObjectName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogPaginationList;
        zertoVpgMonitorLogs[0].InfraObjectName= "FilteredInfraObject";


        await _repository.AddRangeAsync(zertoVpgMonitorLogs);


        var specification = new TestZertoVpgMonitorLogsSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "InfraObjectName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogList;
        _repository.AddRangeAsync(zertoVpgMonitorLogs).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    #endregion



    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var zertoVpgMonitorLogs = new List<ZertoVpgMonitorLog>
        {
            new ZertoVpgMonitorLog
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ZertoVpgMonitorLog
            {
                InfraObjectId = infraObjectId,

                CreatedDate = baseDate.AddDays(2),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ZertoVpgMonitorLog
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(10), // Outside range
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ZertoVpgMonitorLog
            {
                InfraObjectId = "OTHER_INFRA",

                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _dbContext.ZertoVpgMonitorLogs.AddRangeAsync(zertoVpgMonitorLogs);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoLogsInRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var futureStartDate = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddYears(2).ToString("yyyy-MM-dd");

        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogList;
        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, futureStartDate, futureEndDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogsOfSpecificType()
    {
        // Arrange
        var logType = "ERROR";
        var zertoVpgMonitorLogs = new List<ZertoVpgMonitorLog>
        {
            new ZertoVpgMonitorLog
            {
                Type = logType,

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {
                Type = logType,

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {
                Type = "INFO",

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var result = await _repository.GetDetailByType(logType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(logType, x.Type));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeNotExists()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogList;
        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var result = await _repository.GetDetailByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var zertoVpgMonitorLogs = _zertoVpgMonitorLogFixture.ZertoVpgMonitorLogList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(zertoVpgMonitorLogs);
        var initialCount = zertoVpgMonitorLogs.Count;

        var toUpdate = zertoVpgMonitorLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.Threshold = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = zertoVpgMonitorLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Threshold == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldFilterByCompanyId_WhenNotParent()
    {
        // Arrange
        var zertoVpgMonitorLogs = new List<ZertoVpgMonitorLog>
        {
            new ZertoVpgMonitorLog
            {

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var paginatedResult = _repository.GetPaginatedQuery().ToList();
        var listResult = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, paginatedResult.Count);
        Assert.Equal(3, listResult.Count);

    }

    [Fact]
    public async Task Repository_ShouldNotFilterByCompanyId_WhenIsParent()
    {
        // Arrange
        var zertoVpgMonitorLogs = new List<ZertoVpgMonitorLog>
        {
            new ZertoVpgMonitorLog
            {

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {


                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new ZertoVpgMonitorLog
            {

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _repository.AddRangeAsync(zertoVpgMonitorLogs);

        // Act
        var paginatedResult = _repository.GetPaginatedQuery().ToList();
        var listResult = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, paginatedResult.Count);
        Assert.Equal(3, listResult.Count);
    }

    #endregion

    #region GetByInfraObjectId Tests - Additional Coverage

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnFilteredLogs_WhenLogsExistInDateRange()
    {
        // Arrange
        var infraObjectId = "ZERTO_INFRA_001";
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        var logs = new List<ZertoVpgMonitorLog>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "VPG",
                CreatedDate = DateTime.Today.AddDays(-3),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "VPG",
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "DIFFERENT_INFRA",
                Type = "VPG",
                CreatedDate = DateTime.Today.AddDays(-2),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var infraObjectId = "ZERTO_INFRA_002";
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        var logs = new List<ZertoVpgMonitorLog>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "VPG",
                CreatedDate = DateTime.Today.AddDays(-2),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                Type = "VPG",
                CreatedDate = DateTime.Today.AddDays(-1),
                IsActive = false // Should be filtered out by Active() extension
            }
        };

        await _dbContext.ZertoVpgMonitorLogs.AddRangeAsync(logs);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleEmptyInfraObjectId()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetByInfraObjectId("", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullInfraObjectId()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.Today.ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetByInfraObjectId(null, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests - Additional Coverage

    [Fact]
    public async Task GetDetailByType_ShouldReturnFilteredLogs_WhenTypeMatches()
    {
        // Arrange
        var targetType = "VPG";

        var logs = new List<ZertoVpgMonitorLog>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_001",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_002",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_003",
                Type = "Different Type",
                CreatedDate = DateTime.Today,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(targetType, x.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var logs = new List<ZertoVpgMonitorLog>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_001",
                Type = "VPG",
                CreatedDate = DateTime.Today,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType("Non Existent Type");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldFilterInactiveRecords()
    {
        // Arrange
        var targetType = "VPG";

        var logs = new List<ZertoVpgMonitorLog>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_001",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "ZERTO_INFRA_002",
                Type = targetType,
                CreatedDate = DateTime.Today,
                IsActive = false // Should be filtered out by Active() extension
            }
        };

        await _dbContext.ZertoVpgMonitorLogs.AddRangeAsync(logs);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleNullType()
    {
        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleEmptyType()
    {
        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion
}
