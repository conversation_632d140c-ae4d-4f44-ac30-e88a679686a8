﻿using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class ReplicationServiceTests : IClassFixture<ReplicationServiceFixture>
{
    private readonly ReplicationServiceFixture _fixture;

    public ReplicationServiceTests(ReplicationServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_Response()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<CreateReplicationResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.CreateResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.CreateResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_Response()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<UpdateReplicationResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.UpdateResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.UpdateResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("replication-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetReplicationList_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<ReplicationListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ReplicationList);

        var result = await _fixture.Service.GetReplicationList();

        Assert.Equal(_fixture.ReplicationList, result);
    }

    [Fact]
    public async Task IsReplicationNameExist_ShouldReturn_True()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsReplicationNameExist("test-name", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<ReplicationNameVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.NameList);

        var result = await _fixture.Service.GetReplicationNames();

        Assert.Equal(_fixture.NameList, result);
    }

    [Fact]
    public async Task GetReplicationByLicenseKey_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<GetReplicationByLicenseKeyListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ByLicenseList);

        var result = await _fixture.Service.GetReplicationByLicenseKey("license-key");

        Assert.Equal(_fixture.ByLicenseList, result);
    }

    [Fact]
    public async Task GetReplicationById_ShouldReturn_DetailVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<ReplicationDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetReplicationById("replication-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetReplicationByType_ShouldReturn_List()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<List<ReplicationTypeVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.TypeList);

        var result = await _fixture.Service.GetReplicationByType("type-id");

        Assert.Equal(_fixture.TypeList, result);
    }

    [Fact]
    public async Task GetPaginatedReplications_ShouldReturn_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<ReplicationListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedReplications(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task SaveAsReplication_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.SaveAsReplication(_fixture.SaveAsCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task SaveAllReplication_ShouldReturn_BaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.SaveAllReplication(_fixture.SaveAllCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }
}
