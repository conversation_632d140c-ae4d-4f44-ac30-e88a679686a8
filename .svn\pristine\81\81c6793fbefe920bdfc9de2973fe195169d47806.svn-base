﻿using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class AccountServiceTests : IClassFixture<AccountServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly AccountService _service;
    private readonly AccountServiceFixture _fixture;

    public AccountServiceTests(AccountServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new AccountService(_clientMock.Object);
    }

    [Fact]
    public async Task Authenticate_ShouldReturnAuthenticationResponse()
    {
        // Arrange
        _clientMock.Setup(x => x.Post<AuthenticationResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.AuthResponse);

        // Act
        var result = await _service.Authenticate(_fixture.AuthRequest);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fixture.AuthResponse, result);
    }
}