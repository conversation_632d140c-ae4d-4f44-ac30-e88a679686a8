﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class EmployeeServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public EmployeeService Service { get; }

    public CreateEmployeeCommand CreateCommand { get; }
    public UpdateEmployeeCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<EmployeeListVm> ListVm { get; }
    public EmployeeDetailVm DetailVm { get; }
    public GetEmployeePaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<EmployeeListVm> PaginatedResult { get; }

    public EmployeeServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new EmployeeService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateEmployeeCommand>();
        UpdateCommand = Fixture.Create<UpdateEmployeeCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        ListVm = Fixture.CreateMany<EmployeeListVm>(3).ToList();
        DetailVm = Fixture.Create<EmployeeDetailVm>();
        PaginatedQuery = Fixture.Create<GetEmployeePaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<EmployeeListVm>>();
    }
}