﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration

{
    public class DataSetServiceTests : IClassFixture<DataSetServiceFixture>
    {
        private readonly DataSetServiceFixture _fixture;
        private readonly DataSetService _service;

        public DataSetServiceTests(DataSetServiceFixture fixture)
        {
            _fixture = fixture;
            _service = new DataSetService(_fixture.ClientMock.Object);
        }

        [Fact]
        public async Task GetDataSetList_ShouldReturnList()
        {
            _fixture.ClientMock.Setup(x => x.Get<List<DataSetListVm>>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.DataSetList);

            var result = await _service.GetDataSetList();

            Assert.Equal(_fixture.DataSetList, result);
        }

        [Fact]
        public async Task CreateAsync_ShouldReturnBaseResponse()
        {
            _fixture.ClientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.BaseResponse);

            var result = await _service.CreateAsync(_fixture.CreateCommand);

            Assert.Equal(_fixture.BaseResponse, result);
        }

        [Fact]
        public async Task UpdateAsync_ShouldReturnBaseResponse()
        {
            _fixture.ClientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.BaseResponse);

            var result = await _service.UpdateAsync(_fixture.UpdateCommand);

            Assert.Equal(_fixture.BaseResponse, result);
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnBaseResponse()
        {
            _fixture.ClientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.BaseResponse);

            var result = await _service.DeleteAsync("sample-id");

            Assert.Equal(_fixture.BaseResponse, result);
        }

        [Fact]
        public async Task GetDataSetPaginatedList_ShouldReturnPaginatedResult()
        {
            _fixture.ClientMock.Setup(x => x.Get<PaginatedResult<DataSetListVm>>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.PaginatedResult);

            var result = await _service.GetDataSetPaginatedList(_fixture.PaginatedQuery);

            Assert.Equal(_fixture.PaginatedResult, result);
        }

        [Fact]
        public async Task GetDataSetById_ShouldReturnDetailVm()
        {
            _fixture.ClientMock.Setup(x => x.Get<DataSetDetailVm>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.DetailVm);

            var result = await _service.GetDataSetById("sample-id");

            Assert.Equal(_fixture.DetailVm, result);
        }

        [Fact]
        public async Task IsDataSetNameExist_ShouldReturnTrue()
        {
            _fixture.ClientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
                .ReturnsAsync(true);

            var result = await _service.IsDataSetNameExist("TestName", null);

            Assert.True(result);
        }

        [Fact]
        public async Task RunQuery_ShouldReturnRunQueryVm()
        {
            _fixture.ClientMock.Setup(x => x.Get<DataSetRunQueryVm>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.RunQueryVm);

            var result = await _service.RunQuery("SELECT * FROM Dummy");

            Assert.Equal(_fixture.RunQueryVm, result);
        }

        [Fact]
        public async Task GetRunQueryById_ShouldReturnQueryByIdVm()
        {
            _fixture.ClientMock.Setup(x => x.Get<GetDataSetByIdVm>(It.IsAny<RestRequest>()))
                .ReturnsAsync(_fixture.RunQueryByIdVm);

            var result = await _service.GetRunQueryById("sample-id");

            Assert.Equal(_fixture.RunQueryByIdVm, result);
        }
    }
}
