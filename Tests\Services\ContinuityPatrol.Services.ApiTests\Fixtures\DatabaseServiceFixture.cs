﻿using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;

public class DatabaseServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public DatabaseService Service { get; }

    public DatabaseServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new DatabaseService(ClientMock.Object);
    }
}