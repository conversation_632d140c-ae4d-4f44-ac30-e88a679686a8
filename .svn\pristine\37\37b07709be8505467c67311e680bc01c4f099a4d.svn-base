﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.UserRole.Commands.Create;
using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

public class UserRoleServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public UserRoleService Service { get; }

    public CreateUserRoleCommand CreateCommand { get; }
    public UpdateUserRoleCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public string UserRoleId { get; }
    public UserRoleDetailVm DetailVm { get; }
    public GetUserRolePaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<UserRoleListVm> PaginatedResult { get; }
    public List<UserRoleListVm> RoleList { get; }
    public List<UserRoleNamesVm> RoleNames { get; }

    public UserRoleServiceFixture()
    {
        var fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new UserRoleService(ClientMock.Object);

        CreateCommand = fixture.Create<CreateUserRoleCommand>();
        UpdateCommand = fixture.Create<UpdateUserRoleCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        UserRoleId = fixture.Create<string>();
        DetailVm = fixture.Create<UserRoleDetailVm>();
        PaginatedQuery = fixture.Create<GetUserRolePaginatedListQuery>();
        PaginatedResult = fixture.Create<PaginatedResult<UserRoleListVm>>();
        RoleList = fixture.Create<List<UserRoleListVm>>();
        RoleNames = fixture.Create<List<UserRoleNamesVm>>();
    }
}