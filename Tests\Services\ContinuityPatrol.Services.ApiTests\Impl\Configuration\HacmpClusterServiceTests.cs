﻿using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class HacmpClusterServiceTests : IClassFixture<HacmpClusterServiceFixture>
{
    private readonly HacmpClusterServiceFixture _fixture;

    public HacmpClusterServiceTests(HacmpClusterServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("test-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetHacmpClusterList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(c => c.GetFromCache<List<HacmpClusterListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetHacmpClusterList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<HacmpClusterDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("ref-123");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsHacmpClusterNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsHacmpClusterNameExist("test-name", null);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedHacmpClusters_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(c => c.Get<PaginatedResult<HacmpClusterListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedHacmpClusters(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
