﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using System.Threading.Tasks;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BusinessServiceServiceTests : IClassFixture<BusinessServiceServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BusinessServiceService _service;
    private readonly BusinessServiceServiceFixture _fixture;

    public BusinessServiceServiceTests(BusinessServiceServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BusinessServiceService(_clientMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(c => c.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("some-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetBusinessServiceNames_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<BusinessServiceNameVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.NameVm);

        var result = await _service.GetBusinessServiceNames();

        Assert.Equal(_fixture.NameVm, result);
    }

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnTrue()
    {
        _clientMock.Setup(c => c.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsBusinessServiceNameExist("TestName", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        _clientMock.Setup(c => c.Get<BusinessServiceDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetBusinessServicePaginatedList_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(c => c.Get<PaginatedResult<BusinessServiceListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetBusinessServicePaginatedList(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task GetBusinessServiceDiagramByBusinessServiceId_ShouldReturnDiagramVm()
    {
        _clientMock.Setup(c => c.Get<GetBusinessServiceDiagramDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DiagramVm);

        var result = await _service.GetBusinessServiceDiagramByBusinessServiceId("svc-id");

        Assert.Equal(_fixture.DiagramVm, result);
    }

    [Fact]
    public async Task GetBusinessServiceList_ShouldReturnList()
    {
        _clientMock.Setup(c => c.Get<List<BusinessServiceListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBusinessServiceList();

        Assert.Equal(_fixture.ListVm, result);
    }
}
