﻿using AutoFixture;
using Moq;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Services.Api.Impl.Configuration;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class GlobalVariableServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public GlobalVariableService Service { get; }

    public CreateGlobalVariableCommand CreateCommand { get; }
    public UpdateGlobalVariableCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<GlobalVariableListVm> ListVm { get; }
    public GlobalVariableDetailVm DetailVm { get; }
    public GetGlobalVariablePaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<GlobalVariableListVm> PaginatedResult { get; }
    public List<GlobalVariableDetailVm> DetailVmList { get; }

    public GlobalVariableServiceFixture()
    {
        Fixture = new Fixture();
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        ClientMock = new Mock<IBaseClient>();
        Service = new GlobalVariableService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateGlobalVariableCommand>();
        UpdateCommand = Fixture.Create<UpdateGlobalVariableCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        ListVm = Fixture.CreateMany<GlobalVariableListVm>(3).ToList();
        DetailVm = Fixture.Create<GlobalVariableDetailVm>();
        PaginatedQuery = Fixture.Create<GetGlobalVariablePaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<GlobalVariableListVm>>();
        DetailVmList = Fixture.CreateMany<GlobalVariableDetailVm>(2).ToList();
    }
}
