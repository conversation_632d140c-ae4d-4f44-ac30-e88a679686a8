﻿using AutoFixture;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class AdPasswordJobServiceFixture
{
    public CreateAdPasswordJobCommand CreateCommand { get; }
    public UpdateAdPasswordJobCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public List<AdPasswordJobListVm> ListVm { get; }
    public AdPasswordJobDetailVm DetailVm { get; }
    public GetAdPasswordJobPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<AdPasswordJobListVm> PaginatedResult { get; }

    public AdPasswordJobServiceFixture()
    {
        var fixture = new Fixture();
        CreateCommand = fixture.Create<CreateAdPasswordJobCommand>();
        UpdateCommand = fixture.Create<UpdateAdPasswordJobCommand>();
        BaseResponse = fixture.Create<BaseResponse>();
        ListVm = fixture.CreateMany<AdPasswordJobListVm>(3).ToList();
        DetailVm = fixture.Create<AdPasswordJobDetailVm>();
        PaginatedQuery = fixture.Create<GetAdPasswordJobPaginatedListQuery>();
        PaginatedResult = fixture.Create<PaginatedResult<AdPasswordJobListVm>>();
    }
}