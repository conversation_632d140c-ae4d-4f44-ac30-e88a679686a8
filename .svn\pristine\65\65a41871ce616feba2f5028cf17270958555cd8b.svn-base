﻿using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class DataSyncOptionsServiceTests : IClassFixture<DataSyncOptionsServiceFixture>
{
    private readonly DataSyncOptionsServiceFixture _fixture;

    public DataSyncOptionsServiceTests(DataSyncOptionsServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("sample-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<DataSyncOptionsDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("reference-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsDataSyncNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsDataSyncNameExist("TestName", "optional-id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedDataSyncs_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<DataSyncOptionsListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedVm);

        var result = await _fixture.Service.GetPaginatedDataSyncs(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedVm, result);
    }

    [Fact]
    public async Task GetDataSyncList_ShouldReturnList()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<DataSyncOptionsListVm>>(It.IsAny<RestRequest>(), It.IsAny<string>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetDataSyncList();

        Assert.Equal(_fixture.ListVm, result);
    }
}
