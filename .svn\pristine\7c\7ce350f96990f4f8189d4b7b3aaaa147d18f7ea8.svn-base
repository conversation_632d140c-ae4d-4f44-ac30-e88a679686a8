﻿using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class EmployeeServiceTests : IClassFixture<EmployeeServiceFixture>
{
    private readonly EmployeeServiceFixture _fixture;

    public EmployeeServiceTests(EmployeeServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync("employee-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnEmployeeDetailVm()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<EmployeeDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetByReferenceId("emp-ref");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetEmployeeList_ShouldReturnListVm()
    {
        _fixture.ClientMock
            .Setup(x => x.GetFromCache<List<EmployeeListVm>>(It.IsAny<RestRequest>(), "GetEmployeeList"))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetEmployeeList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetPaginatedEmployees_ShouldReturnPaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<EmployeeListVm>>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedEmployees(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }

    [Fact]
    public async Task IsEmployeeNameExist_ShouldReturnTrue()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsEmployeeNameExist("John Doe", null);

        Assert.True(result);
    }
}
