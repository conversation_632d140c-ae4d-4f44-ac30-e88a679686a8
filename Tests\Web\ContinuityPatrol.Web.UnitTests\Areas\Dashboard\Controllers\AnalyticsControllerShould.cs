﻿using AutoMapper;
using ContinuityPatrol.Application.Features.DashboardView.Event.OperationalAnalyticsView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Extension;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers;

[Collection("AnalyticsTests")]
public class AnalyticsControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<ILogger<AnalyticsController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly AnalyticsController _controller;

    public AnalyticsControllerShould()
    {
        _controller = new AnalyticsController(
            _mockDataProvider.Object,
            _mockPublisher.Object,
            _mockLogger.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task Index_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.Index();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<OperationalAnalyticsEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithValidData_ReturnsJsonResult()
    {
        var drillAnalyticsData = new DrillAnalyticsDetailVm
        {
            DrillCount = 5,
            SuccessfulDrills = 4,
            FailedDrills = 1
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(drillAnalyticsData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = drillAnalyticsData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithValidData_ReturnsJsonResult()
    {
        var componentFailureData = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponents = 10,
            FailedComponents = 2,
            HealthyComponents = 8
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(componentFailureData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = componentFailureData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithValidData_ReturnsJsonResult()
    {
        var availabilityData = new GetOperationalAvailabilityAnalyticsDetailVm
        {
            AvailabilityPercentage = 99.5,
            UptimeHours = 720,
            DowntimeHours = 3.6
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(availabilityData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = availabilityData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithValidData_ReturnsJsonResult()
    {
        var workflowData = new GetWorkflowAnalyticsDetailVm
        {
            TotalWorkflows = 15,
            ActiveWorkflows = 12,
            InactiveWorkflows = 3
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(workflowData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = workflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithValidData_ReturnsJsonResult()
    {
        var slaData = new GetSlaBreachListVm
        {
            TotalSLAs = 20,
            BreachedSLAs = 3,
            ComplianceSLAs = 17
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(slaData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = slaData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ThrowsAsync(exception);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithValidData_ReturnsJsonResult()
    {
        var healthSummaryData = new { OverallHealth = "Good", CriticalIssues = 1, WarningIssues = 3, HealthyServices = 25 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(healthSummaryData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = healthSummaryData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync((DrillAnalyticsDetailVm)null);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (DrillAnalyticsDetailVm)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync((ComponentFailureAnalyticsDetailVm)null);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (ComponentFailureAnalyticsDetailVm)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync((GetOperationalAvailabilityAnalyticsDetailVm)null);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (GetOperationalAvailabilityAnalyticsDetailVm)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync((GetWorkflowAnalyticsDetailVm)null);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (GetWorkflowAnalyticsDetailVm)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync((GetSlaBreachListVm)null);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (GetSlaBreachListVm)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync((List<OperationalHealthSummaryDetailVm>)null);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (List<OperationalHealthSummaryDetailVm>)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithDatabaseException_ReturnsJsonException()
    {
        var exception = new InvalidOperationException("Database connection failed");

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithTimeoutException_ReturnsJsonException()
    {
        var exception = new TimeoutException("Request timeout");

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithArgumentException_ReturnsJsonException()
    {
        var exception = new ArgumentException("Invalid parameter");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithUnauthorizedAccessException_ReturnsJsonException()
    {
        var exception = new UnauthorizedAccessException("Access denied");

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNotSupportedException_ReturnsJsonException()
    {
        var exception = new NotSupportedException("Operation not supported");

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ThrowsAsync(exception);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithNullReferenceException_ReturnsJsonException()
    {
        var exception = new NullReferenceException("Object reference not set");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithComplexData_ReturnsJsonResult()
    {
        var complexDrillData = new DrillAnalyticsDetailVm
        {
            ConfiguredProfileCount = 25,
            ExecutedProfileCount = 20,
            ExecutedWorkflowCount = 18,
            DrillAnalyticsDetailLists = new List<DrillAnalyticsDetailList>
            {
                new DrillAnalyticsDetailList
                {
                    ProfileTotalCount = 10,
                    ProfileSuccessCount = 8,
                    ProfiledFailedCount = 2,
                    WorkflowTotalCount = 15,
                    WorkflowSuccessCount = 12,
                    WorkflowFailedCount = 3,
                    OutOfRtoCount = 1,
                    DrillDate = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd")
                }
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(complexDrillData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = complexDrillData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithDetailedMetrics_ReturnsJsonResult()
    {
        var detailedFailureData = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponent = 150,
            AvailableCount = 142,
            FailedCount = 8,
            ComponentsAffectedToday = 2,
            TotalDatabaseCount = 75,
            DatabaseUpCount = 70,
            DatabaseDownCount = 5,
            TotalReplicationCount = 50,
            ReplicationUpCount = 48,
            ReplicationDownCount = 2,
            ServerDtl = new List<Dictionary<string, dynamic>>
            {
                new Dictionary<string, dynamic> { { "ServerName", "Web-Server-01" }, { "Status", "Up" }, { "RoleType", "Web Server" } },
                new Dictionary<string, dynamic> { { "ServerName", "App-Server-01" }, { "Status", "Down" }, { "RoleType", "Application Server" } }
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(detailedFailureData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = detailedFailureData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithExtendedMetrics_ReturnsJsonResult()
    {
        var extendedAvailabilityData = new GetOperationalAvailabilityAnalyticsDetailVm
        {
            TotalBusinessServiceCount = 25,
            BusinessServiceSuccessCount = 23,
            BusinessServiceErrorCount = 2,
            TotalInfraObjectCount = 150,
            InfraObjectSuccessCount = 145,
            InfraObjectErrorCount = 5,
            SiteRunningListVm = new List<SiteRunningListVm>
            {
                new SiteRunningListVm { SiteType = "Primary", TotalRunningCount = 75 },
                new SiteRunningListVm { SiteType = "Secondary", TotalRunningCount = 70 },
                new SiteRunningListVm { SiteType = "DR", TotalRunningCount = 5 }
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(extendedAvailabilityData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = extendedAvailabilityData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithComprehensiveData_ReturnsJsonResult()
    {
        var comprehensiveWorkflowData = new GetWorkflowAnalyticsDetailVm
        {
            TotalConfigured = 45,
            ExecutedWorkFlowCount = 38,
            WorkFlowSuccessCount = 35,
            WorkFlowErrorCount = 3
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(comprehensiveWorkflowData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = comprehensiveWorkflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithDetailedBreachData_ReturnsJsonResult()
    {
        var detailedSlaData = new GetSlaBreachListVm
        {
            SlaBreachCount = 4,
            SlaNonBreachCount = 31,
            SlaMeetingRtoCount = 30,
            ActiveAlertCount = 3,
            SlaImpactList = new List<GetSlaImpactListVm>
            {
                new GetSlaImpactListVm { Date = DateTime.Now.AddDays(-1), ImpactCount = 2, NonImpactCount = 15 },
                new GetSlaImpactListVm { Date = DateTime.Now.AddDays(-2), ImpactCount = 1, NonImpactCount = 16 },
                new GetSlaImpactListVm { Date = DateTime.Now.AddDays(-3), ImpactCount = 0, NonImpactCount = 17 }
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(detailedSlaData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = detailedSlaData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithComprehensiveHealthData_ReturnsJsonResult()
    {
        var comprehensiveHealthData = new List<OperationalHealthSummaryDetailVm>
        {
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Core Banking Platform",
                HealthyCount = 85,
                UnHealthyCount = 10,
                MaintenanceCount = 5
            },
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Digital Payment Gateway",
                HealthyCount = 92,
                UnHealthyCount = 6,
                MaintenanceCount = 2
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(comprehensiveHealthData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = comprehensiveHealthData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task Index_VerifiesEventPublishing_AndLogging()
    {
        var result = await _controller.Index();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<OperationalAnalyticsEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithEmptyData_ReturnsJsonResult()
    {
        var emptyData = new DrillAnalyticsDetailVm
        {
            ConfiguredProfileCount = 0,
            ExecutedProfileCount = 0,
            ExecutedWorkflowCount = 0,
            DrillAnalyticsDetailLists = new List<DrillAnalyticsDetailList>()
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(emptyData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = emptyData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithZeroValues_ReturnsJsonResult()
    {
        var zeroData = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponent = 0,
            AvailableCount = 0,
            FailedCount = 0,
            ComponentsAffectedToday = 0,
            TotalDatabaseCount = 0,
            DatabaseUpCount = 0,
            DatabaseDownCount = 0,
            TotalReplicationCount = 0,
            ReplicationUpCount = 0,
            ReplicationDownCount = 0,
            ServerDtl = new List<Dictionary<string, dynamic>>()
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(zeroData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = zeroData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithPerfectUptime_ReturnsJsonResult()
    {
        var perfectUptimeData = new GetOperationalAvailabilityAnalyticsDetailVm
        {
            TotalBusinessServiceCount = 50,
            BusinessServiceSuccessCount = 50,
            BusinessServiceErrorCount = 0,
            TotalInfraObjectCount = 200,
            InfraObjectSuccessCount = 200,
            InfraObjectErrorCount = 0,
            SiteRunningListVm = new List<SiteRunningListVm>
            {
                new SiteRunningListVm { SiteType = "Primary", TotalRunningCount = 100 },
                new SiteRunningListVm { SiteType = "Secondary", TotalRunningCount = 100 }
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(perfectUptimeData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = perfectUptimeData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithAllInactiveWorkflows_ReturnsJsonResult()
    {
        var allInactiveData = new GetWorkflowAnalyticsDetailVm
        {
            TotalConfigured = 10,
            ExecutedWorkFlowCount = 0,
            WorkFlowSuccessCount = 0,
            WorkFlowErrorCount = 0
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(allInactiveData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = allInactiveData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNoBreaches_ReturnsJsonResult()
    {
        var noBreachData = new GetSlaBreachListVm
        {
            SlaBreachCount = 0,
            SlaNonBreachCount = 50,
            SlaMeetingRtoCount = 50,
            ActiveAlertCount = 0,
            SlaImpactList = new List<GetSlaImpactListVm>()
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(noBreachData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = noBreachData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithCriticalIssues_ReturnsJsonResult()
    {
        var criticalHealthData = new List<OperationalHealthSummaryDetailVm>
        {
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Critical Enterprise Service",
                HealthyCount = 15,
                UnHealthyCount = 10,
                MaintenanceCount = 5
            }
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(criticalHealthData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = criticalHealthData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }
}