using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestDatabaseViewSpecification : Specification<DatabaseView>
{
    public TestDatabaseViewSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.CompanyId == "ChHILD_COMPANY_123";
        }
        else
        {
            Criteria = x => x.CompanyId == "ChHILD_COMPANY_123" && x.Name.Contains(searchTerm);
        }
    }
}

public class DatabaseViewRepositoryTests : IClassFixture<DatabaseViewFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly DatabaseViewFixture _databaseViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatabaseViewRepository _repository;
    private readonly DatabaseViewRepository _repositoryNotParent;
    private readonly InfraObjectRepository _infraObjectRepository;


    public DatabaseViewRepositoryTests(DatabaseViewFixture databaseViewFixture)
    {

        _databaseViewFixture = databaseViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _infraObjectRepository = new InfraObjectRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _repository = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockUserService(), _infraObjectRepository);

        _repositoryNotParent = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _infraObjectRepository);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var databaseView = _databaseViewFixture.DatabaseViewDto;

        // Act
        var result = await _repository.AddAsync(databaseView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databaseView.Name, result.Name);
        Assert.Equal(databaseView.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DatabaseViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;

        databaseViews.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123"); 
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databaseViews.Count, result.Count);
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

   
    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        databaseViews.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123"); 
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldThrowException_WhenLoggedInUserServiceIsNull()
    {
        // Arrange
        var faultyRepo = new DatabaseViewRepository(_dbContext, null, _infraObjectRepository);

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => faultyRepo.ListAllAsync());
    }
    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenDatabaseViewIsEmpty()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }


    #endregion

    #region DatabaseCountAsync Tests

    [Fact]
    public async Task DatabaseCountAsync_ShouldReturnCorrectCount_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.DatabaseCountAsync();

        // Assert
        Assert.Equal(databaseViews.Count, result);
    }

    [Fact]
    public async Task DatabaseCountAsync_ShouldReturnFilteredCount_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.DatabaseCountAsync();

        // Assert
        Assert.True(result >= 0); // Should return filtered count
    }

    #endregion

    #region GetAllByDatabaseIdsAsync Tests

    [Fact]
    public async Task GetAllByDatabaseIdsAsync_ShouldReturnEntitiesWithMatchingIds_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetAllByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
    }

    [Fact]
    public async Task GetAllByDatabaseIdsAsync_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        databaseViews.ForEach(x=>x.CompanyId = "ChHILD_COMPANY_123"); 
        await _repositoryNotParent.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repositoryNotParent.GetAllByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetByUserName Tests

    [Fact]
    public async Task GetByUserName_ShouldReturnEntitiesWithMatchingUserName_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByUserName(DatabaseViewFixture.UserName);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.UserName, x.UserName));
    }

    [Fact]
    public async Task GetByUserName_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.GetByUserName(DatabaseViewFixture.UserName);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseByServerId Tests

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEntitiesWithMatchingServerId_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerId(DatabaseViewFixture.ServerId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.ServerId, x.ServerId));
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.GetDatabaseByServerId(DatabaseViewFixture.ServerId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldHandleCommaSeparatedServerIds()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var serverId1 = "SERVER_001";
        var serverId2 = "SERVER_002";
        databaseViews.First().ServerId = serverId1;
        databaseViews.Skip(1).First().ServerId = serverId2;

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerId($"{serverId1},{serverId2}");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ServerId == serverId1);
        Assert.Contains(result, x => x.ServerId == serverId2);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());

        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseByDatabaseTypeIds Tests

    [Fact]
    public async Task GetDatabaseByDatabaseTypeIds_ShouldReturnEntitiesWithMatchingDatabaseTypeIds()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeIds = new List<string> { DatabaseViewFixture.DatabaseTypeId };
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeIds(databaseTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.DatabaseTypeId, x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeIds_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeIds = new List<string> { "NON_EXISTENT_TYPE" };
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeIds(databaseTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(databaseViews);
        var initialCount = databaseViews.Count;

        var toUpdate = databaseViews.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDatabaseViewName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = databaseViews.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedDatabaseViewName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region GetDatabaseList Tests

    [Fact]
    public async Task GetDatabaseList_ShouldReturnDatabaseList_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseList();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.NotNull(x.DatabaseType));
        Assert.All(result, x => Assert.NotNull(x.DatabaseTypeId));
        Assert.All(result, x => Assert.NotNull(x.BusinessServiceId));
    }

    [Fact]
    public async Task GetDatabaseList_ShouldReturnFilteredList_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.GetDatabaseList();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseByDatabaseTypeId Tests

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnMatchingDatabases_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var targetTypeId = "TYPE_001";
        databaseViews[0].DatabaseTypeId = targetTypeId;
        databaseViews[1].DatabaseTypeId = targetTypeId;

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId(targetTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(targetTypeId, x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnFilteredDatabases_WhenIsNotParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var targetTypeId = "TYPE_001";
        databaseViews[0].DatabaseTypeId = targetTypeId;
        databaseViews[0].CompanyId = "ChHILD_COMPANY_123";
        databaseViews[1].DatabaseTypeId = targetTypeId;
        databaseViews[1].CompanyId = "OTHER_COMPANY_456";

        await _repositoryNotParent.AddRangeAsync(databaseViews);

        var _mockinfraRepository = new Mock<IInfraObjectRepository>();

        var infraobject = GetInfraObjectMock(databaseViews[0].ReferenceId, databaseViews[1].ReferenceId);

        _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

        var repositoryNotParent = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);

        // Act
        var result = await repositoryNotParent.GetDatabaseByDatabaseTypeId(targetTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    #endregion

    #region GetByDatabaseIdsAsync Tests

    [Fact]
    public async Task GetByDatabaseIdsAsync_ShouldReturnProjectedEntities_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ModeType));
        Assert.All(result, x => Assert.NotNull(x.ServerId));
        Assert.All(result, x => Assert.NotNull(x.ServerName));
        Assert.All(result, x => Assert.NotNull(x.DatabaseType));
    }

    [Fact]
    public async Task GetByDatabaseIdsAsync_ShouldHandleSIDAndInstanceName()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        databaseViews[0].SID = null;
        databaseViews[0].InstanceName = "TestInstance";
        databaseViews[1].SID = "TestSID";
        databaseViews[1].InstanceName = "AnotherInstance";

        await _repository.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal("TestInstance", result.First(x => x.ReferenceId == databaseViews[0].ReferenceId).SID);
        Assert.Equal("TestSID", result.First(x => x.ReferenceId == databaseViews[1].ReferenceId).SID);
    }

    #endregion

    #region GetByUserNameAndDatabaseType Tests

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var userName = "testuser";
        var databaseTypeIds = "TYPE_001,TYPE_002";

        databaseViews[0].UserName = userName;
        databaseViews[0].DatabaseTypeId = "TYPE_001";
        databaseViews[1].UserName = userName;
        databaseViews[1].DatabaseTypeId = "TYPE_002";
        databaseViews[2].UserName = userName;
        databaseViews[2].DatabaseTypeId = "TYPE_003"; // Should not be included

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByUserNameAndDatabaseType(userName, databaseTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(userName, x.UserName));
        Assert.All(result, x => Assert.True(x.DatabaseTypeId == "TYPE_001" || x.DatabaseTypeId == "TYPE_002"));
    }

    #endregion

    #region GetDatabaseNames Tests

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnNamesOnly()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseNames();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
        Assert.All(result, x => Assert.NotNull(x.Name));
        // Should only have ReferenceId and Name populated
    }

    #endregion

    #region GetDatabaseByServerIds Tests

    [Fact]
    public async Task GetDatabaseByServerIds_ShouldHandleCommaSeparatedIds()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var serverIds = new List<string> { "SERVER_001,SERVER_002", "SERVER_003" };

        databaseViews[0].ServerId = "SERVER_001";
        databaseViews[1].ServerId = "SERVER_002";
        databaseViews[2].ServerId = "SERVER_003";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerIds(serverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Contains(result, x => x.ServerId == "SERVER_001");
        Assert.Contains(result, x => x.ServerId == "SERVER_002");
        Assert.Contains(result, x => x.ServerId == "SERVER_003");
    }

    [Fact]
    public async Task GetDatabaseByServerIds_ShouldHandleEmptyAndWhitespace()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var serverIds = new List<string> { "SERVER_001, , SERVER_002,   " };

        databaseViews[0].ServerId = "SERVER_001";
        databaseViews[1].ServerId = "SERVER_002";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerIds(serverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ServerId == "SERVER_001");
        Assert.Contains(result, x => x.ServerId == "SERVER_002");
    }

    #endregion

    #region GetDatabaseListByLicenseKey Tests

    [Fact]
    public async Task GetDatabaseListByLicenseKey_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var licenseId = "LICENSE_001";

        databaseViews[0].LicenseId = licenseId;
        databaseViews[1].LicenseId = licenseId;
        databaseViews[2].LicenseId = "LICENSE_002";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseListByLicenseKey(licenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(licenseId, x.LicenseId));
    }

    [Fact]
    public async Task GetDatabaseListByLicenseKey_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseListByLicenseKey("NON_EXISTENT_LICENSE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByDatabaseTypeIdAndFormVersion Tests

    [Fact]
    public async Task GetByDatabaseTypeIdAndFormVersion_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeId = "TYPE_001";
        var formVersion = "V1.0";

        databaseViews[0].DatabaseTypeId = databaseTypeId;
        databaseViews[0].FormVersion = formVersion;
        databaseViews[1].DatabaseTypeId = databaseTypeId;
        databaseViews[1].FormVersion = "V2.0"; // Different version
        databaseViews[2].DatabaseTypeId = "TYPE_002"; // Different type
        databaseViews[2].FormVersion = formVersion;

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByDatabaseTypeIdAndFormVersion(databaseTypeId, formVersion);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(databaseTypeId, result.First().DatabaseTypeId);
        Assert.Equal(formVersion, result.First().FormVersion);
    }

    #endregion

    #region GetDatabaseType Tests

    [Fact]
    public async Task GetDatabaseType_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var type = "Production";

        databaseViews[0].Type = type;
        databaseViews[1].Type = type;
        databaseViews[2].Type = "Development";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(type, x.Type));
    }

    #endregion

    #region GetDatabaseByBusinessServiceId Tests

    [Fact]
    public async Task GetDatabaseByBusinessServiceId_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var businessServiceId = "SERVICE_001";

        databaseViews[0].BusinessServiceId = businessServiceId;
        databaseViews[1].BusinessServiceId = businessServiceId;
        databaseViews[2].BusinessServiceId = "SERVICE_002";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessServiceId, x.BusinessServiceId));
    }

    #endregion

    #region GetDatabaseByNodeId Tests

    [Fact]
    public async Task GetDatabaseByNodeId_ShouldReturnMatchingDatabases()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var nodeId = "NODE_001";

        databaseViews[0].Properties = nodeId;
        databaseViews[1].Properties = nodeId;
        databaseViews[2].Properties = "NODE_002";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByNodeId(nodeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(nodeId, x.Properties));
    }

    #endregion

    #region GetDatabaseByType Tests

    [Fact]
    public async Task GetDatabaseByType_ShouldReturnPaginatedResults_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewPaginationList;
        var databaseTypeId = "TYPE_001";
        databaseViews.ForEach(x => x.DatabaseTypeId = databaseTypeId);

        await _repository.AddRangeAsync(databaseViews);

        var specification = new TestDatabaseViewSpecification();

        // Act
        var result = await _repository.GetDatabaseByType(1, 10, specification, databaseTypeId, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
        Assert.All(result.Data, x => Assert.Equal(databaseTypeId, x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetDatabaseByType_ShouldReturnFilteredResults_WhenIsNotParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewPaginationList;
        var databaseTypeId = "TYPE_001";
        databaseViews.ForEach(x =>
        {
            x.DatabaseTypeId = databaseTypeId;
            x.CompanyId = "ChHILD_COMPANY_123";
        });

        await _repositoryNotParent.AddRangeAsync(databaseViews);

        var specification = new TestDatabaseViewSpecification();

        // Act
        var result = await _repositoryNotParent.GetDatabaseByType(1, 10, specification, databaseTypeId, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetDatabaseByType_ShouldReturnAllInfraDatabases_WhenIsAllInfra()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewPaginationList;
        var databaseTypeId = "TYPE_ALL_INFRA";
        databaseViews.ForEach(x =>
        {
            x.DatabaseTypeId = databaseTypeId;
        });

        await _repository.AddRangeAsync(databaseViews);

        var specification = new TestDatabaseViewSpecification();

        // Act
        var result = await _repository.GetDatabaseByType(1, 10, specification, databaseTypeId, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.All(result.Data, x => Assert.Equal(databaseTypeId, x.DatabaseTypeId));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewPaginationList;
        await _repository.AddRangeAsync(databaseViews);

        var specification = new TestDatabaseViewSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnFilteredResults_WhenIsNotParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewPaginationList;
        databaseViews.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");

        await _repositoryNotParent.AddRangeAsync(databaseViews);

        var specification = new TestDatabaseViewSpecification();

        // Act
        var result = await _repositoryNotParent.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetDatabaseByType IQueryable Tests - 100% Coverage

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnFilteredDatabases_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeId = "TYPE_PARENT_TEST";

        databaseViews[0].DatabaseTypeId = databaseTypeId;
        databaseViews[0].CompanyId = "PARENT_COMPANY";
        databaseViews[1].DatabaseTypeId = databaseTypeId;
        databaseViews[1].CompanyId = "CHILD_COMPANY";
        databaseViews[2].DatabaseTypeId = "DIFFERENT_TYPE";
        databaseViews[2].CompanyId = "PARENT_COMPANY";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // Parent can see all companies
        Assert.All(resultList, x => Assert.Equal(databaseTypeId, x.DatabaseTypeId));
        Assert.Contains(resultList, x => x.CompanyId == "PARENT_COMPANY");
        Assert.Contains(resultList, x => x.CompanyId == "CHILD_COMPANY");
    }

    //[Fact]
    //public async Task GetDatabaseByType_IQueryable_ShouldReturnFilteredDatabases_WhenIsParentFalse()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var databaseViews = _databaseViewFixture.DatabaseViewList;
    //    var databaseTypeId = "TYPE_CHILD_TEST";

    //    databaseViews[0].DatabaseTypeId = databaseTypeId;
    //    databaseViews[0].CompanyId = "ChHILD_COMPANY_123"; // Matches child company
    //    databaseViews[1].DatabaseTypeId = databaseTypeId;
    //    databaseViews[1].CompanyId = "OTHER_COMPANY"; // Different company
    //    databaseViews[2].DatabaseTypeId = "DIFFERENT_TYPE";
    //    databaseViews[2].CompanyId = "ChHILD_COMPANY_123";

    //    await _repositoryNotParent.AddRangeAsync(databaseViews);

    //    var _mockinfraRepository = new Mock<IInfraObjectRepository>();

    //    var infraobject = GetInfraObjectMock(databaseViews[0].ReferenceId, databaseViews[1].ReferenceId);

    //    _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

    //    var repositoryNotParent = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);

    //    // Act
    //    var result = repositoryNotParent.GetDatabaseByType(databaseTypeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    var resultList = result.ToList();
    //    Assert.Single(resultList); // Child can only see their company
    //    Assert.Equal(databaseTypeId, resultList.First().DatabaseTypeId);
    //    Assert.Equal("ChHILD_COMPANY_123", resultList.First().CompanyId);
    //}

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnAllInfraDatabases_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeId = "TYPE_ALL_INFRA_TEST";

        databaseViews.ForEach(x =>
        {
            x.DatabaseTypeId = databaseTypeId;
            x.CompanyId = "ChHILD_COMPANY_123";
        });

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(databaseViews.Count, resultList.Count);
        Assert.All(resultList, x => Assert.Equal(databaseTypeId, x.DatabaseTypeId));

        // Verify ordering
        var orderedList = resultList.OrderByDescending(x => x.Id).ToList();
        Assert.Equal(orderedList.First().Id, resultList.First().Id);
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnAssignedInfraDatabases_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        // Create infra objects for assigned business services
        var infraObjects = new List<InfraObject>
        {
            new InfraObject
            {
                ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // This is in assigned infras
                CompanyId = "ChHILD_COMPANY_123",
                DatabaseProperties = "[\"DB_ASSIGNED_1\", \"DB_ASSIGNED_2\"]",
                IsActive = true
            }
        };
        await _dbContext.InfraObjects.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var databaseViews = new List<DatabaseView>
        {
            new DatabaseView
            {
                ReferenceId = "DB_ASSIGNED_1",
                DatabaseTypeId = "TYPE_ASSIGNED_TEST",
                BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa", // This is in assigned business services
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Assigned Database 1",
                IsActive = true
            },
            new DatabaseView
            {
                ReferenceId = "DB_UNASSIGNED_1",
                DatabaseTypeId = "TYPE_ASSIGNED_TEST",
                BusinessServiceId = "UNASSIGNED_BS_ID", // Not in assigned business services
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Unassigned Database 1",
                IsActive = true
            }
        };

        await _dbContext.DatabaseViews.AddRangeAsync(databaseViews);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var infraObjectRepoNotAllInfra = new InfraObjectRepository(_dbContext, mockUserService.Object);
        var repositoryNotAllInfra = new DatabaseViewRepository(_dbContext, mockUserService.Object, infraObjectRepoNotAllInfra);

        // Act
        var result = repositoryNotAllInfra.GetDatabaseByType("TYPE_ASSIGNED_TEST");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList); // Only assigned database should be returned
        Assert.Equal("DB_ASSIGNED_1", resultList.First().ReferenceId);
        Assert.Equal("c9b3cd51-f688-4667-be33-46f82b7086fa", resultList.First().BusinessServiceId);
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnEmpty_WhenNoDatabasesMatchType()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        databaseViews.ForEach(x =>
        {
            x.DatabaseTypeId = "DIFFERENT_TYPE";
            x.CompanyId = "ChHILD_COMPANY_123";
        });

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Empty(resultList);
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldHandleNullDatabaseTypeId()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(null);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Empty(resultList); // Should return empty when databaseTypeId is null
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldHandleEmptyDatabaseTypeId()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType("");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Empty(resultList); // Should return empty when databaseTypeId is empty
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnOrderedResults()
    {
        // Arrange
        await ClearDatabase();
        var databaseTypeId = "TYPE_ORDER_TEST";
        var databaseViews = new List<DatabaseView>
        {
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = databaseTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Database 1",
                Id = 1,
                IsActive = true
            },
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = databaseTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Database 2",
                Id = 2,
                IsActive = true
            },
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = databaseTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Database 3",
                Id = 3,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(3, resultList.Count);

        // Verify ordering (should be OrderByDescending(x => x.Id))
        Assert.True(resultList[0].Id >= resultList[1].Id);
        Assert.True(resultList[1].Id >= resultList[2].Id);
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldReturnNoTrackingResults()
    {
        // Arrange
        await ClearDatabase();
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeId = "TYPE_NO_TRACKING_TEST";

        databaseViews.ForEach(x =>
        {
            x.DatabaseTypeId = databaseTypeId;
            x.CompanyId = "ChHILD_COMPANY_123";
        });

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(databaseTypeId);

        // Assert
        Assert.NotNull(result);

        // Verify that the query is set to AsNoTracking
        var resultList = result.ToList();
        Assert.NotEmpty(resultList);

        // Modify one of the entities and verify it's not tracked
        var firstEntity = resultList.First();
        var originalName = firstEntity.Name;
        firstEntity.Name = "Modified Name";

        // Get the same entity again - should not reflect the change since it's not tracked
        var resultAgain = _repository.GetDatabaseByType(databaseTypeId).ToList();
        var sameEntity = resultAgain.First(x => x.ReferenceId == firstEntity.ReferenceId);
        Assert.Equal(originalName, sameEntity.Name); // Should still have original name
    }

    [Fact]
    public async Task GetDatabaseByType_IQueryable_ShouldHandleMultipleDatabaseTypesCorrectly()
    {
        // Arrange
        await ClearDatabase();
        var targetTypeId = "TARGET_TYPE";
        var otherTypeId = "OTHER_TYPE";

        var databaseViews = new List<DatabaseView>
        {
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = targetTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Target Database 1",
                IsActive = true
            },
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = targetTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Target Database 2",
                IsActive = true
            },
            new DatabaseView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseTypeId = otherTypeId,
                CompanyId = "ChHILD_COMPANY_123",
                Name = "Other Database",
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetDatabaseByType(targetTypeId);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);
        Assert.All(resultList, x => Assert.Equal(targetTypeId, x.DatabaseTypeId));
        Assert.All(resultList, x => Assert.Contains("Target Database", x.Name));
    }

    //[Fact]
    //public async Task GetDatabaseByType_IQueryable_ShouldFilterByCompanyId_WhenIsParentFalse()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var databaseTypeId = "TYPE_COMPANY_FILTER_TEST";

    //    var databaseViews = new List<DatabaseView>
    //    {
    //        new DatabaseView
    //        {
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            DatabaseTypeId = databaseTypeId,
    //            CompanyId = "ChHILD_COMPANY_123", // Matches user's company
    //            Name = "User Company Database",
    //            BusinessServiceId= "c9b3cd51-f688-4667-be33-46f82b7086fa",
    //            IsActive = true
    //        },
    //        new DatabaseView
    //        {
    //            ReferenceId = Guid.NewGuid().ToString(),
    //            DatabaseTypeId = databaseTypeId,
    //            CompanyId = "OTHER_COMPANY_456", // Different company
    //            Name = "Other Company Database",
    //           BusinessServiceId= "c9b3cd51-f688-4667-be33-46f82b7086fa",
    //            IsActive = true
    //        }
    //    };

    //    await _repositoryNotParent.AddRangeAsync(databaseViews);

    //    var _mockinfraRepository = new Mock<IInfraObjectRepository>();

    //    var infraobject = GetInfraObjectMock(databaseViews[0].ReferenceId, databaseViews[1].ReferenceId);

    //    _mockinfraRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(infraobject);

    //    var repositoryNotParent = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _mockinfraRepository.Object);
    //    // Act
    //    var result = repositoryNotParent.GetDatabaseByType(databaseTypeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    var resultList = result.ToList();
    //    Assert.Single(resultList); // Only user's company database should be returned
    //    Assert.Equal("ChHILD_COMPANY_123", resultList.First().CompanyId);
    //    Assert.Equal("User Company Database", resultList.First().Name);
    //}

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetByDatabaseIdsAsync_ShouldHandleEmptyIdsList()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByDatabaseIdsAsync(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByDatabaseIdsAsync_ShouldHandleNullIdsList()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByDatabaseIdsAsync(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByUserName_ShouldHandleCaseInsensitive()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var userName = "TestUser";
        databaseViews[0].UserName = userName.ToUpper();
        databaseViews[1].UserName = "Lowe";
        databaseViews[2].UserName = "Di";

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByUserName(userName.ToLower());

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(userName.ToUpper(), result.First().UserName);
    }


    [Fact]
    public async Task GetDatabaseByServerId_ShouldHandleNullServerId()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldHandleNullTypeId()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldHandleNullParameters()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result1 = await _repository.GetByUserNameAndDatabaseType(null, "TYPE_001");
        var result2 = await _repository.GetByUserNameAndDatabaseType("testuser", null);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.Empty(result1);
        Assert.Empty(result2);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.DatabaseViews.RemoveRange(_dbContext.DatabaseViews);
        _dbContext.InfraObjects.RemoveRange(_dbContext.InfraObjects);
        await _dbContext.SaveChangesAsync();
    }

    public IQueryable<InfraObject> GetInfraObjectMock(string database1, string database2)
    {
        var infraObjects = new List<InfraObject>
            {
    new InfraObject
    {
        Id = 1,
        ReferenceId="70bb97c9-1193-4e86-98ab-bebc88fb438c",
        CompanyId = "ChHILD_COMPANY_123",
        DatabaseProperties = JsonConvert.SerializeObject(new
        {
            PR = new
            {
                id = $"{database1}",
                name = "MongoDB_Testing_PR,RAC_PR1_Server",
                type = "PRDB"
            },
            DR = new
            {
                id = $"{database1}",
                name = "MongoDB_Testing_DR,MSSQL AlwaysOnAG_DR1Server",
                type = "DRDB"
            }
        })
    },
    new InfraObject
    {
        Id = 2,
        ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb43d8d",
        CompanyId = "ChHILD_COMPANY_123",
        DatabaseProperties =JsonConvert.SerializeObject(new
        {
            PR = new
            {
                id = $"{database2}",
                name = "MongoDB_Testing_PR,RAC_PR1_Server",
                type = "PRDB"
            },
            DR = new
            {
                id = $"{database2}",
                name = "MongoDB_Testing_DR,MSSQL AlwaysOnAG_DR1Server",
                type = "DRDB"
            }
        })
    }
        }.AsQueryable();
        return infraObjects;
    }
}
