using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using MockQueryable.Moq;
using System;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestWorkflowPredictionSpecification : Specification<WorkflowPrediction>
{
    public TestWorkflowPredictionSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.ActionName.Contains(searchTerm);
        }
    }
}

public class WorkflowPredictionRepositoryTests : IClassFixture<WorkflowPredictionFixture>
{
    private readonly WorkflowPredictionFixture _workflowPredictionFixture;
    private readonly WorkflowPredictionRepository _repository;
    private readonly TestDbContext _dbContext;
    private readonly ApplicationDbContext appDbContext;

    public WorkflowPredictionRepositoryTests(WorkflowPredictionFixture workflowPredictionFixture)
    {
        _workflowPredictionFixture = workflowPredictionFixture;

        appDbContext = _workflowPredictionFixture.DbContext;

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase("TestDb")
            .Options;

        _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(
            _workflowPredictionFixture.WorkflowHistoricalFailureRateViewList,
            _workflowPredictionFixture.WorkflowLastFailureRecencyViewList,
            _workflowPredictionFixture.WorkflowExecutionTimeDeviationViewList,
            _workflowPredictionFixture.WorkflowInfrastructureHealthViewList,
            _workflowPredictionFixture.WorkflowRTOBreachRiskViewList,
            _workflowPredictionFixture.WorkflowRecentConfigurationChangesViewList
        );
        _repository = new WorkflowPredictionRepository(appDbContext);
    }

    #region GetWorkflowFailurePredictionById Tests

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldReturnPredictions_WhenExists()
    {

        var ids = string.Join(",", _workflowPredictionFixture.WorkflowHistoricalFailureRateViewList.Select(x => x.ReferenceId));

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(ids);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains(x.WorkflowId, ids));
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldReturnUnlikelyfailWhenInvalidIds()
    {

        await ClearAllViewsAsync();
        // Act
        var result = await _repository.GetWorkflowFailurePredictionById("test24548");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("Unlikely to Fail", x.Status));
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldThrowException()
    {

       await Assert.ThrowsAsync<NullReferenceException>(async ()=>await _repository.GetWorkflowFailurePredictionById(null));

    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldCalculateCorrectStatus_ModerateRisk()
    {
        // Arrange
        var workflowId = "TEST_WORKFLOW_001";

        // Create test data with specific weightage points that total to 45 (Moderate Risk: 30-60)
        var historicalFailure = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = workflowId,
            WorkflowName = "Test Workflow",
            WeightagePoints = 15
        };

        var rtoBreachRisk = new WorkflowRTOBreachRiskView
        {
            ReferenceId = workflowId,
            WorkflowName = "Test Workflow",
            WeightagePoint = 10
        };

        var lastFailureRecency = new WorkflowLastFailureRecencyView
        {
            ReferenceId = workflowId,
            WorkflowName = "Test Workflow",
            WeightagePoints = 8
        };

        var infrastructureHealth = new WorkflowInfrastructureHealthView
        {
            ReferenceId = workflowId,
            WorkflowName = "Test Workflow",
            Weightage = 7
        };

        var executionTimeDeviation = new WorkflowExecutionTimeDeviationView
        {
            ReferenceId = workflowId,
            WorkflowName = "Test Workflow",
            WeightagePoints = 5
        };

        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(
            new[] { historicalFailure },
        new[] { lastFailureRecency },
        new[] { executionTimeDeviation },
        new[] { infrastructureHealth },
        new[] { rtoBreachRisk },
        Enumerable.Empty<WorkflowRecentConfigurationChangesView>());
        var _repository = new WorkflowPredictionRepository(_dbContext);

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal("Test Workflow", prediction.WorkflowName);
        Assert.Equal("Moderate Risk", prediction.Status); // Total weight = 45, which is >= 30 and <= 60
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldReturnVeryLikelyToFail_WhenHighWeight()
    {
        // Arrange
        var workflowId = "HIGH_RISK_WORKFLOW";

        // Create test data with specific weightage points that total to 70 (Very Likely to Fail: > 60)
        var historicalFailure = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = workflowId,
            WorkflowName = "High Risk Workflow",
            WeightagePoints = 20
        };

        var rtoBreachRisk = new WorkflowRTOBreachRiskView
        {
            ReferenceId = workflowId,
            WorkflowName = "High Risk Workflow",
            WeightagePoint = 15
        };

        var lastFailureRecency = new WorkflowLastFailureRecencyView
        {
            ReferenceId = workflowId,
            WorkflowName = "High Risk Workflow",
            WeightagePoints = 12
        };

        var infrastructureHealth = new WorkflowInfrastructureHealthView
        {
            ReferenceId = workflowId,
            WorkflowName = "High Risk Workflow",
            Weightage = 10
        };

        var executionTimeDeviation = new WorkflowExecutionTimeDeviationView
        {
            ReferenceId = workflowId,
            WorkflowName = "High Risk Workflow",
            WeightagePoints = 8
        };

        var configChange = new WorkflowRecentConfigurationChangesView
        {
            WorkflowId = workflowId,
            WorkflowName = "High Risk Workflow",
            TotalRiskScore = 5
        };

        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(
            new[] { historicalFailure },
        new[] { lastFailureRecency },
        new[] { executionTimeDeviation },
        new[] { infrastructureHealth },
        new[] { rtoBreachRisk },
        new[] { configChange });
        var _repository = new WorkflowPredictionRepository(_dbContext);
        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal("High Risk Workflow", prediction.WorkflowName);
        Assert.Equal("Very Likely to Fail", prediction.Status); // Total weight = 70, which is > 60
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldReturnUnlikelyToFail_WhenLowWeight()
    {
        // Arrange
        var workflowId = "LOW_RISK_WORKFLOW";

        // Create test data with specific weightage points that total to 20 (Unlikely to Fail: < 30)
        var historicalFailure = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = workflowId,
            WorkflowName = "Low Risk Workflow",
            WeightagePoints = 8
        };

        var rtoBreachRisk = new WorkflowRTOBreachRiskView
        {
            ReferenceId = workflowId,
            WorkflowName = "Low Risk Workflow",
            WeightagePoint = 6
        };

        var lastFailureRecency = new WorkflowLastFailureRecencyView
        {
            ReferenceId = workflowId,
            WorkflowName = "Low Risk Workflow",
            WeightagePoints = 4
        };

        var infrastructureHealth = new WorkflowInfrastructureHealthView
        {
            ReferenceId = workflowId,
            WorkflowName = "Low Risk Workflow",
            Weightage = 2
        };
        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(
            new[] { historicalFailure },
        new[] { lastFailureRecency },
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        new[] { infrastructureHealth },
        new[] { rtoBreachRisk },
        Enumerable.Empty<WorkflowRecentConfigurationChangesView>());

        var _repository = new WorkflowPredictionRepository(_dbContext);

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal("Low Risk Workflow", prediction.WorkflowName);
        Assert.Equal("Unlikely to Fail", prediction.Status); // Total weight = 20, which is < 30
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleMultipleWorkflows()
    {
        // Arrange
        var workflowIds = "WORKFLOW_1,WORKFLOW_2,WORKFLOW_3";

        // Create test data for multiple workflows
        var historicalFailures = new List<WorkflowHistoricalFailureRateView>
            {
                new WorkflowHistoricalFailureRateView { ReferenceId = "WORKFLOW_1", WorkflowName = "Workflow 1", WeightagePoints = 10 },
                new WorkflowHistoricalFailureRateView { ReferenceId = "WORKFLOW_2", WorkflowName = "Workflow 2", WeightagePoints = 35 },
                new WorkflowHistoricalFailureRateView { ReferenceId = "WORKFLOW_3", WorkflowName = "Workflow 3", WeightagePoints = 25 }
            };

        var rtoBreachRisks = new List<WorkflowRTOBreachRiskView>
            {
                new WorkflowRTOBreachRiskView { ReferenceId = "WORKFLOW_1", WorkflowName = "Workflow 1", WeightagePoint = 5 },
                new WorkflowRTOBreachRiskView { ReferenceId = "WORKFLOW_2", WorkflowName = "Workflow 2", WeightagePoint = 30 },
                new WorkflowRTOBreachRiskView { ReferenceId = "WORKFLOW_3", WorkflowName = "Workflow 3", WeightagePoint = 20 }
            };

        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(historicalFailures,
       Enumerable.Empty<WorkflowLastFailureRecencyView>(),
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        Enumerable.Empty<WorkflowInfrastructureHealthView>(), rtoBreachRisks,
        Enumerable.Empty<WorkflowRecentConfigurationChangesView>());

        var _repository = new WorkflowPredictionRepository(_dbContext);

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);

        var workflow1 = result.First(x => x.WorkflowId == "WORKFLOW_1");
        Assert.Equal("Unlikely to Fail", workflow1.Status); // 10 + 5 = 15 < 30

        var workflow2 = result.First(x => x.WorkflowId == "WORKFLOW_2");
        Assert.Equal("Very Likely to Fail", workflow2.Status); // 35 + 30 = 65 > 60

        var workflow3 = result.First(x => x.WorkflowId == "WORKFLOW_3");
        Assert.Equal("Moderate Risk", workflow3.Status); // 25 + 20 = 45 (30-60)
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleWorkflowNameFallback()
    {
        // Arrange
        var workflowId = "WORKFLOW_FALLBACK";

        // Only create configuration change (last in fallback chain)
        var configChange = new WorkflowRecentConfigurationChangesView
        {
            WorkflowId = workflowId,
            WorkflowName = "Fallback Workflow Name",
            TotalRiskScore = 15
        };
        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(Enumerable.Empty<WorkflowHistoricalFailureRateView>(),
       Enumerable.Empty<WorkflowLastFailureRecencyView>(),
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        Enumerable.Empty<WorkflowInfrastructureHealthView>(),
         Enumerable.Empty<WorkflowRTOBreachRiskView>(),
        new[] { configChange });
        var _repository = new WorkflowPredictionRepository(_dbContext);
        await _dbContext.WorkflowRecentConfigurationChanges.AddAsync(configChange);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal("Fallback Workflow Name", prediction.WorkflowName); // Should get name from config change
        Assert.Equal("Unlikely to Fail", prediction.Status); // Total weight = 15 < 30
    }

    #endregion

    #region GetWorkflowPredictionByActionId Tests

    [Fact]
    public async Task GetWorkflowPredictionByActionId_ShouldReturnPredictions_WhenExists()
    {
        // Arrange
        await ClearAllViewsAsync();

        await appDbContext.WorkflowPredictions.AddRangeAsync(_workflowPredictionFixture.WorkflowPredictionList);
        await appDbContext.SaveChangesAsync();

        var actionId = _workflowPredictionFixture.WorkflowPredictionList.First().ActionId;

        // Act
        var result = await _repository.GetWorkflowPredictionByActionId(actionId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(actionId, x.ActionId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetWorkflowPredictionByActionId_ShouldReturnEmpty_WhenNotExists()
    {
        // Arrange
        await ClearAllViewsAsync();
        await appDbContext.WorkflowPredictions.AddRangeAsync(_workflowPredictionFixture.WorkflowPredictionList);
        await appDbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetWorkflowPredictionByActionId("NON_EXISTENT_ACTION");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPredictionByActionId_ShouldHandleNullActionId()
    {
        // Act
        var result = await _repository.GetWorkflowPredictionByActionId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPredictionByActionId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        await ClearAllViewsAsync();
        var actionId = "TEST_ACTION_001";
        var workflowPredictions = new List<WorkflowPrediction>
        {
            new WorkflowPrediction
            {
                ActionId = actionId,

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new WorkflowPrediction
            {
                ActionId = actionId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = false
            }
        };

        await appDbContext.WorkflowPredictions.AddRangeAsync(workflowPredictions);
         appDbContext.SaveChanges();

        // Act
        var result = await _repository.GetWorkflowPredictionByActionId(actionId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    #endregion

    #region GetWorkflowPredictionByNextPossibleId Tests

    [Fact]
    public async Task GetWorkflowPredictionByNextPossibleId_ShouldReturnEmpty_WhenNotExists()
    {
        // Arrange
        await _dbContext.WorkflowPredictions.AddRangeAsync(_workflowPredictionFixture.WorkflowPredictionList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetWorkflowPredictionByNextPossibleId("NON_EXISTENT_ID");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPredictionByNextPossibleId_ShouldHandleNullId()
    {
        // Act
        var result = await _repository.GetWorkflowPredictionByNextPossibleId(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPredictionByNextPossibleId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        await ClearAllViewsAsync();
        var nextPossibleId = "NEXT_POSSIBLE_001";
        var workflowPredictions = new List<WorkflowPrediction>
        {
            new WorkflowPrediction
            {
                NextPossibleId = nextPossibleId,

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new WorkflowPrediction
            {
                NextPossibleId = nextPossibleId,

                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = false
            }
        };

        await appDbContext.WorkflowPredictions.AddRangeAsync(workflowPredictions);
         appDbContext.SaveChanges();

        // Act
        var result = await _repository.GetWorkflowPredictionByNextPossibleId(nextPossibleId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }
    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldMapAllViewSourcesCorrectly()
    {
        // Arrange
        var workflowId = Guid.NewGuid().ToString();

        var workflowHistoricalFailureRateViews = (new WorkflowHistoricalFailureRateView
        {
            ReferenceId = workflowId,
            WorkflowName = "Workflow A",
            WeightagePoints = 10
        });

        var workflowRTOBreachRiskViews = (new WorkflowRTOBreachRiskView
        {
            ReferenceId = workflowId,
            WorkflowName = "Workflow A",
            WeightagePoint = 10
        });

        var workflowLastFailureRecencyViews = (new WorkflowLastFailureRecencyView
        {
            ReferenceId = workflowId,
            WorkflowName = "Workflow A",
            WeightagePoints = 10
        });

        var workflowInfrastructureHealthViews = (new WorkflowInfrastructureHealthView
        {
            ReferenceId = workflowId,
            WorkflowName = "Workflow A",
            Weightage = 10
        });

        var workflowExecutionTimeDeviationViews = (new WorkflowExecutionTimeDeviationView
        {
            ReferenceId = workflowId,
            WorkflowName = "Workflow A",
            WeightagePoints = 10
        });

        var workflowRecentConfigurationChanges = (new WorkflowRecentConfigurationChangesView
        {
            WorkflowId = workflowId,
            WorkflowName = "Workflow A",
            TotalRiskScore = 10
        });

        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(
            new[] { workflowHistoricalFailureRateViews },
        new[] { workflowLastFailureRecencyViews },
        new[] { workflowExecutionTimeDeviationViews },
        new[] { workflowInfrastructureHealthViews },
        new[] { workflowRTOBreachRiskViews },
        new[] { workflowRecentConfigurationChanges });
        var _repository = new WorkflowPredictionRepository(_dbContext);
        // Act


        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.Single(result);
        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal("Workflow A", prediction.WorkflowName);
        Assert.Equal("Moderate Risk", prediction.Status); // total weight = 60
    }

    #endregion

    #region Edge Cases for GetWorkflowFailurePredictionById

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleEmptyWorkflowName_WhenNoDataExists()
    {
        // Arrange
        var workflowId = "WORKFLOW_NO_DATA";

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal(workflowId, prediction.WorkflowId);
        Assert.Equal(string.Empty, prediction.WorkflowName); // Should be empty when no data exists
        Assert.Equal("Unlikely to Fail", prediction.Status); // Total weight = 0 < 30
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleWhitespaceInIds()
    {
        // Arrange
        var workflowIds = " WORKFLOW_1 , WORKFLOW_2 , WORKFLOW_3 "; // With spaces

        var historicalFailure = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = "WORKFLOW_1",
            WorkflowName = "Workflow 1",
            WeightagePoints = 10
        };
        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());

        _dbContext.SeedViews(new[] { historicalFailure },
       Enumerable.Empty<WorkflowLastFailureRecencyView>(),
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        Enumerable.Empty<WorkflowInfrastructureHealthView>(),
         Enumerable.Empty<WorkflowRTOBreachRiskView>(),
         Enumerable.Empty<WorkflowRecentConfigurationChangesView>());

        var _repository = new WorkflowPredictionRepository(_dbContext);

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Contains(result, x => x.WorkflowId == "WORKFLOW_1");
        Assert.Contains(result, x => x.WorkflowId == "WORKFLOW_2");
        Assert.Contains(result, x => x.WorkflowId == "WORKFLOW_3");
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleNullWeightageValues()
    {
        // Arrange
        var workflowId = "WORKFLOW_NULL_VALUES";

        // Create entities with null weightage values (should default to 0)
        var historicalFailure = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = workflowId,
            WorkflowName = "Null Values Workflow",
            WeightagePoints = 0 // This should be treated as 0
        };
        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());
        _dbContext.SeedViews(new[] { historicalFailure },
       Enumerable.Empty<WorkflowLastFailureRecencyView>(),
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        Enumerable.Empty<WorkflowInfrastructureHealthView>(),
         Enumerable.Empty<WorkflowRTOBreachRiskView>(),
         Enumerable.Empty<WorkflowRecentConfigurationChangesView>());
        var _repository = new WorkflowPredictionRepository(_dbContext);

        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var prediction = result.First();
        Assert.Equal("Unlikely to Fail", prediction.Status); // Should handle null as 0
    }

    [Fact]
    public async Task GetWorkflowFailurePredictionById_ShouldHandleBoundaryValues()
    {
        // Arrange - Test exact boundary values
        var workflowIds = "BOUNDARY_30,BOUNDARY_60,BOUNDARY_61";

        // Workflow with exactly 30 points (should be "Moderate Risk")
        var historical30 = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = "BOUNDARY_30",
            WorkflowName = "Boundary 30",
            WeightagePoints = 30
        };

        // Workflow with exactly 60 points (should be "Moderate Risk")
        var historical60 = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = "BOUNDARY_60",
            WorkflowName = "Boundary 60",
            WeightagePoints = 60
        };

        // Workflow with 61 points (should be "Very Likely to Fail")
        var historical61 = new WorkflowHistoricalFailureRateView
        {
            ReferenceId = "BOUNDARY_61",
            WorkflowName = "Boundary 61",
            WeightagePoints = 61
        };

        var options = CreateInMemoryOptions("TestDb");

        var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());

        _dbContext.SeedViews(new[] { historical30, historical60, historical61, },
       Enumerable.Empty<WorkflowLastFailureRecencyView>(),
        Enumerable.Empty<WorkflowExecutionTimeDeviationView>(),
        Enumerable.Empty<WorkflowInfrastructureHealthView>(),
         Enumerable.Empty<WorkflowRTOBreachRiskView>(),
         Enumerable.Empty<WorkflowRecentConfigurationChangesView>());
        var _repository = new WorkflowPredictionRepository(_dbContext);
        // Act
        var result = await _repository.GetWorkflowFailurePredictionById(workflowIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);

        var boundary30 = result.First(x => x.WorkflowId == "BOUNDARY_30");
        Assert.Equal("Moderate Risk", boundary30.Status); // 30 >= 30

        var boundary60 = result.First(x => x.WorkflowId == "BOUNDARY_60");
        Assert.Equal("Moderate Risk", boundary60.Status); // 60 <= 60

        var boundary61 = result.First(x => x.WorkflowId == "BOUNDARY_61");
        Assert.Equal("Very Likely to Fail", boundary61.Status); // 61 > 60
    }

    #endregion

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddWorkflowPrediction_Successfully()
    {
        // Arrange
        var workflowPrediction = new WorkflowPrediction
        {
            ActionId = "ACTION_001",
            NextPossibleId = "NEXT_001",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        // Act
        await _repository.AddAsync(workflowPrediction);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(workflowPrediction.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal(workflowPrediction.ActionId, result.ActionId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateWorkflowPrediction_Successfully()
    {
        // Arrange
        var workflowPrediction = _workflowPredictionFixture.WorkflowPredictionDto;
        await _repository.AddAsync(workflowPrediction);

        // Act
        workflowPrediction.ActionName = "Updated Workflow Name";
        await _repository.UpdateAsync(workflowPrediction);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(workflowPrediction.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("Updated Workflow Name", result.ActionName);
    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        await ClearAllViewsAsync();
        // Arrange
        var workflowPrediction = _workflowPredictionFixture.WorkflowPredictionDto;
        await _repository.AddAsync(workflowPrediction);

        // Act
        workflowPrediction.IsActive = false;

        await _repository.UpdateAsync(workflowPrediction);

        // Assert
        var result = await _repository.GetByReferenceIdAsync(workflowPrediction.ReferenceId);

        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
       await ClearAllViewsAsync();


        var workflowPredictions = _workflowPredictionFixture.WorkflowPredictionList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(workflowPredictions);
        var initialCount = workflowPredictions.Count;

        var toUpdate = workflowPredictions.Take(2).ToList();
        toUpdate.ForEach(x => x.ActionName = "Updated Name");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = workflowPredictions.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.ActionName == "Updated Name").ToList();
        Assert.Equal(2, updated.Count);
    }
    public static DbContextOptions<ApplicationDbContext> CreateInMemoryOptions(string dbName = "TestDb")
    {
        return new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;
    }

    #endregion
    private async Task ClearAllViewsAsync()
    {
        var options = CreateInMemoryOptions("TestDb");

        await using var _dbContext = new TestDbContext(options, DbContextFactory.GetMockUserService());

        _dbContext.WorkflowHistoricalFailureRateViews.RemoveRange(_dbContext.WorkflowHistoricalFailureRateViews);
        _dbContext.WorkflowLastFailureRecencyViews.RemoveRange(_dbContext.WorkflowLastFailureRecencyViews);
        _dbContext.WorkflowExecutionTimeDeviationViews.RemoveRange(_dbContext.WorkflowExecutionTimeDeviationViews);
        _dbContext.WorkflowInfrastructureHealthViews.RemoveRange(_dbContext.WorkflowInfrastructureHealthViews);
        _dbContext.WorkflowRTOBreachRiskViews.RemoveRange(_dbContext.WorkflowRTOBreachRiskViews);
        _dbContext.WorkflowRecentConfigurationChanges.RemoveRange(_dbContext.WorkflowRecentConfigurationChanges);

        appDbContext.WorkflowPredictions.RemoveRange(appDbContext.WorkflowPredictions);
        await _dbContext.SaveChangesAsync();
    }

}


public class TestDbContext : ApplicationDbContext
{
    public TestDbContext(DbContextOptions<ApplicationDbContext> options, ILoggedInUserService userService)
        : base(options, userService)
    {
    }

    public void SeedViews(
        IEnumerable<WorkflowHistoricalFailureRateView> historicalRates,
        IEnumerable<WorkflowLastFailureRecencyView> lastFailures,
        IEnumerable<WorkflowExecutionTimeDeviationView> timeDeviations,
        IEnumerable<WorkflowInfrastructureHealthView> healthViews,
        IEnumerable<WorkflowRTOBreachRiskView> rtoViews,
        IEnumerable<WorkflowRecentConfigurationChangesView> configChanges)
    {
        WorkflowHistoricalFailureRateViews = historicalRates.AsQueryable().BuildMockDbSet().Object;
        WorkflowLastFailureRecencyViews = lastFailures.AsQueryable().BuildMockDbSet().Object;
        WorkflowExecutionTimeDeviationViews = timeDeviations.AsQueryable().BuildMockDbSet().Object;
        WorkflowInfrastructureHealthViews = healthViews.AsQueryable().BuildMockDbSet().Object;
        WorkflowRTOBreachRiskViews = rtoViews.AsQueryable().BuildMockDbSet().Object;
        WorkflowRecentConfigurationChanges = configChanges.AsQueryable().BuildMockDbSet().Object;
    }

   
}