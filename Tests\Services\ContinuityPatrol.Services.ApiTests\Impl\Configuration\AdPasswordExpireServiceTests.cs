﻿using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class AdPasswordExpireServiceTests : IClassFixture<AdPasswordExpireServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly AdPasswordExpireService _service;
    private readonly AdPasswordExpireServiceFixture _fixture;

    public AdPasswordExpireServiceTests(AdPasswordExpireServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new AdPasswordExpireService(_clientMock.Object);
    }

    [Fact]
    public async Task GetAdPasswordExpireList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<AdPasswordExpireListVm>>(It.IsAny<RestRequest>(), "GetAdPasswordExpireList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetAdPasswordExpireList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("id123");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _clientMock.Setup(x => x.Get<AdPasswordExpireDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsAdPasswordExpireNameExist_ShouldReturnTrue()
    {
        _clientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsAdPasswordExpireNameExist("name", "id");

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedAdPasswordExpires_ShouldReturnPaginatedResult()
    {
        _clientMock.Setup(x => x.Get<PaginatedResult<AdPasswordExpireListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _service.GetPaginatedAdPasswordExpires(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
