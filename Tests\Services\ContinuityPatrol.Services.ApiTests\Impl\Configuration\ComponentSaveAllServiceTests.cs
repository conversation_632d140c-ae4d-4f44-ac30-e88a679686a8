﻿using ContinuityPatrol.Application.Features.ComponentSaveAll.Queries.GetDetail;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using RestSharp;
using Xunit;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class ComponentSaveAllServiceTests : IClassFixture<ComponentSaveAllServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly ComponentSaveAllService _service;
    private readonly ComponentSaveAllServiceFixture _fixture;

    public ComponentSaveAllServiceTests(ComponentSaveAllServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new ComponentSaveAllService(_clientMock.Object);
    }

    [Fact]
    public async Task GetComponentById_ShouldReturnComponentDetailVm()
    {
        // Arrange
        _clientMock.Setup(c => c.Get<ComponentSaveAllDetailVm>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.ComponentDetailVm);

        // Act
        var result = await _service.GetComponentById(_fixture.ComponentId);

        // Assert
        Assert.Equal(_fixture.ComponentDetailVm, result);
    }
}