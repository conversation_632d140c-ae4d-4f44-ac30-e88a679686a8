using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DashboardViewLogRepositoryTests : IClassFixture<DashboardViewLogFixture>,IClassFixture<BusinessServiceFixture>
{
    private readonly DashboardViewLogFixture _dashboardViewLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DashboardViewLogRepository _repository;
    private readonly DashboardViewLogRepository _repositoryNotParent;
    private readonly BusinessServiceFixture _businessServiceFixture;

    public DashboardViewLogRepositoryTests(DashboardViewLogFixture dashboardViewLogFixture, BusinessServiceFixture businessServiceFixture)
    {
        _dashboardViewLogFixture = dashboardViewLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DashboardViewLogRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DashboardViewLogRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _businessServiceFixture = businessServiceFixture;

    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        var bsList=_businessServiceFixture.BusinessServiceList;

        bsList[0].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        await _dbContext.BusinessServices.AddRangeAsync(bsList);
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
         dashboardViewLog.BusinessServiceId = bsList[0].ReferenceId;
        // Act
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
        Assert.Equal(dashboardViewLog.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(dashboardViewLog.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DashboardViewLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var bsList = _businessServiceFixture.BusinessServiceList;

        bsList[0].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        bsList[0].Name = "Updated Service Name";
        await _dbContext.BusinessServices.AddRangeAsync(bsList);
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;

        dashboardViewLog.BusinessServiceId = bsList[0].ReferenceId;

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync(); 

        dashboardViewLog.BusinessServiceName = "Updated Service Name";

        // Act
         _dbContext.DashboardViewLogs.Update(dashboardViewLog);
         await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act

        dashboardViewLog.IsActive=false;
         _dbContext.DashboardViewLogs.Update(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByIdAsync(dashboardViewLog.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.Id, result.Id);
        Assert.Equal(dashboardViewLog.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        dashboardViewLog.CompanyId = "ChHILD_COMPANY_123";
        dashboardViewLog.ReferenceId = Guid.NewGuid().ToString();
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
        Assert.Equal(dashboardViewLog.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;

        dashboardViewLog.CompanyId = "ChHILD_COMPANY_123";
        dashboardViewLog.ReferenceId = Guid.NewGuid().ToString();

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        await _repositoryNotParent.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesFromYesterday()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to today (should not be included)
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today);
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDashboardViewLogByLast30daysList Tests

    [Fact]
    public async Task GetDashboardViewLogByLast30daysList_ShouldReturnEntitiesFromLast30Days_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set some logs within last 30 days
        dashboardViewLogs.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-15));
        // Set some logs older than 30 days
        dashboardViewLogs.Skip(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-35));
        
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByLast30daysList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.LastModifiedDate.Date >= DateTime.Today.AddDays(-30)));
    }

    [Fact]
    public async Task GetDashboardViewLogByLast30daysList_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set some logs within last 30 days
        dashboardViewLogs.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-15));
       
        
        await _repositoryNotParent.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repositoryNotParent.GetDashboardViewLogByLast30daysList();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDashboardViewLogByInfraObjectId Tests

    [Fact]
    public async Task GetDashboardViewLogByInfraObjectId_ShouldReturnEntitiesWithMatchingInfraObjectId()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate hour to 1 for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddHours(1));
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByInfraObjectId(DashboardViewLogFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DashboardViewLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(1, x.LastModifiedDate.Hour));
    }

    [Fact]
    public async Task GetDashboardViewLogByInfraObjectId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByInfraObjectId("non-existent-infra-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDataLagByOneDayReport Tests

    [Fact]
    public async Task GetDataLagByOneDayReport_ShouldReturnEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDataLagByOneDayReport(DashboardViewLogFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DashboardViewLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(DateTime.Today.AddDays(-1).Date, x.LastModifiedDate.Date));
    }

    [Fact]
    public async Task GetDataLagByOneDayReport_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        dashboardViewLogs.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        dashboardViewLogs.ForEach(x => x.BusinessFunctionId = "BF1");
        dashboardViewLogs.ForEach(x => x.BusinessServiceId = "BS1");

        dashboardViewLogs.ForEach(x => x.InfraObjectId = "INFRA_1");

        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repositoryNotParent.GetDataLagByOneDayReport(dashboardViewLogs[0].InfraObjectId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;

        // Act
        var result = await _repository.AddRangeAsync(dashboardViewLogs);

        // Assert
        Assert.Equal(dashboardViewLogs.Count, result.Count());
        Assert.Equal(dashboardViewLogs.Count, _dbContext.DashboardViewLogs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.RemoveRangeAsync(dashboardViewLogs);

        // Assert
        Assert.Equal(dashboardViewLogs.Count, result.Count());
        Assert.Empty(_dbContext.DashboardViewLogs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogPaginationList.Take(5).ToList();

        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        // Act - Add, then update some, then delete some
        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);

        _dbContext.SaveChanges();
        var initialCount = dashboardViewLogs.Count;
        
        var toUpdate = dashboardViewLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.DataLagValue = "00:00:44");
         _dbContext.DashboardViewLogs.UpdateRange(toUpdate);
        _dbContext.SaveChanges();

        var toDelete = dashboardViewLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

       
   // Assert
   var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.DataLagValue == "00:00:44").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region Uncovered Methods Tests - 100% Coverage

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnMatchingInfraObjects_WhenAssignedInfraObjectsExist()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa"; // This is in assigned business services
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = new Domain.Entities.BusinessFunction
        {
            ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93", // This is in assigned business functions
            Name = "Test Business Function",
            CompanyId = "ChHILD_COMPANY_123",
            BusinessServiceId = businessService.ReferenceId,
            IsActive = true
        };
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = new Domain.Entities.InfraObject
        {
            ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // This is in assigned infra objects
            Name = "Test Infra Object",
            CompanyId = "ChHILD_COMPANY_123",
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            IsActive = true
        };
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var dashboardViewLogs = new List<Domain.Entities.DashboardViewLog>
        {
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObject.ReferenceId, // Assigned infra object
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            },
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "UNASSIGNED_INFRA_ID", // Non-assigned infra object
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            }
        };

        await _dbContext.DashboardViewLogs.AddRangeAsync(dashboardViewLogs);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.Where(x => x.CompanyId == "ChHILD_COMPANY_123").AsQueryable();
        var result = repositoryNotAllInfra.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only assigned infra object should be returned
        Assert.Equal(infraObject.ReferenceId, result.First().InfraObjectId);
    }

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_ID_1",
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with empty assigned business services
        var mockUserService = new Mock<ILoggedInUserService>();
        var emptyAssignedEntity = new AssignedEntity
        {
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        };
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(emptyAssignedEntity);
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryEmptyAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryEmptyAssigned.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when no assigned business services
    }

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedBusinessFunctions()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_ID_1",
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with assigned business services but no business functions
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedEntityWithEmptyFunctions = new AssignedEntity
        {
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>() // Empty functions
                }
            }
        };
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(assignedEntityWithEmptyFunctions);
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryEmptyFunctions = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryEmptyFunctions.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when no assigned business functions
    }

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnEmpty_WhenNoAssignedInfraObjects()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_ID_1",
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with assigned business functions but no infra objects
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedEntityWithEmptyInfraObjects = new AssignedEntity
        {
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>() // Empty infra objects
                        }
                    }
                }
            }
        };
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(assignedEntityWithEmptyInfraObjects);
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryEmptyInfraObjects = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryEmptyInfraObjects.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when no assigned infra objects
    }

    [Fact]
    public async Task AssignedBusinessServices_ShouldReturnMatchingBusinessServices_WhenAssignedBusinessServicesExist()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa"; // This is in assigned business services
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        var dashboardViewLogs = new List<Domain.Entities.DashboardViewLog>
        {
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService.ReferenceId, // Assigned business service
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            },
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "UNASSIGNED_BS_ID", // Non-assigned business service
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            }
        };

        await _dbContext.DashboardViewLogs.AddRangeAsync(dashboardViewLogs);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.Where(x => x.CompanyId == "ChHILD_COMPANY_123").AsQueryable();
        var result = repositoryNotAllInfra.AssignedBusinessServices(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only assigned business service should be returned
        Assert.Equal(businessService.ReferenceId, result.First().BusinessServiceId);
    }

    [Fact]
    public async Task AssignedBusinessServices_ShouldReturnEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with empty assigned business services
        var mockUserService = new Mock<ILoggedInUserService>();
        var emptyAssignedEntity = new AssignedEntity
        {
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        };
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(emptyAssignedEntity);
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryEmptyAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryEmptyAssigned.AssignedBusinessServices(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when no assigned business services
    }

    [Fact]
    public async Task AssignedBusinessServices_ShouldHandleNullBusinessServiceId()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = null, // Null business service ID
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with assigned business services
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryWithAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryWithAssigned.AssignedBusinessServices(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when business service ID is null
    }

    [Fact]
    public async Task AssignedBusinessServices_ShouldReturnMultipleMatches_WhenMultipleAssignedBusinessServicesExist()
    {
        // Arrange
        await ClearDatabase();
        var businessService1 = _businessServiceFixture.BusinessServiceDto;
        businessService1.ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa"; // This is in assigned business services
        var businessService2 = _businessServiceFixture.BusinessServiceList[1];
        businessService2.ReferenceId = "ANOTHER_ASSIGNED_BS_ID";

        await _dbContext.BusinessServices.AddRangeAsync(new[] { businessService1, businessService2 });
        await _dbContext.SaveChangesAsync();

        var dashboardViewLogs = new List<Domain.Entities.DashboardViewLog>
        {
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService1.ReferenceId,
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            },
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessService1.ReferenceId, // Same business service, different log
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            }
        };

        await _dbContext.DashboardViewLogs.AddRangeAsync(dashboardViewLogs);
        await _dbContext.SaveChangesAsync();

        // Create repository with assigned business services
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryWithAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryWithAssigned.AssignedBusinessServices(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Both logs should be returned as they use the same assigned business service
        Assert.All(result, x => Assert.Equal(businessService1.ReferenceId, x.BusinessServiceId));
    }

   

    [Fact]
    public async Task AssignedBusinessServices_ShouldThrowExceptionOnNullAssignedEntity()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with null AssignedEntity
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns((string)null); // This will make AssignedEntity null

        var repositoryNullAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        

       await Assert.ThrowsAsync<NullReferenceException>( () => Task.FromResult(
           repositoryNullAssigned.AssignedBusinessServices(queryable)));
       
    }

    [Fact]
    public async Task AssignedInfraObjects_ShouldHandleNullInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var dashboardViewLog = new Domain.Entities.DashboardViewLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = null, // Null infra object ID
            BusinessServiceId = "BS_ID_1",
            CompanyId = "ChHILD_COMPANY_123",
            LastModifiedDate = DateTime.Today.AddDays(-1),
            IsActive = true
        };

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();

        // Create repository with assigned infra objects
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryWithAssigned = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryWithAssigned.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty when infra object ID is null
    }

    [Fact]
    public async Task AssignedInfraObjects_ShouldReturnMultipleMatches_WhenMultipleAssignedInfraObjectsExist()
    {
        // Arrange
        await ClearDatabase();
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa"; // This is in assigned business services
        await _dbContext.BusinessServices.AddAsync(businessService);

        var businessFunction = new Domain.Entities.BusinessFunction
        {
            ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93", // This is in assigned business functions
            Name = "Test Business Function",
            CompanyId = "ChHILD_COMPANY_123",
            BusinessServiceId = businessService.ReferenceId,
            IsActive = true
        };
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);

        var infraObject = new Domain.Entities.InfraObject
        {
            ReferenceId = "70bb97c9-1193-4e86-98ab-bebc88fb438c", // This is in assigned infra objects
            Name = "Test Infra Object",
            CompanyId = "ChHILD_COMPANY_123",
            BusinessServiceId = businessService.ReferenceId,
            BusinessFunctionId = businessFunction.ReferenceId,
            IsActive = true
        };
        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        var dashboardViewLogs = new List<Domain.Entities.DashboardViewLog>
        {
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObject.ReferenceId,
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            },
            new Domain.Entities.DashboardViewLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObject.ReferenceId, // Same infra object, different log
                BusinessServiceId = businessService.ReferenceId,
                BusinessFunctionId = businessFunction.ReferenceId,
                CompanyId = "ChHILD_COMPANY_123",
                LastModifiedDate = DateTime.Today.AddDays(-1),
                IsActive = true
            }
        };

        await _dbContext.DashboardViewLogs.AddRangeAsync(dashboardViewLogs);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new DashboardViewLogRepository(_dbContext, mockUserService.Object);

        // Act
        var queryable = _dbContext.DashboardViewLogs.AsQueryable();
        var result = repositoryNotAllInfra.AssignedInfraObjects(queryable);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Both logs should be returned as they use the same assigned infra object
        Assert.All(result, x => Assert.Equal(infraObject.ReferenceId, x.InfraObjectId));
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.DashboardViewLogs.RemoveRange(_dbContext.DashboardViewLogs);
        _dbContext.BusinessServices.RemoveRange(_dbContext.BusinessServices);
        _dbContext.BusinessFunctions.RemoveRange(_dbContext.BusinessFunctions);
        _dbContext.InfraObjects.RemoveRange(_dbContext.InfraObjects);
        await _dbContext.SaveChangesAsync();
    }

  
}
