﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class ServerTypeServiceTests : IClassFixture<ServerTypeServiceFixture>
{
    private readonly ServerTypeServiceFixture _fixture;

    public ServerTypeServiceTests(ServerTypeServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Post<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Put<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(x => x.Delete<BaseResponse>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.BaseResponse);

        var result = await _fixture.Service.DeleteAsync(_fixture.Id);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetServerTypeList_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<ServerTypeListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _fixture.Service.GetServerTypeList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetServerTypeById_Should_Return_Detail()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<ServerTypeDetailVm>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _fixture.Service.GetServerTypeById(_fixture.Id);

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task IsServerTypeExist_Should_Return_TrueOrFalse()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<bool>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(true);

        var result = await _fixture.Service.IsServerTypeExist(_fixture.TypeName, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetServerTypeListByName_Should_Return_List()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<List<ServerTypeModel>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.ModelList);

        var result = await _fixture.Service.GetServerTypeListByName(_fixture.TypeName);

        Assert.Equal(_fixture.ModelList, result);
    }

    [Fact]
    public async Task GetPaginatedServerTypes_Should_Return_PaginatedResult()
    {
        _fixture.ClientMock
            .Setup(x => x.Get<PaginatedResult<ServerTypeListVm>>(It.IsAny<RestSharp.RestRequest>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _fixture.Service.GetPaginatedServerTypes(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult, result);
    }
}
