﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class DataSyncJobServiceFixture
{
    public IFixture Fixture { get; }
    public Mock<IBaseClient> ClientMock { get; }
    public DataSyncJobService Service { get; }

    public CreateDataSyncJobCommand CreateCommand { get; }
    public UpdateDataSyncJobCommand UpdateCommand { get; }
    public GetDataSyncJobPaginatedQuery PaginatedQuery { get; }

    public BaseResponse BaseResponse { get; }
    public DataSyncJobDetailVm DetailVm { get; }
    public List<DataSyncJobListVm> ListVm { get; }
    public PaginatedResult<DataSyncJobListVm> PaginatedVm { get; }

    public DataSyncJobServiceFixture()
    {
        Fixture = new Fixture();
        ClientMock = new Mock<IBaseClient>();
        Service = new DataSyncJobService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateDataSyncJobCommand>();
        UpdateCommand = Fixture.Create<UpdateDataSyncJobCommand>();
        PaginatedQuery = Fixture.Create<GetDataSyncJobPaginatedQuery>();

        BaseResponse = Fixture.Create<BaseResponse>();
        DetailVm = Fixture.Create<DataSyncJobDetailVm>();
        ListVm = Fixture.Create<List<DataSyncJobListVm>>();
        PaginatedVm = Fixture.Create<PaginatedResult<DataSyncJobListVm>>();
    }
}