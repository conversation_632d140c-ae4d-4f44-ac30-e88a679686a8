﻿using AutoFixture;
using AutoFixture.AutoMoq;
using ContinuityPatrol.Application.Features.ServerType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ServerType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using Moq;
using System.Collections.Generic;

public class ServerTypeServiceFixture
{
    public Mock<IBaseClient> ClientMock { get; }
    public ServerTypeService Service { get; }

    public IFixture Fixture { get; }

    public CreateServerTypeCommand CreateCommand { get; }
    public UpdateServerTypeCommand UpdateCommand { get; }
    public BaseResponse BaseResponse { get; }
    public string Id { get; }
    public string TypeName { get; }
    public List<ServerTypeListVm> ListVm { get; }
    public ServerTypeDetailVm DetailVm { get; }
    public List<ServerTypeModel> ModelList { get; }
    public PaginatedResult<ServerTypeListVm> PaginatedResult { get; }
    public GetServerTypePaginatedListQuery PaginatedQuery { get; }

    public ServerTypeServiceFixture()
    {
        Fixture = new Fixture().Customize(new AutoMoqCustomization());

        ClientMock = new Mock<IBaseClient>();
        Service = new ServerTypeService(ClientMock.Object);

        CreateCommand = Fixture.Create<CreateServerTypeCommand>();
        UpdateCommand = Fixture.Create<UpdateServerTypeCommand>();
        BaseResponse = Fixture.Create<BaseResponse>();
        Id = Fixture.Create<string>();
        TypeName = Fixture.Create<string>();
        ListVm = Fixture.Create<List<ServerTypeListVm>>();
        DetailVm = Fixture.Create<ServerTypeDetailVm>();
        ModelList = Fixture.Create<List<ServerTypeModel>>();
        PaginatedQuery = Fixture.Create<GetServerTypePaginatedListQuery>();
        PaginatedResult = Fixture.Create<PaginatedResult<ServerTypeListVm>>();
    }
}
