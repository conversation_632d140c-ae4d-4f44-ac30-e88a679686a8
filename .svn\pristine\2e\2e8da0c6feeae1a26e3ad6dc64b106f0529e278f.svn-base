﻿using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;

public class BulkImportActionResultServiceTests : IClassFixture<BulkImportActionResultServiceFixture>
{
    private readonly Mock<IBaseClient> _clientMock;
    private readonly BulkImportActionResultService _service;
    private readonly BulkImportActionResultServiceFixture _fixture;

    public BulkImportActionResultServiceTests(BulkImportActionResultServiceFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new BulkImportActionResultService(_clientMock.Object);
    }

    [Fact]
    public async Task GetBulkImportActionResultList_ShouldReturnList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<BulkImportActionResultListVm>>(It.IsAny<RestRequest>(), "GetBulkImportActionResultList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBulkImportActionResultList();

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync("some-id");

        Assert.Equal(_fixture.BaseResponse, result);
    }

    [Fact]
    public async Task GetByReferenceId_ShouldReturnDetailVm()
    {
        _clientMock.Setup(x => x.Get<BulkImportActionResultDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId("ref-id");

        Assert.Equal(_fixture.DetailVm, result);
    }

    [Fact]
    public async Task GetByOperationIdAndOperationGroupId_ShouldReturnList()
    {
        _clientMock.Setup(x => x.Get<List<BulkImportActionResultListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetByOperationIdAndOperationGroupId("op-id", "group-id");

        Assert.Equal(_fixture.ListVm, result);
    }

    [Fact]
    public async Task GetBulkImportActionResultOperationGroupId_ShouldReturnList()
    {
        _clientMock.Setup(x => x.Get<List<BulkImportActionResultListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetBulkImportActionResultOperationGroupId("group-id");

        Assert.Equal(_fixture.ListVm, result);
    }
}
