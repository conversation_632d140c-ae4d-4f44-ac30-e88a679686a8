using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestBusinessServiceHealthStatusSpecification : Specification<BusinessServiceHealthStatus>
{
    public TestBusinessServiceHealthStatusSpecification(string searchTerm )
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.ProblemState.Contains(searchTerm);
        }
    }
}

public class BusinessServiceHealthStatusRepositoryTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly BusinessServiceHealthStatusFixture _businessServiceHealthStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceHealthStatusRepository _repository;
    private readonly BusinessServiceHealthStatusRepository _repositoryNotParent;

    public BusinessServiceHealthStatusRepositoryTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusFixture = businessServiceHealthStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceHealthStatusRepository(_dbContext);
        _repositoryNotParent = new BusinessServiceHealthStatusRepository(_dbContext);
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddBusinessServiceHealthStatus_Successfully()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties();

        // Act
        var result = await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceHealthStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceHealthStatus.ConfiguredCount, result.ConfiguredCount);
        Assert.Equal(businessServiceHealthStatus.DRReadyCount, result.DRReadyCount);
        Assert.Equal(businessServiceHealthStatus.DRNotReadyCount, result.DRNotReadyCount);
        Assert.Equal(businessServiceHealthStatus.ProblemState, result.ProblemState);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnBusinessServiceHealthStatus_WhenExists()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties();
        await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessServiceHealthStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceHealthStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceHealthStatus.ConfiguredCount, result.ConfiguredCount);
        Assert.Equal(businessServiceHealthStatus.DRReadyCount, result.DRReadyCount);
        Assert.Equal(businessServiceHealthStatus.DRNotReadyCount, result.DRNotReadyCount);
        Assert.Equal(businessServiceHealthStatus.ProblemState, result.ProblemState);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllActiveBusinessServiceHealthStatuses()
    {
        // Arrange
        var businessServiceHealthStatuses = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList;
        foreach (var status in businessServiceHealthStatuses)
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= businessServiceHealthStatuses.Count);
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateBusinessServiceHealthStatus_Successfully()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties();
        await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Modify properties
        businessServiceHealthStatus.ConfiguredCount = 200;
        businessServiceHealthStatus.DRReadyCount = 180;
        businessServiceHealthStatus.DRNotReadyCount = 20;
        businessServiceHealthStatus.ProblemState = "Updated Status";

        // Act
        await _repository.UpdateAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        var updatedStatus = await _repository.GetByReferenceIdAsync(businessServiceHealthStatus.ReferenceId);
        Assert.NotNull(updatedStatus);
        Assert.Equal(200, updatedStatus.ConfiguredCount);
        Assert.Equal(180, updatedStatus.DRReadyCount);
        Assert.Equal(20, updatedStatus.DRNotReadyCount);
        Assert.Equal("Updated Status", updatedStatus.ProblemState);
    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkBusinessServiceHealthStatusAsInactive_Successfully()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties();
        await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        businessServiceHealthStatus.IsActive = false;
        _dbContext.BusinessServiceHealthStatuses.Update(businessServiceHealthStatus);
         _dbContext.SaveChanges();

        // Assert
        var deletedStatus = await _repository.GetByReferenceIdAsync(businessServiceHealthStatus.ReferenceId);   
       
        Assert.NotNull(deletedStatus);
        Assert.False(deletedStatus.IsActive);
    }

    #endregion

    #region Specification Tests

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnMatchingBusinessServiceHealthStatuses()
    {
        // Arrange
        var operationalStatuses = _businessServiceHealthStatusFixture.CreateMultipleBusinessServiceHealthStatusWithSameProblemState("Operational", 3);
        var issuesStatuses = _businessServiceHealthStatusFixture.CreateMultipleBusinessServiceHealthStatusWithSameProblemState("Issues Detected", 2);

        foreach (var status in operationalStatuses.Concat(issuesStatuses))
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new BusinessServiceHealthStatusFilterSpecification("Operational");

        // Act
        var result = await _repository.PaginatedListAllAsync(1,10,specification, "ConfiguredCount", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count >= 3);
        Assert.All(result.Data, status => Assert.Contains("Operational", status.ProblemState));
    }

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var statuses = _businessServiceHealthStatusFixture.CreateMultipleBusinessServiceHealthStatusWithSameProblemState("Operational", 2);
        foreach (var status in statuses)
        {
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new TestBusinessServiceHealthStatusSpecification("NonExistent");

        // Act
        var result = await _repository.PaginatedListAllAsync(1,10,specification, "ConfiguredCount", "Asc");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
    }

    #endregion

    #region Business Logic Tests

    [Fact]
    public async Task AddAsync_ShouldSetCorrectCounts_WhenCreatingBusinessServiceHealthStatus()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithSpecificCounts(120, 100, 20);

        // Act
        var result = await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(120, result.ConfiguredCount);
        Assert.Equal(100, result.DRReadyCount);
        Assert.Equal(20, result.DRNotReadyCount);
        Assert.True(result.DRReadyCount + result.DRNotReadyCount <= result.ConfiguredCount);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnStatusesOrderedByCreatedDate()
    {
        // Arrange
        var statuses = new List<BusinessServiceHealthStatus>();
        for (int i = 0; i < 3; i++)
        {
            var status = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties();
            status.CreatedDate = DateTime.UtcNow.AddDays(-i);
            statuses.Add(status);
            await _repository.AddAsync(status);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 3);
        
        var orderedStatuses = result.Where(x => statuses.Any(s => s.ReferenceId == x.ReferenceId))
                                   .OrderByDescending(x => x.CreatedDate)
                                   .ToList();
        
        for (int i = 0; i < orderedStatuses.Count - 1; i++)
        {
            Assert.True(orderedStatuses[i].CreatedDate >= orderedStatuses[i + 1].CreatedDate);
        }
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task AddAsync_ShouldHandleNullProblemState()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithProperties(problemState: "");

        // Act
        var result = await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ProblemState);
    }

    [Fact]
    public async Task AddAsync_ShouldHandleZeroCounts()
    {
        // Arrange
        var businessServiceHealthStatus = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusWithSpecificCounts(0, 0, 0);

        // Act
        var result = await _repository.AddAsync(businessServiceHealthStatus);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.ConfiguredCount);
        Assert.Equal(0, result.DRReadyCount);
        Assert.Equal(0, result.DRNotReadyCount);
    }

    #endregion
}
