﻿using ContinuityPatrol.Shared.Core.Responses;
namespace ContinuityPatrol.Services.ApiTests.Impl.Configuration;
public class TeamResourceServiceTests : IClassFixture<TeamResourceServiceFixture>
{
    private readonly TeamResourceServiceFixture _fixture;

    public TeamResourceServiceTests(TeamResourceServiceFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(client => client.Post<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
        Assert.Equal(_fixture.Response, result);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Response()
    {
        _fixture.ClientMock
            .Setup(client => client.Delete<BaseResponse>(It.IsAny<RestRequest>()))
            .ReturnsAsync(_fixture.Response);

        var result = await _fixture.Service.DeleteAsync(_fixture.TeamId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.Response, result);
    }
}